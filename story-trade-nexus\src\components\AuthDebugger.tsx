import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/lib/AuthContext';
import { initializeFirebase, db } from '@/lib/firebase';
import { setupAdminsCollection, verifyAdminAccess } from '@/lib/blogCollections';
import { toast } from 'sonner';
import { Shield, User, Database, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';

interface AuthDebugResult {
  step: string;
  status: 'success' | 'error' | 'warning';
  message: string;
  data?: any;
}

const AuthDebugger: React.FC = () => {
  const { currentUser, isAdmin, checkAdminStatus } = useAuth();
  const [isDebugging, setIsDebugging] = useState(false);
  const [debugResults, setDebugResults] = useState<AuthDebugResult[]>([]);

  const addDebugResult = (step: string, status: 'success' | 'error' | 'warning', message: string, data?: any) => {
    setDebugResults(prev => [...prev, { step, status, message, data }]);
  };

  const runAuthDebug = async () => {
    setIsDebugging(true);
    setDebugResults([]);

    try {
      // Step 1: Check current user authentication
      addDebugResult('Authentication', 'success', 'Checking current user authentication...');
      
      if (!currentUser) {
        addDebugResult('Authentication', 'error', 'No user is currently authenticated');
        return;
      }

      addDebugResult('Authentication', 'success', `User authenticated: ${currentUser.email}`, {
        uid: currentUser.uid,
        email: currentUser.email,
        emailVerified: currentUser.emailVerified,
        displayName: currentUser.displayName
      });

      // Step 2: Check admin status from AuthContext
      addDebugResult('Admin Status', isAdmin ? 'success' : 'warning', `Admin status from context: ${isAdmin}`);

      // Step 3: Check Firebase token
      try {
        await initializeFirebase();
        const { getAuth } = await import('firebase/auth');
        const auth = getAuth();
        const user = auth.currentUser;
        
        if (user) {
          const token = await user.getIdToken(true); // Force refresh
          const tokenResult = await user.getIdTokenResult();
          
          addDebugResult('Firebase Token', 'success', 'Firebase token retrieved successfully', {
            email: tokenResult.claims.email,
            emailVerified: tokenResult.claims.email_verified,
            authTime: new Date(tokenResult.authTime).toLocaleString(),
            issuedAt: new Date(tokenResult.issuedAtTime).toLocaleString(),
            expirationTime: new Date(tokenResult.expirationTime).toLocaleString()
          });
        } else {
          addDebugResult('Firebase Token', 'error', 'No Firebase user found');
        }
      } catch (tokenError) {
        addDebugResult('Firebase Token', 'error', `Token error: ${tokenError instanceof Error ? tokenError.message : 'Unknown error'}`);
      }

      // Step 4: Check /admins collection
      try {
        const { doc, getDoc } = await import('firebase/firestore');
        const adminsRef = doc(db, 'admins', 'admins'); // Common pattern for admin lists
        const adminsSnap = await getDoc(adminsRef);
        
        if (adminsSnap.exists()) {
          const adminsData = adminsSnap.data();
          addDebugResult('Admins Collection', 'success', 'Admins collection found', adminsData);
          
          if (adminsData.uids && Array.isArray(adminsData.uids)) {
            const isInAdminsList = adminsData.uids.includes(currentUser.uid);
            addDebugResult('Admin UID Check', isInAdminsList ? 'success' : 'warning', 
              `User UID ${isInAdminsList ? 'found' : 'not found'} in admins list`, {
                userUID: currentUser.uid,
                adminUIDs: adminsData.uids
              });
          } else {
            addDebugResult('Admin UID Check', 'warning', 'Admins collection exists but no uids array found');
          }
        } else {
          addDebugResult('Admins Collection', 'warning', 'Admins collection does not exist');
        }
      } catch (adminsError) {
        addDebugResult('Admins Collection', 'error', `Error accessing admins collection: ${adminsError instanceof Error ? adminsError.message : 'Unknown error'}`);
      }

      // Step 5: Test direct collection access
      try {
        const { collection, getDocs, query, limit } = await import('firebase/firestore');
        
        // Test blogs collection
        const blogsRef = collection(db, 'blogs');
        const blogsQuery = query(blogsRef, limit(1));
        await getDocs(blogsQuery);
        addDebugResult('Blogs Access', 'success', 'Successfully accessed blogs collection');
      } catch (blogsError) {
        addDebugResult('Blogs Access', 'error', `Cannot access blogs collection: ${blogsError instanceof Error ? blogsError.message : 'Unknown error'}`);
      }

      try {
        const { collection, getDocs, query, limit } = await import('firebase/firestore');
        
        // Test blogPosts collection
        const blogPostsRef = collection(db, 'blogPosts');
        const blogPostsQuery = query(blogPostsRef, limit(1));
        await getDocs(blogPostsQuery);
        addDebugResult('BlogPosts Access', 'success', 'Successfully accessed blogPosts collection');
      } catch (blogPostsError) {
        addDebugResult('BlogPosts Access', 'error', `Cannot access blogPosts collection: ${blogPostsError instanceof Error ? blogPostsError.message : 'Unknown error'}`);
      }

      // Step 6: Test admin status refresh
      try {
        const refreshedAdminStatus = await checkAdminStatus();
        addDebugResult('Admin Refresh', 'success', `Admin status after refresh: ${refreshedAdminStatus}`);
      } catch (refreshError) {
        addDebugResult('Admin Refresh', 'error', `Error refreshing admin status: ${refreshError instanceof Error ? refreshError.message : 'Unknown error'}`);
      }

    } catch (error) {
      addDebugResult('Debug Error', 'error', `Debug process failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsDebugging(false);
    }
  };

  const createAdminsCollection = async () => {
    if (!currentUser || !currentUser.email) {
      toast.error('No user authenticated or email missing');
      return;
    }

    try {
      const result = await setupAdminsCollection(currentUser.uid, currentUser.email);

      if (result.success) {
        toast.success('Admins collection setup successfully');
        addDebugResult('Admin Collection Setup', 'success', result.message, result.details);
      } else {
        toast.error(`Failed to setup admins collection: ${result.message}`);
        addDebugResult('Admin Collection Setup', 'error', result.message, result.details);
      }
    } catch (error) {
      toast.error(`Failed to setup admins collection: ${error instanceof Error ? error.message : 'Unknown error'}`);
      addDebugResult('Admin Collection Setup', 'error', `Failed to setup admins collection: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const verifyAdminStatus = async () => {
    if (!currentUser) {
      toast.error('No user authenticated');
      return;
    }

    try {
      const result = await verifyAdminAccess(currentUser.uid);

      if (result.success) {
        toast.success('User has admin access');
        addDebugResult('Admin Access Verification', 'success', result.message, result.details);
      } else {
        toast.warning(`Admin access verification: ${result.message}`);
        addDebugResult('Admin Access Verification', 'warning', result.message, result.details);
      }
    } catch (error) {
      toast.error(`Failed to verify admin access: ${error instanceof Error ? error.message : 'Unknown error'}`);
      addDebugResult('Admin Access Verification', 'error', `Failed to verify admin access: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const getStatusIcon = (status: 'success' | 'error' | 'warning') => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: 'success' | 'error' | 'warning') => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-700';
      case 'error':
        return 'bg-red-100 text-red-700';
      case 'warning':
        return 'bg-yellow-100 text-yellow-700';
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5 text-blue-500" />
          Authentication & Permissions Debugger
        </CardTitle>
        <CardDescription>
          Debug authentication flow and Firebase permissions for blog collections access.
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Current Status */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-2">
            <User className="h-4 w-4" />
            <span className="text-sm font-medium">User:</span>
            <span className="text-sm">{currentUser?.email || 'Not logged in'}</span>
          </div>
          <div className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            <span className="text-sm font-medium">Admin:</span>
            <Badge variant={isAdmin ? 'default' : 'destructive'}>
              {isAdmin ? 'Yes' : 'No'}
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            <span className="text-sm font-medium">UID:</span>
            <span className="text-xs font-mono">{currentUser?.uid?.substring(0, 8) || 'N/A'}...</span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-3">
          <Button 
            onClick={runAuthDebug}
            disabled={isDebugging || !currentUser}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isDebugging ? 'Debugging...' : 'Run Auth Debug'}
          </Button>

          <Button
            onClick={createAdminsCollection}
            disabled={!currentUser}
            variant="outline"
            className="border-green-300 text-green-600 hover:bg-green-50"
          >
            Setup Admins Collection
          </Button>

          <Button
            onClick={verifyAdminStatus}
            disabled={!currentUser}
            variant="outline"
            className="border-blue-300 text-blue-600 hover:bg-blue-50"
          >
            Verify Admin Access
          </Button>
        </div>

        {/* Debug Results */}
        {debugResults.length > 0 && (
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">Debug Results</h3>
            {debugResults.map((result, index) => (
              <div 
                key={index}
                className="border rounded-lg p-3"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(result.status)}
                    <span className="font-medium">{result.step}</span>
                  </div>
                  <Badge variant="outline" className={getStatusColor(result.status)}>
                    {result.status}
                  </Badge>
                </div>
                <p className="text-sm text-gray-600 mb-2">{result.message}</p>
                {result.data && (
                  <details className="text-xs">
                    <summary className="cursor-pointer text-blue-600 hover:text-blue-800">
                      View Details
                    </summary>
                    <pre className="mt-2 p-2 bg-gray-100 rounded overflow-x-auto">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Instructions */}
        <div className="p-4 bg-blue-50 rounded-lg">
          <h4 className="font-medium text-blue-800 mb-2">Troubleshooting Steps</h4>
          <ol className="text-sm text-blue-600 space-y-1">
            <li>1. <strong>Run Auth Debug:</strong> Check authentication status and permissions</li>
            <li>2. <strong>Create Admins Collection:</strong> If missing, create the /admins collection</li>
            <li>3. <strong>Check Security Rules:</strong> Ensure Firestore rules allow admin access</li>
            <li>4. <strong>Verify Email:</strong> Ensure <EMAIL> is the authenticated email</li>
            <li>5. <strong>Check Token:</strong> Verify Firebase authentication token is valid</li>
          </ol>
        </div>
      </CardContent>
    </Card>
  );
};

export default AuthDebugger;
