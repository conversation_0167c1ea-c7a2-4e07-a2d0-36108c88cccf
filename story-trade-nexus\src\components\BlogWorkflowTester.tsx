import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/lib/AuthContext';
import { 
  testFirebaseConnection, 
  createBlog, 
  publishBlogPost, 
  getAllBlogs, 
  getAllPublishedBlogPosts,
  autosaveBlogDraft 
} from '@/lib/blogService';
import { toast } from 'sonner';
import { Play, CheckCircle, XCircle, Clock, AlertTriangle } from 'lucide-react';

interface TestResult {
  name: string;
  status: 'pending' | 'running' | 'success' | 'error';
  message?: string;
  data?: any;
}

const BlogWorkflowTester: React.FC = () => {
  const { currentUser, isAdmin } = useAuth();
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);

  const updateTestResult = (name: string, status: TestResult['status'], message?: string, data?: any) => {
    setTestResults(prev => {
      const existing = prev.find(r => r.name === name);
      if (existing) {
        existing.status = status;
        existing.message = message;
        existing.data = data;
        return [...prev];
      } else {
        return [...prev, { name, status, message, data }];
      }
    });
  };

  const runWorkflowTests = async () => {
    if (!currentUser || !isAdmin) {
      toast.error('You must be logged in as an admin to run these tests');
      return;
    }

    setIsRunning(true);
    setTestResults([]);

    const tests = [
      'Firebase Connection',
      'Create Draft Blog',
      'Autosave Draft',
      'Publish Blog Post',
      'Fetch Blogs',
      'Fetch Published Posts'
    ];

    // Initialize all tests as pending
    tests.forEach(test => updateTestResult(test, 'pending'));

    try {
      // Test 1: Firebase Connection
      updateTestResult('Firebase Connection', 'running');
      const connectionResult = await testFirebaseConnection();
      if (connectionResult.success) {
        updateTestResult('Firebase Connection', 'success', 'All Firebase services accessible');
      } else {
        updateTestResult('Firebase Connection', 'error', connectionResult.message);
        return; // Stop if connection fails
      }

      // Test 2: Create Draft Blog
      updateTestResult('Create Draft Blog', 'running');
      const draftData = {
        title: `Test Draft Blog ${new Date().toISOString()}`,
        content: 'This is a test draft blog post created by the workflow tester. It contains enough content to pass validation requirements.',
        tags: ['test', 'workflow', 'draft'],
        published: false,
        authorId: currentUser.uid,
        authorName: currentUser.displayName || currentUser.email?.split('@')[0] || 'Test User',
        authorEmail: currentUser.email || '<EMAIL>',
        excerpt: 'Test draft blog excerpt for workflow validation'
      };

      const draftId = await createBlog(draftData);
      updateTestResult('Create Draft Blog', 'success', `Draft created with ID: ${draftId}`, { id: draftId });

      // Test 3: Autosave Draft
      updateTestResult('Autosave Draft', 'running');
      const autosaveData = {
        title: `${draftData.title} - Updated`,
        content: `${draftData.content} - This content has been updated via autosave.`,
        tags: [...draftData.tags, 'autosaved'],
        excerpt: 'Updated excerpt via autosave'
      };

      await autosaveBlogDraft(draftId, autosaveData);
      updateTestResult('Autosave Draft', 'success', 'Draft autosaved successfully');

      // Test 4: Publish Blog Post
      updateTestResult('Publish Blog Post', 'running');
      const publishData = {
        title: `Test Published Blog ${new Date().toISOString()}`,
        content: 'This is a test published blog post created by the workflow tester. It demonstrates the complete publishing workflow from the admin panel.',
        excerpt: 'Test published blog excerpt for workflow validation',
        author: currentUser.displayName || currentUser.email?.split('@')[0] || 'Test User',
        tags: ['test', 'workflow', 'published'],
        coverImageUrl: null
      };

      const publishedId = await publishBlogPost(publishData);
      updateTestResult('Publish Blog Post', 'success', `Blog published with ID: ${publishedId}`, { id: publishedId });

      // Test 5: Fetch Blogs (drafts)
      updateTestResult('Fetch Blogs', 'running');
      const blogs = await getAllBlogs();
      updateTestResult('Fetch Blogs', 'success', `Retrieved ${blogs.length} blogs from drafts collection`);

      // Test 6: Fetch Published Posts
      updateTestResult('Fetch Published Posts', 'running');
      const publishedPosts = await getAllPublishedBlogPosts();
      updateTestResult('Fetch Published Posts', 'success', `Retrieved ${publishedPosts.length} published posts`);

      toast.success('All workflow tests completed successfully!');

    } catch (error) {
      console.error('Workflow test failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      // Update the currently running test as failed
      const runningTest = testResults.find(r => r.status === 'running');
      if (runningTest) {
        updateTestResult(runningTest.name, 'error', errorMessage);
      }
      
      toast.error(`Workflow test failed: ${errorMessage}`);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-gray-400" />;
      case 'running':
        return <Clock className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return 'bg-gray-100 text-gray-600';
      case 'running':
        return 'bg-blue-100 text-blue-600';
      case 'success':
        return 'bg-green-100 text-green-600';
      case 'error':
        return 'bg-red-100 text-red-600';
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-orange-500" />
          Blog Workflow Tester
        </CardTitle>
        <CardDescription>
          Test the complete blog management workflow including draft creation, autosave, and publishing.
          This will create test data in your Firebase collections.
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* User Status */}
        <div className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
          <div>
            <span className="text-sm font-medium">User:</span>
            <span className="ml-2 text-sm">{currentUser?.email || 'Not logged in'}</span>
          </div>
          <div>
            <span className="text-sm font-medium">Admin:</span>
            <Badge variant={isAdmin ? 'default' : 'destructive'} className="ml-2">
              {isAdmin ? 'Yes' : 'No'}
            </Badge>
          </div>
        </div>

        {/* Test Button */}
        <Button 
          onClick={runWorkflowTests}
          disabled={isRunning || !isAdmin}
          className="w-full"
        >
          <Play className="h-4 w-4 mr-2" />
          {isRunning ? 'Running Tests...' : 'Run Workflow Tests'}
        </Button>

        {/* Test Results */}
        {testResults.length > 0 && (
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Test Results</h3>
            {testResults.map((result, index) => (
              <div 
                key={index}
                className="flex items-center justify-between p-3 border rounded-lg"
              >
                <div className="flex items-center gap-3">
                  {getStatusIcon(result.status)}
                  <span className="font-medium">{result.name}</span>
                </div>
                <div className="flex items-center gap-2">
                  {result.message && (
                    <span className="text-sm text-gray-600">{result.message}</span>
                  )}
                  <Badge variant="outline" className={getStatusColor(result.status)}>
                    {result.status}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Summary */}
        {testResults.length > 0 && !isRunning && (
          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-800">Test Summary</h4>
            <div className="text-sm text-blue-600 mt-1">
              Passed: {testResults.filter(r => r.status === 'success').length} | 
              Failed: {testResults.filter(r => r.status === 'error').length} | 
              Total: {testResults.length}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default BlogWorkflowTester;
