const l=e=>new Promise((o,n)=>{if(!navigator.geolocation){n(new Error("Geolocation is not supported by your browser"));return}const a={...{enableHighAccuracy:!0,timeout:2e4,maximumAge:0},...e};console.log("Getting position with options:",a),navigator.geolocation.getCurrentPosition(t=>{console.log("Position obtained successfully"),o({latitude:t.coords.latitude,longitude:t.coords.longitude})},t=>{let c="Unknown error occurred while getting location",s="UNKNOWN";switch(t.code){case t.PERMISSION_DENIED:c="Location permission denied. Please enable location services in your browser settings.",s="PERMISSION_DENIED";break;case t.POSITION_UNAVAILABLE:c="Location information is unavailable. Please try again in a different area.",s="POSITION_UNAVAILABLE";break;case t.TIMEOUT:c="The request to get your location timed out. Please check your internet connection and try again.",s="TIMEOUT";break}console.error(`Geolocation error (${s}):`,c);const i=new Error(c);i.code=s,i.originalError=t,n(i)},a)}),u=async e=>{try{console.log("reverseGeocode: Starting reverse geocoding for coordinates:",e);const o=`https://nominatim.openstreetmap.org/reverse?format=json&lat=${e.latitude}&lon=${e.longitude}&addressdetails=1`;console.log("reverseGeocode: Fetching from URL:",o);const n=await fetch(o,{headers:{"Accept-Language":"en-US,en;q=0.9","User-Agent":"BookSwap Application (https://bookswap.example.com)"}});if(console.log("reverseGeocode: Response status:",n.status),!n.ok)throw console.error("reverseGeocode: Response not OK:",n.status,n.statusText),new Error(`Failed to fetch location data: ${n.status} ${n.statusText}`);const r=await n.json();console.log("reverseGeocode: Received data:",r);const a=r.address||{};console.log("reverseGeocode: Extracted address:",a);const t={state:a.state||"",city:a.city||a.town||a.village||"",pincode:a.postcode||"",fullAddress:r.display_name||""};return console.log("reverseGeocode: Returning location data:",t),t}catch(o){throw console.error("Error in reverse geocoding:",o),o}},d=e=>{const o=e.latitude.toFixed(6),n=e.longitude.toFixed(6);return{fullAddress:`Coordinates: ${o}, ${n}`,coordinates:e}},g=(e,o)=>{const r=(o.latitude-e.latitude)*Math.PI/180,a=(o.longitude-e.longitude)*Math.PI/180,t=Math.sin(r/2)*Math.sin(r/2)+Math.cos(e.latitude*Math.PI/180)*Math.cos(o.latitude*Math.PI/180)*Math.sin(a/2)*Math.sin(a/2),s=6371*(2*Math.atan2(Math.sqrt(t),Math.sqrt(1-t)));return Math.round(s*10)/10},h=async()=>{try{console.log("Getting user location silently...");try{const e=await l({enableHighAccuracy:!0,timeout:1e4});return console.log("Got high-accuracy user location:",e),e}catch(e){if(console.log("High accuracy location failed, trying with lower accuracy..."),(e==null?void 0:e.code)!=="PERMISSION_DENIED"){const o=await l({enableHighAccuracy:!1,timeout:15e3,maximumAge:6e4});return console.log("Got low-accuracy user location:",o),o}return console.log("Location permission denied"),null}}catch(e){return console.error("Error getting user location silently:",e),null}};export{g as calculateDistance,d as getBasicLocationInfo,l as getCurrentPosition,h as getUserLocationSilently,u as reverseGeocode};
