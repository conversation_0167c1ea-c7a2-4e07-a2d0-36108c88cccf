# Installation
> `npm install --save @types/dompurify`

# Summary
This package contains type definitions for dompurify (https://github.com/cure53/DOMPurify).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/dompurify.

### Additional Details
 * Last updated: Mon, 06 Nov 2023 22:41:05 GMT
 * Dependencies: [@types/trusted-types](https://npmjs.com/package/@types/trusted-types)

# Credits
These definitions were written by [<PERSON> https://github.com/davetayls
//                 <PERSON><PERSON>](https://github.com/bazuzi), [FlowCrypt](https://github.com/FlowCrypt), [Exigerr](https://github.com/Exigerr), [<PERSON><PERSON><PERSON>](https://github.com/peter<PERSON><PERSON><PERSON><PERSON>), and [<PERSON>](https://github.com/<PERSON>).
