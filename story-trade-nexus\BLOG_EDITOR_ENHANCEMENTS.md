# Blog Editor Enhancements

## Overview
The BlogEditor component has been enhanced with autosave drafts and live preview features to improve the blog writing experience for administrators.

## New Features

### 1. Autosave Drafts
- **Automatic saving**: Form content is automatically saved to Firestore every 10 seconds
- **Draft-only**: Autosave only works for unpublished blog posts (drafts)
- **Debounced approach**: Uses a 10-second debounce to prevent excessive API calls
- **Visual feedback**: Shows autosave status with timestamps and loading indicators
- **Error handling**: Gracefully handles autosave failures with user feedback

#### Implementation Details
- Uses `useEffect` with `form.watch()` to monitor form changes
- Implements debouncing with `setTimeout` and cleanup
- Only autosaves for existing blogs in edit mode
- Preserves draft status during autosave operations

### 2. Live Preview
- **Side-by-side view**: Toggle-able preview pane showing real-time blog appearance
- **Real-time updates**: Preview updates as users type (with debouncing)
- **Accurate rendering**: Uses the same styling as the actual blog display
- **Complete preview**: Shows title, cover image, excerpt, tags, content, and publication status
- **Responsive design**: Adjusts dialog width when preview is enabled

#### Preview Features
- Cover image display with proper sizing
- Rich text content rendering (HTML from ReactQuill)
- Tag display with consistent styling
- Publication status indicator
- Proper typography and spacing

### 3. Enhanced UI/UX
- **Toggle button**: Easy show/hide preview functionality
- **Status indicators**: Clear autosave feedback with icons and timestamps
- **Responsive layout**: Adapts to preview mode with proper spacing
- **Color scheme**: Maintains burgundy/navy/beige theme consistency
- **Loading states**: Proper loading indicators for all async operations

## Technical Implementation

### Files Modified
1. `src/components/BlogEditor.tsx` - Main component with new features
2. `src/lib/blogService.ts` - Added `autosaveBlogDraft` function

### New Dependencies
- Added `useRef` for timeout management
- Enhanced state management for preview and autosave
- Improved TypeScript types for better type safety

### Key Functions
- `performAutosave()` - Handles automatic draft saving
- `BlogPreview` - Renders live preview of blog post
- `formatAutosaveTime()` - Formats timestamp display
- `autosaveBlogDraft()` - Service function for draft autosaving

## Usage

### For Administrators
1. **Creating blogs**: Use "Create New Post" button in Admin Blog Manager
2. **Editing blogs**: Click "Edit" on any existing blog post
3. **Preview toggle**: Click "Show Preview" to see live preview
4. **Autosave**: Automatic for drafts - no action needed
5. **Manual save**: Use "Save as Draft" or "Save & Publish" buttons

### Autosave Behavior
- Only works in edit mode for existing blog posts
- Only saves drafts (not published posts)
- Triggers every 10 seconds after content changes
- Shows status: "Saving...", "Saved at [time]", or error messages
- Preserves all form fields: title, content, tags, excerpt, cover image

### Preview Features
- Toggle with "Show Preview" / "Hide Preview" button
- Real-time updates as you type
- Shows exactly how the blog will appear to readers
- Includes all blog elements: images, formatting, tags, etc.

## Performance Considerations
- Debounced autosave (10 seconds) prevents excessive API calls
- Preview updates are optimized to prevent performance issues
- Proper cleanup of timeouts and event listeners
- Efficient re-rendering with React hooks

## Error Handling
- Network connectivity issues handled gracefully
- Autosave failures show user-friendly error messages
- Image upload errors with proper feedback
- Form validation maintained throughout

## Future Enhancements
- Offline support for autosave
- Version history for drafts
- Collaborative editing features
- Advanced preview modes (mobile, tablet)
- Markdown support in addition to rich text
