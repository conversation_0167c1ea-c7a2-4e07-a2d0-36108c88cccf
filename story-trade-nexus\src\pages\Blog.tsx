import React, { useEffect, useState, useCallback } from 'react';
import { marked } from 'marked';
import DOMPurify from 'dompurify';
import MainLayout from '@/components/layouts/MainLayout';
import { PublishedBlogPost } from '@/types';
import { getAllPublishedBlogPosts } from '@/lib/blogService';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, User, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';

// Loading skeleton component for blog posts
const BlogPostSkeleton: React.FC = () => (
  <article 
    className="bg-white rounded-lg shadow-md overflow-hidden mb-8 animate-pulse"
    role="article"
    aria-label="Loading blog post"
  >
    <Skeleton className="h-64 w-full" />
    <div className="p-6 space-y-4">
      <Skeleton className="h-8 w-3/4" />
      <div className="flex items-center space-x-4">
        <Skeleton className="h-4 w-24" />
        <Skeleton className="h-4 w-32" />
        <Skeleton className="h-4 w-20" />
      </div>
      <Skeleton className="h-4 w-full" />
      <Skeleton className="h-4 w-5/6" />
      <Skeleton className="h-4 w-4/5" />
      <div className="flex flex-wrap gap-2 mt-4">
        <Skeleton className="h-6 w-16" />
        <Skeleton className="h-6 w-20" />
        <Skeleton className="h-6 w-14" />
      </div>
    </div>
    <span className="sr-only">Loading blog post content...</span>
  </article>
);

// Error component for blog loading failures
const BlogError: React.FC<{ onRetry: () => void }> = ({ onRetry }) => (
  <div 
    className="text-center py-12"
    role="alert"
    aria-live="polite"
  >
    <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
    <h2 className="text-2xl font-bold text-navy-800 mb-2">Unable to Load Blog Posts</h2>
    <p className="text-gray-600 mb-6 max-w-md mx-auto">
      We're having trouble loading the blog posts right now. Please check your internet connection and try again.
    </p>
    <button
      onClick={onRetry}
      className="bg-burgundy-600 hover:bg-burgundy-700 text-white px-6 py-2 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-burgundy-500 focus:ring-offset-2"
      aria-label="Retry loading blog posts"
    >
      Try Again
    </button>
  </div>
);

const Blog: React.FC = () => {
  const [posts, setPosts] = useState<PublishedBlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const postsPerPage = 6; // Show 6 posts per page

  // Markdown rendering function with sanitization
  const renderMarkdown = useCallback((content: string) => {
    try {
      const rawHtml = marked(content);
      return DOMPurify.sanitize(rawHtml as string);
    } catch (error) {
      console.error('Error rendering markdown:', error);
      return '<p class="text-red-500">Error rendering content</p>';
    }
  }, []);

  // Format date for display
  const formatDate = useCallback((date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }, []);

  // Fetch blog posts
  const fetchPosts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('Fetching published blog posts...');
      
      const publishedPosts = await getAllPublishedBlogPosts();
      setPosts(publishedPosts);
      
      console.log(`Successfully loaded ${publishedPosts.length} blog posts`);
      
      if (publishedPosts.length === 0) {
        toast.info('No blog posts available yet. Check back soon!');
      }
    } catch (err) {
      console.error('Error fetching blog posts:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to load blog posts';
      setError(errorMessage);
      toast.error('Failed to load blog posts. Please try again.');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchPosts();
  }, [fetchPosts]);

  // Set page title and meta description for SEO
  useEffect(() => {
    document.title = 'Blog - PeerBooks | Book Sharing Community Stories';

    // Add or update meta description
    let metaDescription = document.querySelector('meta[name="description"]');
    if (!metaDescription) {
      metaDescription = document.createElement('meta');
      metaDescription.setAttribute('name', 'description');
      document.head.appendChild(metaDescription);
    }
    metaDescription.setAttribute('content', 'Discover stories, insights, and updates from our book-sharing community. Read about book recommendations, sharing experiences, and community highlights on PeerBooks.');

    // Cleanup function to reset title when component unmounts
    return () => {
      document.title = 'PeerBooks - Share Books, Build Community';
    };
  }, []);

  // Calculate pagination
  const totalPages = Math.ceil(posts.length / postsPerPage);
  const startIndex = (currentPage - 1) * postsPerPage;
  const endIndex = startIndex + postsPerPage;
  const currentPosts = posts.slice(startIndex, endIndex);

  // Pagination handlers
  const goToPage = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const goToPrevious = () => {
    if (currentPage > 1) {
      goToPage(currentPage - 1);
    }
  };

  const goToNext = () => {
    if (currentPage < totalPages) {
      goToPage(currentPage + 1);
    }
  };

  return (
    <MainLayout>
      <div className="min-h-screen bg-beige-50">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-burgundy-600 to-burgundy-700 text-white py-16">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-4xl md:text-5xl font-playfair font-bold mb-4">
              PeerBooks Blog
            </h1>
            <p className="text-xl md:text-2xl text-burgundy-100 max-w-2xl mx-auto">
              Discover stories, insights, and updates from our book-sharing community
            </p>
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          <div className="max-w-4xl mx-auto">
            {loading && (
              <div 
                className="space-y-8"
                role="status"
                aria-label="Loading blog posts"
              >
                {[...Array(3)].map((_, index) => (
                  <BlogPostSkeleton key={index} />
                ))}
              </div>
            )}

            {error && !loading && (
              <BlogError onRetry={fetchPosts} />
            )}

            {!loading && !error && posts.length === 0 && (
              <div 
                className="text-center py-12"
                role="status"
                aria-live="polite"
              >
                <div className="text-6xl mb-4" aria-hidden="true">📚</div>
                <h2 className="text-2xl font-bold text-navy-800 mb-2">No Blog Posts Yet</h2>
                <p className="text-gray-600 max-w-md mx-auto">
                  We're working on creating amazing content for you. Check back soon for the latest updates and stories from our book-sharing community!
                </p>
              </div>
            )}

            {!loading && !error && posts.length > 0 && (
              <div className="space-y-8">
                {currentPosts.map((post: PublishedBlogPost) => (
                  <article
                    key={post.id}
                    className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300"
                  >
                    {/* Cover Image */}
                    {post.coverImageUrl && (
                      <div className="relative h-64 overflow-hidden">
                        <img
                          src={post.coverImageUrl}
                          alt={post.title}
                          className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                          loading="lazy"
                        />
                      </div>
                    )}

                    {/* Content */}
                    <div className="p-6">
                      {/* Title */}
                      <h2 className="text-2xl md:text-3xl font-playfair font-bold text-navy-800 mb-4 hover:text-burgundy-600 transition-colors duration-200">
                        {post.title}
                      </h2>

                      {/* Metadata */}
                      <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-4">
                        <div className="flex items-center">
                          <User className="h-4 w-4 mr-1" />
                          <span>By {post.author}</span>
                        </div>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          <span>{formatDate(post.createdAt)}</span>
                        </div>
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-1" />
                          <span>{post.readTime} min read</span>
                        </div>
                      </div>

                      {/* Excerpt */}
                      {post.excerpt && (
                        <p className="text-gray-700 text-lg leading-relaxed mb-4">
                          {post.excerpt}
                        </p>
                      )}

                      {/* Content */}
                      <div
                        className="prose prose-lg max-w-none prose-headings:text-navy-800 prose-a:text-burgundy-600 prose-strong:text-navy-700 prose-blockquote:border-burgundy-300 prose-blockquote:text-gray-700"
                        dangerouslySetInnerHTML={{
                          __html: renderMarkdown(post.content)
                        }}
                      />

                      {/* Tags */}
                      {post.tags && post.tags.length > 0 && (
                        <div className="flex flex-wrap gap-2 mt-6 pt-4 border-t border-gray-200">
                          {post.tags.map((tag: string, index: number) => (
                            <Badge
                              key={index}
                              variant="secondary"
                              className="bg-beige-100 text-navy-700 hover:bg-beige-200 transition-colors duration-200"
                            >
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  </article>
                ))}

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex justify-center items-center space-x-2 mt-12">
                    <button
                      onClick={goToPrevious}
                      disabled={currentPage === 1}
                      className="px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      aria-label="Previous page"
                    >
                      Previous
                    </button>

                    <div className="flex space-x-1">
                      {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                        <button
                          key={page}
                          onClick={() => goToPage(page)}
                          className={`px-3 py-2 text-sm font-medium rounded-md ${
                            currentPage === page
                              ? 'bg-burgundy-600 text-white'
                              : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50'
                          }`}
                          aria-label={`Go to page ${page}`}
                          aria-current={currentPage === page ? 'page' : undefined}
                        >
                          {page}
                        </button>
                      ))}
                    </div>

                    <button
                      onClick={goToNext}
                      disabled={currentPage === totalPages}
                      className="px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      aria-label="Next page"
                    >
                      Next
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default Blog;
