{"version": 3, "file": "msWord.js", "names": ["ignoreRegexp", "idRegexp", "indentRegexp", "parseListItem", "element", "html", "style", "getAttribute", "idMatch", "match", "id", "Number", "indentMatch", "indent", "typeRegexp", "RegExp", "typeMatch", "type", "normalizeListItem", "doc", "msoList", "Array", "from", "querySelectorAll", "ignored", "others", "for<PERSON>ach", "node", "shouldIgnore", "push", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "documentElement", "innerHTML", "listItems", "map", "filter", "parsed", "length", "childListItems", "current", "shift", "nextElement<PERSON><PERSON>ling", "ul", "document", "createElement", "listItem", "li", "setAttribute", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "slice", "_ref", "e", "normalize"], "sources": ["../../../../src/modules/normalizeExternalHTML/normalizers/msWord.ts"], "sourcesContent": ["const ignoreRegexp = /\\bmso-list:[^;]*ignore/i;\nconst idRegexp = /\\bmso-list:[^;]*\\bl(\\d+)/i;\nconst indentRegexp = /\\bmso-list:[^;]*\\blevel(\\d+)/i;\n\nconst parseListItem = (element: Element, html: string) => {\n  const style = element.getAttribute('style');\n  const idMatch = style?.match(idRegexp);\n  if (!idMatch) {\n    return null;\n  }\n  const id = Number(idMatch[1]);\n\n  const indentMatch = style?.match(indentRegexp);\n  const indent = indentMatch ? Number(indentMatch[1]) : 1;\n\n  const typeRegexp = new RegExp(\n    `@list l${id}:level${indent}\\\\s*\\\\{[^\\\\}]*mso-level-number-format:\\\\s*([\\\\w-]+)`,\n    'i',\n  );\n  const typeMatch = html.match(typeRegexp);\n  const type = typeMatch && typeMatch[1] === 'bullet' ? 'bullet' : 'ordered';\n\n  return { id, indent, type, element };\n};\n\n// list items are represented as `p` tags with styles like `mso-list: l0 level1` where:\n// 1. \"0\" in \"l0\" means the list item id;\n// 2. \"1\" in \"level1\" means the indent level, starting from 1.\nconst normalizeListItem = (doc: Document) => {\n  const msoList = Array.from(doc.querySelectorAll('[style*=mso-list]'));\n  const ignored: Element[] = [];\n  const others: Element[] = [];\n  msoList.forEach((node) => {\n    const shouldIgnore = (node.getAttribute('style') || '').match(ignoreRegexp);\n    if (shouldIgnore) {\n      ignored.push(node);\n    } else {\n      others.push(node);\n    }\n  });\n\n  // Each list item contains a marker wrapped with \"mso-list: Ignore\".\n  ignored.forEach((node) => node.parentNode?.removeChild(node));\n\n  // The list stype is not defined inline with the tag, instead, it's in the\n  // style tag so we need to pass the html as a string.\n  const html = doc.documentElement.innerHTML;\n  const listItems = others\n    .map((element) => parseListItem(element, html))\n    .filter((parsed) => parsed);\n\n  while (listItems.length) {\n    const childListItems = [];\n\n    let current = listItems.shift();\n    // Group continuous items into the same group (aka \"ul\")\n    while (current) {\n      childListItems.push(current);\n      current =\n        listItems.length &&\n        listItems[0]?.element === current.element.nextElementSibling &&\n        // Different id means the next item doesn't belong to this group.\n        listItems[0].id === current.id\n          ? listItems.shift()\n          : null;\n    }\n\n    const ul = document.createElement('ul');\n    childListItems.forEach((listItem) => {\n      const li = document.createElement('li');\n      li.setAttribute('data-list', listItem.type);\n      if (listItem.indent > 1) {\n        li.setAttribute('class', `ql-indent-${listItem.indent - 1}`);\n      }\n      li.innerHTML = listItem.element.innerHTML;\n      ul.appendChild(li);\n    });\n\n    const element = childListItems[0]?.element;\n    const { parentNode } = element ?? {};\n    if (element) {\n      parentNode?.replaceChild(ul, element);\n    }\n    childListItems.slice(1).forEach(({ element: e }) => {\n      parentNode?.removeChild(e);\n    });\n  }\n};\n\nexport default function normalize(doc: Document) {\n  if (\n    doc.documentElement.getAttribute('xmlns:w') ===\n    'urn:schemas-microsoft-com:office:word'\n  ) {\n    normalizeListItem(doc);\n  }\n}\n"], "mappings": "AAAA,MAAMA,YAAY,GAAG,yBAAyB;AAC9C,MAAMC,QAAQ,GAAG,2BAA2B;AAC5C,MAAMC,YAAY,GAAG,+BAA+B;AAEpD,MAAMC,aAAa,GAAGA,CAACC,OAAgB,EAAEC,IAAY,KAAK;EACxD,MAAMC,KAAK,GAAGF,OAAO,CAACG,YAAY,CAAC,OAAO,CAAC;EAC3C,MAAMC,OAAO,GAAGF,KAAK,EAAEG,KAAK,CAACR,QAAQ,CAAC;EACtC,IAAI,CAACO,OAAO,EAAE;IACZ,OAAO,IAAI;EACb;EACA,MAAME,EAAE,GAAGC,MAAM,CAACH,OAAO,CAAC,CAAC,CAAC,CAAC;EAE7B,MAAMI,WAAW,GAAGN,KAAK,EAAEG,KAAK,CAACP,YAAY,CAAC;EAC9C,MAAMW,MAAM,GAAGD,WAAW,GAAGD,MAAM,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;EAEvD,MAAME,UAAU,GAAG,IAAIC,MAAM,CAC1B,UAASL,EAAG,SAAQG,MAAO,qDAAoD,EAChF,GACF,CAAC;EACD,MAAMG,SAAS,GAAGX,IAAI,CAACI,KAAK,CAACK,UAAU,CAAC;EACxC,MAAMG,IAAI,GAAGD,SAAS,IAAIA,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,GAAG,QAAQ,GAAG,SAAS;EAE1E,OAAO;IAAEN,EAAE;IAAEG,MAAM;IAAEI,IAAI;IAAEb;EAAQ,CAAC;AACtC,CAAC;;AAED;AACA;AACA;AACA,MAAMc,iBAAiB,GAAIC,GAAa,IAAK;EAC3C,MAAMC,OAAO,GAAGC,KAAK,CAACC,IAAI,CAACH,GAAG,CAACI,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;EACrE,MAAMC,OAAkB,GAAG,EAAE;EAC7B,MAAMC,MAAiB,GAAG,EAAE;EAC5BL,OAAO,CAACM,OAAO,CAAEC,IAAI,IAAK;IACxB,MAAMC,YAAY,GAAG,CAACD,IAAI,CAACpB,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,EAAEE,KAAK,CAACT,YAAY,CAAC;IAC3E,IAAI4B,YAAY,EAAE;MAChBJ,OAAO,CAACK,IAAI,CAACF,IAAI,CAAC;IACpB,CAAC,MAAM;MACLF,MAAM,CAACI,IAAI,CAACF,IAAI,CAAC;IACnB;EACF,CAAC,CAAC;;EAEF;EACAH,OAAO,CAACE,OAAO,CAAEC,IAAI,IAAKA,IAAI,CAACG,UAAU,EAAEC,WAAW,CAACJ,IAAI,CAAC,CAAC;;EAE7D;EACA;EACA,MAAMtB,IAAI,GAAGc,GAAG,CAACa,eAAe,CAACC,SAAS;EAC1C,MAAMC,SAAS,GAAGT,MAAM,CACrBU,GAAG,CAAE/B,OAAO,IAAKD,aAAa,CAACC,OAAO,EAAEC,IAAI,CAAC,CAAC,CAC9C+B,MAAM,CAAEC,MAAM,IAAKA,MAAM,CAAC;EAE7B,OAAOH,SAAS,CAACI,MAAM,EAAE;IACvB,MAAMC,cAAc,GAAG,EAAE;IAEzB,IAAIC,OAAO,GAAGN,SAAS,CAACO,KAAK,CAAC,CAAC;IAC/B;IACA,OAAOD,OAAO,EAAE;MACdD,cAAc,CAACV,IAAI,CAACW,OAAO,CAAC;MAC5BA,OAAO,GACLN,SAAS,CAACI,MAAM,IAChBJ,SAAS,CAAC,CAAC,CAAC,EAAE9B,OAAO,KAAKoC,OAAO,CAACpC,OAAO,CAACsC,kBAAkB;MAC5D;MACAR,SAAS,CAAC,CAAC,CAAC,CAACxB,EAAE,KAAK8B,OAAO,CAAC9B,EAAE,GAC1BwB,SAAS,CAACO,KAAK,CAAC,CAAC,GACjB,IAAI;IACZ;IAEA,MAAME,EAAE,GAAGC,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;IACvCN,cAAc,CAACb,OAAO,CAAEoB,QAAQ,IAAK;MACnC,MAAMC,EAAE,GAAGH,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;MACvCE,EAAE,CAACC,YAAY,CAAC,WAAW,EAAEF,QAAQ,CAAC7B,IAAI,CAAC;MAC3C,IAAI6B,QAAQ,CAACjC,MAAM,GAAG,CAAC,EAAE;QACvBkC,EAAE,CAACC,YAAY,CAAC,OAAO,EAAG,aAAYF,QAAQ,CAACjC,MAAM,GAAG,CAAE,EAAC,CAAC;MAC9D;MACAkC,EAAE,CAACd,SAAS,GAAGa,QAAQ,CAAC1C,OAAO,CAAC6B,SAAS;MACzCU,EAAE,CAACM,WAAW,CAACF,EAAE,CAAC;IACpB,CAAC,CAAC;IAEF,MAAM3C,OAAO,GAAGmC,cAAc,CAAC,CAAC,CAAC,EAAEnC,OAAO;IAC1C,MAAM;MAAE0B;IAAW,CAAC,GAAG1B,OAAO,IAAI,CAAC,CAAC;IACpC,IAAIA,OAAO,EAAE;MACX0B,UAAU,EAAEoB,YAAY,CAACP,EAAE,EAAEvC,OAAO,CAAC;IACvC;IACAmC,cAAc,CAACY,KAAK,CAAC,CAAC,CAAC,CAACzB,OAAO,CAAC0B,IAAA,IAAoB;MAAA,IAAnB;QAAEhD,OAAO,EAAEiD;MAAE,CAAC,GAAAD,IAAA;MAC7CtB,UAAU,EAAEC,WAAW,CAACsB,CAAC,CAAC;IAC5B,CAAC,CAAC;EACJ;AACF,CAAC;AAED,eAAe,SAASC,SAASA,CAACnC,GAAa,EAAE;EAC/C,IACEA,GAAG,CAACa,eAAe,CAACzB,YAAY,CAAC,SAAS,CAAC,KAC3C,uCAAuC,EACvC;IACAW,iBAAiB,CAACC,GAAG,CAAC;EACxB;AACF", "ignoreList": []}