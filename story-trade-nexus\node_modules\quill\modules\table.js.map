{"version": 3, "file": "table.js", "names": ["Delta", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "TableCell", "TableRow", "TableBody", "TableContainer", "tableId", "Table", "register", "constructor", "arguments", "listenBalanceCells", "balanceTables", "quill", "scroll", "descendants", "for<PERSON>ach", "table", "balanceCells", "deleteColumn", "cell", "getTable", "cellOffset", "update", "sources", "USER", "deleteRow", "row", "remove", "deleteTable", "offset", "setSelection", "SILENT", "range", "length", "undefined", "getSelection", "getLine", "index", "statics", "blotName", "parent", "insertColumn", "column", "shift", "rowOffset", "insertColumnLeft", "insertColumnRight", "insertRow", "children", "insertRowAbove", "insertRowBelow", "insertTable", "rows", "columns", "delta", "Array", "fill", "reduce", "memo", "text", "join", "insert", "retain", "updateContents", "on", "events", "SCROLL_OPTIMIZE", "mutations", "some", "mutation", "includes", "target", "tagName", "once", "TEXT_CHANGE", "old", "source"], "sources": ["../../src/modules/table.ts"], "sourcesContent": ["import Delta from 'quill-delta';\nimport Quill from '../core/quill.js';\nimport Module from '../core/module.js';\nimport {\n  TableCell,\n  TableRow,\n  TableBody,\n  TableContainer,\n  tableId,\n} from '../formats/table.js';\n\nclass Table extends Module {\n  static register() {\n    Quill.register(TableCell);\n    Quill.register(TableRow);\n    Quill.register(TableBody);\n    Quill.register(TableContainer);\n  }\n\n  constructor(...args: ConstructorParameters<typeof Module>) {\n    super(...args);\n    this.listenBalanceCells();\n  }\n\n  balanceTables() {\n    this.quill.scroll.descendants(TableContainer).forEach((table) => {\n      table.balanceCells();\n    });\n  }\n\n  deleteColumn() {\n    const [table, , cell] = this.getTable();\n    if (cell == null) return;\n    // @ts-expect-error\n    table.deleteColumn(cell.cellOffset());\n    this.quill.update(Quill.sources.USER);\n  }\n\n  deleteRow() {\n    const [, row] = this.getTable();\n    if (row == null) return;\n    row.remove();\n    this.quill.update(Quill.sources.USER);\n  }\n\n  deleteTable() {\n    const [table] = this.getTable();\n    if (table == null) return;\n    // @ts-expect-error\n    const offset = table.offset();\n    // @ts-expect-error\n    table.remove();\n    this.quill.update(Quill.sources.USER);\n    this.quill.setSelection(offset, Quill.sources.SILENT);\n  }\n\n  getTable(\n    range = this.quill.getSelection(),\n  ): [null, null, null, -1] | [Table, TableRow, TableCell, number] {\n    if (range == null) return [null, null, null, -1];\n    const [cell, offset] = this.quill.getLine(range.index);\n    if (cell == null || cell.statics.blotName !== TableCell.blotName) {\n      return [null, null, null, -1];\n    }\n    const row = cell.parent;\n    const table = row.parent.parent;\n    // @ts-expect-error\n    return [table, row, cell, offset];\n  }\n\n  insertColumn(offset: number) {\n    const range = this.quill.getSelection();\n    if (!range) return;\n    const [table, row, cell] = this.getTable(range);\n    if (cell == null) return;\n    const column = cell.cellOffset();\n    table.insertColumn(column + offset);\n    this.quill.update(Quill.sources.USER);\n    let shift = row.rowOffset();\n    if (offset === 0) {\n      shift += 1;\n    }\n    this.quill.setSelection(\n      range.index + shift,\n      range.length,\n      Quill.sources.SILENT,\n    );\n  }\n\n  insertColumnLeft() {\n    this.insertColumn(0);\n  }\n\n  insertColumnRight() {\n    this.insertColumn(1);\n  }\n\n  insertRow(offset: number) {\n    const range = this.quill.getSelection();\n    if (!range) return;\n    const [table, row, cell] = this.getTable(range);\n    if (cell == null) return;\n    const index = row.rowOffset();\n    table.insertRow(index + offset);\n    this.quill.update(Quill.sources.USER);\n    if (offset > 0) {\n      this.quill.setSelection(range, Quill.sources.SILENT);\n    } else {\n      this.quill.setSelection(\n        range.index + row.children.length,\n        range.length,\n        Quill.sources.SILENT,\n      );\n    }\n  }\n\n  insertRowAbove() {\n    this.insertRow(0);\n  }\n\n  insertRowBelow() {\n    this.insertRow(1);\n  }\n\n  insertTable(rows: number, columns: number) {\n    const range = this.quill.getSelection();\n    if (range == null) return;\n    const delta = new Array(rows).fill(0).reduce((memo) => {\n      const text = new Array(columns).fill('\\n').join('');\n      return memo.insert(text, { table: tableId() });\n    }, new Delta().retain(range.index));\n    this.quill.updateContents(delta, Quill.sources.USER);\n    this.quill.setSelection(range.index, Quill.sources.SILENT);\n    this.balanceTables();\n  }\n\n  listenBalanceCells() {\n    this.quill.on(\n      Quill.events.SCROLL_OPTIMIZE,\n      (mutations: MutationRecord[]) => {\n        mutations.some((mutation) => {\n          if (\n            ['TD', 'TR', 'TBODY', 'TABLE'].includes(\n              (mutation.target as HTMLElement).tagName,\n            )\n          ) {\n            this.quill.once(Quill.events.TEXT_CHANGE, (delta, old, source) => {\n              if (source !== Quill.sources.USER) return;\n              this.balanceTables();\n            });\n            return true;\n          }\n          return false;\n        });\n      },\n    );\n  }\n}\n\nexport default Table;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SACEC,SAAS,EACTC,QAAQ,EACRC,SAAS,EACTC,cAAc,EACdC,OAAO,QACF,qBAAqB;AAE5B,MAAMC,KAAK,SAASN,MAAM,CAAC;EACzB,OAAOO,QAAQA,CAAA,EAAG;IAChBR,KAAK,CAACQ,QAAQ,CAACN,SAAS,CAAC;IACzBF,KAAK,CAACQ,QAAQ,CAACL,QAAQ,CAAC;IACxBH,KAAK,CAACQ,QAAQ,CAACJ,SAAS,CAAC;IACzBJ,KAAK,CAACQ,QAAQ,CAACH,cAAc,CAAC;EAChC;EAEAI,WAAWA,CAAA,EAAgD;IACzD,KAAK,CAAC,GAAAC,SAAO,CAAC;IACd,IAAI,CAACC,kBAAkB,CAAC,CAAC;EAC3B;EAEAC,aAAaA,CAAA,EAAG;IACd,IAAI,CAACC,KAAK,CAACC,MAAM,CAACC,WAAW,CAACV,cAAc,CAAC,CAACW,OAAO,CAAEC,KAAK,IAAK;MAC/DA,KAAK,CAACC,YAAY,CAAC,CAAC;IACtB,CAAC,CAAC;EACJ;EAEAC,YAAYA,CAAA,EAAG;IACb,MAAM,CAACF,KAAK,GAAIG,IAAI,CAAC,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IACvC,IAAID,IAAI,IAAI,IAAI,EAAE;IAClB;IACAH,KAAK,CAACE,YAAY,CAACC,IAAI,CAACE,UAAU,CAAC,CAAC,CAAC;IACrC,IAAI,CAACT,KAAK,CAACU,MAAM,CAACvB,KAAK,CAACwB,OAAO,CAACC,IAAI,CAAC;EACvC;EAEAC,SAASA,CAAA,EAAG;IACV,MAAM,GAAGC,GAAG,CAAC,GAAG,IAAI,CAACN,QAAQ,CAAC,CAAC;IAC/B,IAAIM,GAAG,IAAI,IAAI,EAAE;IACjBA,GAAG,CAACC,MAAM,CAAC,CAAC;IACZ,IAAI,CAACf,KAAK,CAACU,MAAM,CAACvB,KAAK,CAACwB,OAAO,CAACC,IAAI,CAAC;EACvC;EAEAI,WAAWA,CAAA,EAAG;IACZ,MAAM,CAACZ,KAAK,CAAC,GAAG,IAAI,CAACI,QAAQ,CAAC,CAAC;IAC/B,IAAIJ,KAAK,IAAI,IAAI,EAAE;IACnB;IACA,MAAMa,MAAM,GAAGb,KAAK,CAACa,MAAM,CAAC,CAAC;IAC7B;IACAb,KAAK,CAACW,MAAM,CAAC,CAAC;IACd,IAAI,CAACf,KAAK,CAACU,MAAM,CAACvB,KAAK,CAACwB,OAAO,CAACC,IAAI,CAAC;IACrC,IAAI,CAACZ,KAAK,CAACkB,YAAY,CAACD,MAAM,EAAE9B,KAAK,CAACwB,OAAO,CAACQ,MAAM,CAAC;EACvD;EAEAX,QAAQA,CAAA,EAEyD;IAAA,IAD/DY,KAAK,GAAAvB,SAAA,CAAAwB,MAAA,QAAAxB,SAAA,QAAAyB,SAAA,GAAAzB,SAAA,MAAG,IAAI,CAACG,KAAK,CAACuB,YAAY,CAAC,CAAC;IAEjC,IAAIH,KAAK,IAAI,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAChD,MAAM,CAACb,IAAI,EAAEU,MAAM,CAAC,GAAG,IAAI,CAACjB,KAAK,CAACwB,OAAO,CAACJ,KAAK,CAACK,KAAK,CAAC;IACtD,IAAIlB,IAAI,IAAI,IAAI,IAAIA,IAAI,CAACmB,OAAO,CAACC,QAAQ,KAAKtC,SAAS,CAACsC,QAAQ,EAAE;MAChE,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC/B;IACA,MAAMb,GAAG,GAAGP,IAAI,CAACqB,MAAM;IACvB,MAAMxB,KAAK,GAAGU,GAAG,CAACc,MAAM,CAACA,MAAM;IAC/B;IACA,OAAO,CAACxB,KAAK,EAAEU,GAAG,EAAEP,IAAI,EAAEU,MAAM,CAAC;EACnC;EAEAY,YAAYA,CAACZ,MAAc,EAAE;IAC3B,MAAMG,KAAK,GAAG,IAAI,CAACpB,KAAK,CAACuB,YAAY,CAAC,CAAC;IACvC,IAAI,CAACH,KAAK,EAAE;IACZ,MAAM,CAAChB,KAAK,EAAEU,GAAG,EAAEP,IAAI,CAAC,GAAG,IAAI,CAACC,QAAQ,CAACY,KAAK,CAAC;IAC/C,IAAIb,IAAI,IAAI,IAAI,EAAE;IAClB,MAAMuB,MAAM,GAAGvB,IAAI,CAACE,UAAU,CAAC,CAAC;IAChCL,KAAK,CAACyB,YAAY,CAACC,MAAM,GAAGb,MAAM,CAAC;IACnC,IAAI,CAACjB,KAAK,CAACU,MAAM,CAACvB,KAAK,CAACwB,OAAO,CAACC,IAAI,CAAC;IACrC,IAAImB,KAAK,GAAGjB,GAAG,CAACkB,SAAS,CAAC,CAAC;IAC3B,IAAIf,MAAM,KAAK,CAAC,EAAE;MAChBc,KAAK,IAAI,CAAC;IACZ;IACA,IAAI,CAAC/B,KAAK,CAACkB,YAAY,CACrBE,KAAK,CAACK,KAAK,GAAGM,KAAK,EACnBX,KAAK,CAACC,MAAM,EACZlC,KAAK,CAACwB,OAAO,CAACQ,MAChB,CAAC;EACH;EAEAc,gBAAgBA,CAAA,EAAG;IACjB,IAAI,CAACJ,YAAY,CAAC,CAAC,CAAC;EACtB;EAEAK,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAACL,YAAY,CAAC,CAAC,CAAC;EACtB;EAEAM,SAASA,CAAClB,MAAc,EAAE;IACxB,MAAMG,KAAK,GAAG,IAAI,CAACpB,KAAK,CAACuB,YAAY,CAAC,CAAC;IACvC,IAAI,CAACH,KAAK,EAAE;IACZ,MAAM,CAAChB,KAAK,EAAEU,GAAG,EAAEP,IAAI,CAAC,GAAG,IAAI,CAACC,QAAQ,CAACY,KAAK,CAAC;IAC/C,IAAIb,IAAI,IAAI,IAAI,EAAE;IAClB,MAAMkB,KAAK,GAAGX,GAAG,CAACkB,SAAS,CAAC,CAAC;IAC7B5B,KAAK,CAAC+B,SAAS,CAACV,KAAK,GAAGR,MAAM,CAAC;IAC/B,IAAI,CAACjB,KAAK,CAACU,MAAM,CAACvB,KAAK,CAACwB,OAAO,CAACC,IAAI,CAAC;IACrC,IAAIK,MAAM,GAAG,CAAC,EAAE;MACd,IAAI,CAACjB,KAAK,CAACkB,YAAY,CAACE,KAAK,EAAEjC,KAAK,CAACwB,OAAO,CAACQ,MAAM,CAAC;IACtD,CAAC,MAAM;MACL,IAAI,CAACnB,KAAK,CAACkB,YAAY,CACrBE,KAAK,CAACK,KAAK,GAAGX,GAAG,CAACsB,QAAQ,CAACf,MAAM,EACjCD,KAAK,CAACC,MAAM,EACZlC,KAAK,CAACwB,OAAO,CAACQ,MAChB,CAAC;IACH;EACF;EAEAkB,cAAcA,CAAA,EAAG;IACf,IAAI,CAACF,SAAS,CAAC,CAAC,CAAC;EACnB;EAEAG,cAAcA,CAAA,EAAG;IACf,IAAI,CAACH,SAAS,CAAC,CAAC,CAAC;EACnB;EAEAI,WAAWA,CAACC,IAAY,EAAEC,OAAe,EAAE;IACzC,MAAMrB,KAAK,GAAG,IAAI,CAACpB,KAAK,CAACuB,YAAY,CAAC,CAAC;IACvC,IAAIH,KAAK,IAAI,IAAI,EAAE;IACnB,MAAMsB,KAAK,GAAG,IAAIC,KAAK,CAACH,IAAI,CAAC,CAACI,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,CAAEC,IAAI,IAAK;MACrD,MAAMC,IAAI,GAAG,IAAIJ,KAAK,CAACF,OAAO,CAAC,CAACG,IAAI,CAAC,IAAI,CAAC,CAACI,IAAI,CAAC,EAAE,CAAC;MACnD,OAAOF,IAAI,CAACG,MAAM,CAACF,IAAI,EAAE;QAAE3C,KAAK,EAAEX,OAAO,CAAC;MAAE,CAAC,CAAC;IAChD,CAAC,EAAE,IAAIP,KAAK,CAAC,CAAC,CAACgE,MAAM,CAAC9B,KAAK,CAACK,KAAK,CAAC,CAAC;IACnC,IAAI,CAACzB,KAAK,CAACmD,cAAc,CAACT,KAAK,EAAEvD,KAAK,CAACwB,OAAO,CAACC,IAAI,CAAC;IACpD,IAAI,CAACZ,KAAK,CAACkB,YAAY,CAACE,KAAK,CAACK,KAAK,EAAEtC,KAAK,CAACwB,OAAO,CAACQ,MAAM,CAAC;IAC1D,IAAI,CAACpB,aAAa,CAAC,CAAC;EACtB;EAEAD,kBAAkBA,CAAA,EAAG;IACnB,IAAI,CAACE,KAAK,CAACoD,EAAE,CACXjE,KAAK,CAACkE,MAAM,CAACC,eAAe,EAC3BC,SAA2B,IAAK;MAC/BA,SAAS,CAACC,IAAI,CAAEC,QAAQ,IAAK;QAC3B,IACE,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAACC,QAAQ,CACpCD,QAAQ,CAACE,MAAM,CAAiBC,OACnC,CAAC,EACD;UACA,IAAI,CAAC5D,KAAK,CAAC6D,IAAI,CAAC1E,KAAK,CAACkE,MAAM,CAACS,WAAW,EAAE,CAACpB,KAAK,EAAEqB,GAAG,EAAEC,MAAM,KAAK;YAChE,IAAIA,MAAM,KAAK7E,KAAK,CAACwB,OAAO,CAACC,IAAI,EAAE;YACnC,IAAI,CAACb,aAAa,CAAC,CAAC;UACtB,CAAC,CAAC;UACF,OAAO,IAAI;QACb;QACA,OAAO,KAAK;MACd,CAAC,CAAC;IACJ,CACF,CAAC;EACH;AACF;AAEA,eAAeL,KAAK", "ignoreList": []}