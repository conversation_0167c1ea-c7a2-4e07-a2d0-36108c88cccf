import{ax as K,aS as I,r as c,aT as H,aC as J,j as e,aQ as Q,aD as j,aU as W,aV as X,az as N,aI as Y,a8 as E,x as Z,A as ee,aW as m,I as p,J as A}from"./index-Bm_kDzMk.js";import{A as se}from"./AdminLayout-BhV0nOiW.js";import{S as g}from"./switch-VgXrWVxu.js";import{C as y,a as k,b as C,d as w,c as S}from"./card-C-mOyesG.js";import"./users-vJ223Oqr.js";import"./index-Cr71giPO.js";var T="Tabs",[ae,ue]=K(T,[I]),F=I(),[ie,R]=ae(T),B=c.forwardRef((i,n)=>{const{__scopeTabs:a,value:o,onValueChange:r,defaultValue:t,orientation:s="horizontal",dir:u,activationMode:x="automatic",...f}=i,d=H(u),[l,h]=J({prop:o,onChange:r,defaultProp:t});return e.jsx(ie,{scope:a,baseId:Q(),value:l,onValueChange:h,orientation:s,dir:d,activationMode:x,children:e.jsx(j.div,{dir:d,"data-orientation":s,...f,ref:n})})});B.displayName=T;var P="TabsList",M=c.forwardRef((i,n)=>{const{__scopeTabs:a,loop:o=!0,...r}=i,t=R(P,a),s=F(a);return e.jsx(W,{asChild:!0,...s,orientation:t.orientation,dir:t.dir,loop:o,children:e.jsx(j.div,{role:"tablist","aria-orientation":t.orientation,...r,ref:n})})});M.displayName=P;var U="TabsTrigger",V=c.forwardRef((i,n)=>{const{__scopeTabs:a,value:o,disabled:r=!1,...t}=i,s=R(U,a),u=F(a),x=O(s.baseId,o),f=D(s.baseId,o),d=o===s.value;return e.jsx(X,{asChild:!0,...u,focusable:!r,active:d,children:e.jsx(j.button,{type:"button",role:"tab","aria-selected":d,"aria-controls":f,"data-state":d?"active":"inactive","data-disabled":r?"":void 0,disabled:r,id:x,...t,ref:n,onMouseDown:N(i.onMouseDown,l=>{!r&&l.button===0&&l.ctrlKey===!1?s.onValueChange(o):l.preventDefault()}),onKeyDown:N(i.onKeyDown,l=>{[" ","Enter"].includes(l.key)&&s.onValueChange(o)}),onFocus:N(i.onFocus,()=>{const l=s.activationMode!=="manual";!d&&!r&&l&&s.onValueChange(o)})})})});V.displayName=U;var q="TabsContent",_=c.forwardRef((i,n)=>{const{__scopeTabs:a,value:o,forceMount:r,children:t,...s}=i,u=R(q,a),x=O(u.baseId,o),f=D(u.baseId,o),d=o===u.value,l=c.useRef(d);return c.useEffect(()=>{const h=requestAnimationFrame(()=>l.current=!1);return()=>cancelAnimationFrame(h)},[]),e.jsx(Y,{present:r||d,children:({present:h})=>e.jsx(j.div,{"data-state":d?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":x,hidden:!h,id:f,tabIndex:0,...s,ref:n,style:{...i.style,animationDuration:l.current?"0s":void 0},children:h&&t})})});_.displayName=q;function O(i,n){return`${i}-trigger-${n}`}function D(i,n){return`${i}-content-${n}`}var te=B,G=M,$=V,L=_;const ne=te,z=c.forwardRef(({className:i,...n},a)=>e.jsx(G,{ref:a,className:E("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",i),...n}));z.displayName=G.displayName;const b=c.forwardRef(({className:i,...n},a)=>e.jsx($,{ref:a,className:E("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",i),...n}));b.displayName=$.displayName;const v=c.forwardRef(({className:i,...n},a)=>e.jsx(L,{ref:a,className:E("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",i),...n}));v.displayName=L.displayName;const fe=()=>{const[i,n]=c.useState(!1);c.useState("general");const[a,o]=c.useState({general:{siteName:"Book Sharing Platform",contactEmail:"<EMAIL>",enableRegistration:!0,requireEmailVerification:!0},books:{requireApproval:!0,maxBooksPerUser:50,allowMultipleImages:!0,defaultBookAvailability:"Available"},notifications:{enableEmailNotifications:!0,notifyOnNewUser:!0,notifyOnBookSubmission:!0,adminEmailRecipients:"<EMAIL>"}}),r=async()=>{try{n(!0),await new Promise(s=>setTimeout(s,1e3)),A.success("Settings saved successfully!")}catch(s){console.error("Error saving settings:",s),A.error("Failed to save settings. Please try again.")}finally{n(!1)}},t=(s,u,x)=>{o(f=>({...f,[s]:{...f[s],[u]:x}}))};return e.jsxs(se,{title:"Admin Settings",description:"Configure admin preferences and system settings",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Admin Settings"}),e.jsx("p",{className:"text-gray-600",children:"Configure system settings and preferences"})]}),e.jsx(Z,{onClick:r,disabled:i,className:"mt-4 md:mt-0",children:i?e.jsxs(e.Fragment,{children:[e.jsx(ee,{size:"sm",className:"mr-2"}),"Saving..."]}):"Save Settings"})]}),e.jsxs(ne,{defaultValue:"general",className:"w-full",children:[e.jsxs(z,{className:"mb-6",children:[e.jsx(b,{value:"general",children:"General"}),e.jsx(b,{value:"books",children:"Books"}),e.jsx(b,{value:"notifications",children:"Notifications"})]}),e.jsx(v,{value:"general",children:e.jsxs(y,{children:[e.jsxs(k,{children:[e.jsx(C,{children:"General Settings"}),e.jsx(w,{children:"Configure general platform settings"})]}),e.jsxs(S,{className:"space-y-4",children:[e.jsxs("div",{className:"grid gap-2",children:[e.jsx(m,{htmlFor:"siteName",children:"Site Name"}),e.jsx(p,{id:"siteName",value:a.general.siteName,onChange:s=>t("general","siteName",s.target.value)})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(m,{htmlFor:"contactEmail",children:"Contact Email"}),e.jsx(p,{id:"contactEmail",type:"email",value:a.general.contactEmail,onChange:s=>t("general","contactEmail",s.target.value)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(m,{htmlFor:"enableRegistration",children:"Enable User Registration"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Allow new users to register on the platform"})]}),e.jsx(g,{id:"enableRegistration",checked:a.general.enableRegistration,onCheckedChange:s=>t("general","enableRegistration",s)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(m,{htmlFor:"requireEmailVerification",children:"Require Email Verification"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Users must verify their email before accessing the platform"})]}),e.jsx(g,{id:"requireEmailVerification",checked:a.general.requireEmailVerification,onCheckedChange:s=>t("general","requireEmailVerification",s)})]})]})]})}),e.jsx(v,{value:"books",children:e.jsxs(y,{children:[e.jsxs(k,{children:[e.jsx(C,{children:"Book Settings"}),e.jsx(w,{children:"Configure book-related settings"})]}),e.jsxs(S,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(m,{htmlFor:"requireApproval",children:"Require Book Approval"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"New book submissions require admin approval"})]}),e.jsx(g,{id:"requireApproval",checked:a.books.requireApproval,onCheckedChange:s=>t("books","requireApproval",s)})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(m,{htmlFor:"maxBooksPerUser",children:"Maximum Books Per User"}),e.jsx(p,{id:"maxBooksPerUser",type:"number",value:a.books.maxBooksPerUser.toString(),onChange:s=>t("books","maxBooksPerUser",parseInt(s.target.value))})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(m,{htmlFor:"allowMultipleImages",children:"Allow Multiple Images"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Users can upload multiple images per book"})]}),e.jsx(g,{id:"allowMultipleImages",checked:a.books.allowMultipleImages,onCheckedChange:s=>t("books","allowMultipleImages",s)})]})]})]})}),e.jsx(v,{value:"notifications",children:e.jsxs(y,{children:[e.jsxs(k,{children:[e.jsx(C,{children:"Notification Settings"}),e.jsx(w,{children:"Configure email and system notifications"})]}),e.jsxs(S,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(m,{htmlFor:"enableEmailNotifications",children:"Enable Email Notifications"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Send email notifications for important events"})]}),e.jsx(g,{id:"enableEmailNotifications",checked:a.notifications.enableEmailNotifications,onCheckedChange:s=>t("notifications","enableEmailNotifications",s)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(m,{htmlFor:"notifyOnNewUser",children:"Notify on New User Registration"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Send notification when a new user registers"})]}),e.jsx(g,{id:"notifyOnNewUser",checked:a.notifications.notifyOnNewUser,onCheckedChange:s=>t("notifications","notifyOnNewUser",s)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(m,{htmlFor:"notifyOnBookSubmission",children:"Notify on Book Submission"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Send notification when a new book is submitted"})]}),e.jsx(g,{id:"notifyOnBookSubmission",checked:a.notifications.notifyOnBookSubmission,onCheckedChange:s=>t("notifications","notifyOnBookSubmission",s)})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(m,{htmlFor:"adminEmailRecipients",children:"Admin Email Recipients"}),e.jsx(p,{id:"adminEmailRecipients",value:a.notifications.adminEmailRecipients,onChange:s=>t("notifications","adminEmailRecipients",s.target.value)}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Separate multiple email addresses with commas"})]})]})]})})]})]})};export{fe as default};
