{"version": 3, "file": "snow.js", "names": ["merge", "Emitter", "BaseTheme", "BaseTooltip", "LinkBlot", "Range", "icons", "<PERSON><PERSON><PERSON>", "TOOLBAR_CONFIG", "header", "list", "SnowTooltip", "TEMPLATE", "join", "preview", "root", "querySelector", "listen", "addEventListener", "event", "classList", "contains", "save", "edit", "textContent", "preventDefault", "linkRange", "range", "restoreFocus", "quill", "formatText", "sources", "USER", "hide", "on", "events", "SELECTION_CHANGE", "oldRange", "source", "length", "link", "offset", "scroll", "descendant", "index", "formats", "domNode", "setAttribute", "show", "bounds", "getBounds", "position", "removeAttribute", "SnowTheme", "constructor", "options", "modules", "toolbar", "container", "add", "extendToolbar", "buildButtons", "querySelectorAll", "buildPickers", "tooltip", "keyboard", "addBinding", "key", "<PERSON><PERSON><PERSON>", "_range", "context", "handlers", "call", "format", "DEFAULTS", "value", "getSelection", "getText", "test", "indexOf", "theme"], "sources": ["../../src/themes/snow.ts"], "sourcesContent": ["import { merge } from 'lodash-es';\nimport Emitter from '../core/emitter.js';\nimport BaseTheme, { BaseTooltip } from './base.js';\nimport LinkBlot from '../formats/link.js';\nimport { Range } from '../core/selection.js';\nimport icons from '../ui/icons.js';\nimport Quill from '../core/quill.js';\nimport type { Context } from '../modules/keyboard.js';\nimport type Toolbar from '../modules/toolbar.js';\nimport type { ToolbarConfig } from '../modules/toolbar.js';\nimport type { ThemeOptions } from '../core/theme.js';\n\nconst TOOLBAR_CONFIG: ToolbarConfig = [\n  [{ header: ['1', '2', '3', false] }],\n  ['bold', 'italic', 'underline', 'link'],\n  [{ list: 'ordered' }, { list: 'bullet' }],\n  ['clean'],\n];\n\nclass SnowTooltip extends BaseTooltip {\n  static TEMPLATE = [\n    '<a class=\"ql-preview\" rel=\"noopener noreferrer\" target=\"_blank\" href=\"about:blank\"></a>',\n    '<input type=\"text\" data-formula=\"e=mc^2\" data-link=\"https://quilljs.com\" data-video=\"Embed URL\">',\n    '<a class=\"ql-action\"></a>',\n    '<a class=\"ql-remove\"></a>',\n  ].join('');\n\n  preview = this.root.querySelector('a.ql-preview');\n\n  listen() {\n    super.listen();\n    // @ts-expect-error Fix me later\n    this.root\n      .querySelector('a.ql-action')\n      .addEventListener('click', (event) => {\n        if (this.root.classList.contains('ql-editing')) {\n          this.save();\n        } else {\n          // @ts-expect-error Fix me later\n          this.edit('link', this.preview.textContent);\n        }\n        event.preventDefault();\n      });\n    // @ts-expect-error Fix me later\n    this.root\n      .querySelector('a.ql-remove')\n      .addEventListener('click', (event) => {\n        if (this.linkRange != null) {\n          const range = this.linkRange;\n          this.restoreFocus();\n          this.quill.formatText(range, 'link', false, Emitter.sources.USER);\n          delete this.linkRange;\n        }\n        event.preventDefault();\n        this.hide();\n      });\n    this.quill.on(\n      Emitter.events.SELECTION_CHANGE,\n      (range, oldRange, source) => {\n        if (range == null) return;\n        if (range.length === 0 && source === Emitter.sources.USER) {\n          const [link, offset] = this.quill.scroll.descendant(\n            LinkBlot,\n            range.index,\n          );\n          if (link != null) {\n            this.linkRange = new Range(range.index - offset, link.length());\n            const preview = LinkBlot.formats(link.domNode);\n            // @ts-expect-error Fix me later\n            this.preview.textContent = preview;\n            // @ts-expect-error Fix me later\n            this.preview.setAttribute('href', preview);\n            this.show();\n            const bounds = this.quill.getBounds(this.linkRange);\n            if (bounds != null) {\n              this.position(bounds);\n            }\n            return;\n          }\n        } else {\n          delete this.linkRange;\n        }\n        this.hide();\n      },\n    );\n  }\n\n  show() {\n    super.show();\n    this.root.removeAttribute('data-mode');\n  }\n}\n\nclass SnowTheme extends BaseTheme {\n  constructor(quill: Quill, options: ThemeOptions) {\n    if (\n      options.modules.toolbar != null &&\n      options.modules.toolbar.container == null\n    ) {\n      options.modules.toolbar.container = TOOLBAR_CONFIG;\n    }\n    super(quill, options);\n    this.quill.container.classList.add('ql-snow');\n  }\n\n  extendToolbar(toolbar: Toolbar) {\n    if (toolbar.container != null) {\n      toolbar.container.classList.add('ql-snow');\n      this.buildButtons(toolbar.container.querySelectorAll('button'), icons);\n      this.buildPickers(toolbar.container.querySelectorAll('select'), icons);\n      // @ts-expect-error\n      this.tooltip = new SnowTooltip(this.quill, this.options.bounds);\n      if (toolbar.container.querySelector('.ql-link')) {\n        this.quill.keyboard.addBinding(\n          { key: 'k', shortKey: true },\n          (_range: Range, context: Context) => {\n            toolbar.handlers.link.call(toolbar, !context.format.link);\n          },\n        );\n      }\n    }\n  }\n}\nSnowTheme.DEFAULTS = merge({}, BaseTheme.DEFAULTS, {\n  modules: {\n    toolbar: {\n      handlers: {\n        link(value: string) {\n          if (value) {\n            const range = this.quill.getSelection();\n            if (range == null || range.length === 0) return;\n            let preview = this.quill.getText(range);\n            if (\n              /^\\S+@\\S+\\.\\S+$/.test(preview) &&\n              preview.indexOf('mailto:') !== 0\n            ) {\n              preview = `mailto:${preview}`;\n            }\n            // @ts-expect-error\n            const { tooltip } = this.quill.theme;\n            tooltip.edit('link', preview);\n          } else {\n            this.quill.format('link', false, Quill.sources.USER);\n          }\n        },\n      },\n    },\n  },\n} satisfies ThemeOptions);\n\nexport default SnowTheme;\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,WAAW;AACjC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,SAAS,IAAIC,WAAW,QAAQ,WAAW;AAClD,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,SAASC,KAAK,QAAQ,sBAAsB;AAC5C,OAAOC,KAAK,MAAM,gBAAgB;AAClC,OAAOC,KAAK,MAAM,kBAAkB;AAMpC,MAAMC,cAA6B,GAAG,CACpC,CAAC;EAAEC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK;AAAE,CAAC,CAAC,EACpC,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,CAAC,EACvC,CAAC;EAAEC,IAAI,EAAE;AAAU,CAAC,EAAE;EAAEA,IAAI,EAAE;AAAS,CAAC,CAAC,EACzC,CAAC,OAAO,CAAC,CACV;AAED,MAAMC,WAAW,SAASR,WAAW,CAAC;EACpC,OAAOS,QAAQ,GAAG,CAChB,yFAAyF,EACzF,kGAAkG,EAClG,2BAA2B,EAC3B,2BAA2B,CAC5B,CAACC,IAAI,CAAC,EAAE,CAAC;EAEVC,OAAO,GAAG,IAAI,CAACC,IAAI,CAACC,aAAa,CAAC,cAAc,CAAC;EAEjDC,MAAMA,CAAA,EAAG;IACP,KAAK,CAACA,MAAM,CAAC,CAAC;IACd;IACA,IAAI,CAACF,IAAI,CACNC,aAAa,CAAC,aAAa,CAAC,CAC5BE,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;MACpC,IAAI,IAAI,CAACJ,IAAI,CAACK,SAAS,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;QAC9C,IAAI,CAACC,IAAI,CAAC,CAAC;MACb,CAAC,MAAM;QACL;QACA,IAAI,CAACC,IAAI,CAAC,MAAM,EAAE,IAAI,CAACT,OAAO,CAACU,WAAW,CAAC;MAC7C;MACAL,KAAK,CAACM,cAAc,CAAC,CAAC;IACxB,CAAC,CAAC;IACJ;IACA,IAAI,CAACV,IAAI,CACNC,aAAa,CAAC,aAAa,CAAC,CAC5BE,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;MACpC,IAAI,IAAI,CAACO,SAAS,IAAI,IAAI,EAAE;QAC1B,MAAMC,KAAK,GAAG,IAAI,CAACD,SAAS;QAC5B,IAAI,CAACE,YAAY,CAAC,CAAC;QACnB,IAAI,CAACC,KAAK,CAACC,UAAU,CAACH,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE1B,OAAO,CAAC8B,OAAO,CAACC,IAAI,CAAC;QACjE,OAAO,IAAI,CAACN,SAAS;MACvB;MACAP,KAAK,CAACM,cAAc,CAAC,CAAC;MACtB,IAAI,CAACQ,IAAI,CAAC,CAAC;IACb,CAAC,CAAC;IACJ,IAAI,CAACJ,KAAK,CAACK,EAAE,CACXjC,OAAO,CAACkC,MAAM,CAACC,gBAAgB,EAC/B,CAACT,KAAK,EAAEU,QAAQ,EAAEC,MAAM,KAAK;MAC3B,IAAIX,KAAK,IAAI,IAAI,EAAE;MACnB,IAAIA,KAAK,CAACY,MAAM,KAAK,CAAC,IAAID,MAAM,KAAKrC,OAAO,CAAC8B,OAAO,CAACC,IAAI,EAAE;QACzD,MAAM,CAACQ,IAAI,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACZ,KAAK,CAACa,MAAM,CAACC,UAAU,CACjDvC,QAAQ,EACRuB,KAAK,CAACiB,KACR,CAAC;QACD,IAAIJ,IAAI,IAAI,IAAI,EAAE;UAChB,IAAI,CAACd,SAAS,GAAG,IAAIrB,KAAK,CAACsB,KAAK,CAACiB,KAAK,GAAGH,MAAM,EAAED,IAAI,CAACD,MAAM,CAAC,CAAC,CAAC;UAC/D,MAAMzB,OAAO,GAAGV,QAAQ,CAACyC,OAAO,CAACL,IAAI,CAACM,OAAO,CAAC;UAC9C;UACA,IAAI,CAAChC,OAAO,CAACU,WAAW,GAAGV,OAAO;UAClC;UACA,IAAI,CAACA,OAAO,CAACiC,YAAY,CAAC,MAAM,EAAEjC,OAAO,CAAC;UAC1C,IAAI,CAACkC,IAAI,CAAC,CAAC;UACX,MAAMC,MAAM,GAAG,IAAI,CAACpB,KAAK,CAACqB,SAAS,CAAC,IAAI,CAACxB,SAAS,CAAC;UACnD,IAAIuB,MAAM,IAAI,IAAI,EAAE;YAClB,IAAI,CAACE,QAAQ,CAACF,MAAM,CAAC;UACvB;UACA;QACF;MACF,CAAC,MAAM;QACL,OAAO,IAAI,CAACvB,SAAS;MACvB;MACA,IAAI,CAACO,IAAI,CAAC,CAAC;IACb,CACF,CAAC;EACH;EAEAe,IAAIA,CAAA,EAAG;IACL,KAAK,CAACA,IAAI,CAAC,CAAC;IACZ,IAAI,CAACjC,IAAI,CAACqC,eAAe,CAAC,WAAW,CAAC;EACxC;AACF;AAEA,MAAMC,SAAS,SAASnD,SAAS,CAAC;EAChCoD,WAAWA,CAACzB,KAAY,EAAE0B,OAAqB,EAAE;IAC/C,IACEA,OAAO,CAACC,OAAO,CAACC,OAAO,IAAI,IAAI,IAC/BF,OAAO,CAACC,OAAO,CAACC,OAAO,CAACC,SAAS,IAAI,IAAI,EACzC;MACAH,OAAO,CAACC,OAAO,CAACC,OAAO,CAACC,SAAS,GAAGlD,cAAc;IACpD;IACA,KAAK,CAACqB,KAAK,EAAE0B,OAAO,CAAC;IACrB,IAAI,CAAC1B,KAAK,CAAC6B,SAAS,CAACtC,SAAS,CAACuC,GAAG,CAAC,SAAS,CAAC;EAC/C;EAEAC,aAAaA,CAACH,OAAgB,EAAE;IAC9B,IAAIA,OAAO,CAACC,SAAS,IAAI,IAAI,EAAE;MAC7BD,OAAO,CAACC,SAAS,CAACtC,SAAS,CAACuC,GAAG,CAAC,SAAS,CAAC;MAC1C,IAAI,CAACE,YAAY,CAACJ,OAAO,CAACC,SAAS,CAACI,gBAAgB,CAAC,QAAQ,CAAC,EAAExD,KAAK,CAAC;MACtE,IAAI,CAACyD,YAAY,CAACN,OAAO,CAACC,SAAS,CAACI,gBAAgB,CAAC,QAAQ,CAAC,EAAExD,KAAK,CAAC;MACtE;MACA,IAAI,CAAC0D,OAAO,GAAG,IAAIrD,WAAW,CAAC,IAAI,CAACkB,KAAK,EAAE,IAAI,CAAC0B,OAAO,CAACN,MAAM,CAAC;MAC/D,IAAIQ,OAAO,CAACC,SAAS,CAAC1C,aAAa,CAAC,UAAU,CAAC,EAAE;QAC/C,IAAI,CAACa,KAAK,CAACoC,QAAQ,CAACC,UAAU,CAC5B;UAAEC,GAAG,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAK,CAAC,EAC5B,CAACC,MAAa,EAAEC,OAAgB,KAAK;UACnCb,OAAO,CAACc,QAAQ,CAAC/B,IAAI,CAACgC,IAAI,CAACf,OAAO,EAAE,CAACa,OAAO,CAACG,MAAM,CAACjC,IAAI,CAAC;QAC3D,CACF,CAAC;MACH;IACF;EACF;AACF;AACAa,SAAS,CAACqB,QAAQ,GAAG1E,KAAK,CAAC,CAAC,CAAC,EAAEE,SAAS,CAACwE,QAAQ,EAAE;EACjDlB,OAAO,EAAE;IACPC,OAAO,EAAE;MACPc,QAAQ,EAAE;QACR/B,IAAIA,CAACmC,KAAa,EAAE;UAClB,IAAIA,KAAK,EAAE;YACT,MAAMhD,KAAK,GAAG,IAAI,CAACE,KAAK,CAAC+C,YAAY,CAAC,CAAC;YACvC,IAAIjD,KAAK,IAAI,IAAI,IAAIA,KAAK,CAACY,MAAM,KAAK,CAAC,EAAE;YACzC,IAAIzB,OAAO,GAAG,IAAI,CAACe,KAAK,CAACgD,OAAO,CAAClD,KAAK,CAAC;YACvC,IACE,gBAAgB,CAACmD,IAAI,CAAChE,OAAO,CAAC,IAC9BA,OAAO,CAACiE,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,EAChC;cACAjE,OAAO,GAAI,UAASA,OAAQ,EAAC;YAC/B;YACA;YACA,MAAM;cAAEkD;YAAQ,CAAC,GAAG,IAAI,CAACnC,KAAK,CAACmD,KAAK;YACpChB,OAAO,CAACzC,IAAI,CAAC,MAAM,EAAET,OAAO,CAAC;UAC/B,CAAC,MAAM;YACL,IAAI,CAACe,KAAK,CAAC4C,MAAM,CAAC,MAAM,EAAE,KAAK,EAAElE,KAAK,CAACwB,OAAO,CAACC,IAAI,CAAC;UACtD;QACF;MACF;IACF;EACF;AACF,CAAwB,CAAC;AAEzB,eAAeqB,SAAS", "ignoreList": []}