import{r as l,J as w,j as e,M as L,L as b,W as $,Y as I,e as T,G as v,Z as M,$ as o}from"./index-Bm_kDzMk.js";import{g as O}from"./blogService-CUQdar90.js";import{C as D}from"./circle-alert-FwbBEnuF.js";import"./storageService-eCr3LQAS.js";import"./index.esm-3FY_JRiK.js";import"./index.esm2017-H7c5Bkvh.js";const R=()=>e.jsxs("article",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-8 animate-pulse",role:"article","aria-label":"Loading blog post",children:[e.jsx(o,{className:"h-64 w-full"}),e.jsxs("div",{className:"p-6 space-y-4",children:[e.jsx(o,{className:"h-8 w-3/4"}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(o,{className:"h-4 w-24"}),e.jsx(o,{className:"h-4 w-32"}),e.jsx(o,{className:"h-4 w-20"})]}),e.jsx(o,{className:"h-4 w-full"}),e.jsx(o,{className:"h-4 w-5/6"}),e.jsx(o,{className:"h-4 w-4/5"}),e.jsxs("div",{className:"flex flex-wrap gap-2 mt-4",children:[e.jsx(o,{className:"h-6 w-16"}),e.jsx(o,{className:"h-6 w-20"}),e.jsx(o,{className:"h-6 w-14"})]})]}),e.jsx("span",{className:"sr-only",children:"Loading blog post content..."})]}),W=({onRetry:n,error:m})=>e.jsxs("div",{className:"text-center py-12",role:"alert","aria-live":"polite",children:[e.jsx(D,{className:"h-16 w-16 text-red-500 mx-auto mb-4"}),e.jsx("h2",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Unable to Load Blog Posts"}),e.jsx("p",{className:"text-gray-600 mb-2 max-w-md mx-auto",children:"We're having trouble loading the blog posts right now."}),m&&e.jsxs("p",{className:"text-sm text-gray-500 mb-4 max-w-md mx-auto",children:["Error: ",m]}),e.jsx("p",{className:"text-gray-600 mb-6 max-w-md mx-auto",children:"Please check your internet connection and try again."}),e.jsx("button",{onClick:n,className:"bg-burgundy-600 hover:bg-burgundy-700 text-white px-6 py-2 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-burgundy-500 focus:ring-offset-2","aria-label":"Retry loading blog posts",children:"Try Again"})]}),G=()=>{const[n,m]=l.useState([]),[i,p]=l.useState(!0),[c,y]=l.useState(null),[r,k]=l.useState(1),x=6,P=l.useCallback((t,s)=>{if(s&&s.trim())return s.length>200?s.substring(0,200)+"...":s;const a=t.replace(/[#*`_~\[\]()]/g,"").replace(/<[^>]*>/g,"").replace(/\s+/g," ").trim();return a.length>150?a.substring(0,150)+"...":a},[]),B=l.useCallback(t=>t.toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),[]),u=l.useCallback(async()=>{try{p(!0),y(null),console.log("Fetching published blog posts...");const t=await O();m(t),console.log(`Successfully loaded ${t.length} blog posts`),t.length===0&&w.info("No blog posts available yet. Check back soon!")}catch(t){console.error("Error fetching blog posts:",t);const s=t instanceof Error?t.message:"Failed to load blog posts";y(s),w.error("Failed to load blog posts. Please try again.")}finally{p(!1)}},[]);l.useEffect(()=>{u()},[u]),l.useEffect(()=>{document.title="Blog - PeerBooks | Book Sharing Community Stories";let t=document.querySelector('meta[name="description"]');t||(t=document.createElement("meta"),t.setAttribute("name","description"),document.head.appendChild(t)),t.setAttribute("content","Discover stories, insights, and updates from our book-sharing community. Read about book recommendations, sharing experiences, and community highlights on PeerBooks.");const s=(N,E)=>{let d=document.querySelector(`meta[property="${N}"]`);d||(d=document.createElement("meta"),d.setAttribute("property",N),document.head.appendChild(d)),d.setAttribute("content",E)};s("og:title","Blog - PeerBooks | Book Sharing Community Stories"),s("og:description","Discover stories, insights, and updates from our book-sharing community. Read about book recommendations, sharing experiences, and community highlights on PeerBooks."),s("og:type","website"),s("og:url",window.location.href),s("og:site_name","PeerBooks");let a=document.querySelector('link[rel="canonical"]');return a||(a=document.createElement("link"),a.setAttribute("rel","canonical"),document.head.appendChild(a)),a.setAttribute("href",window.location.href),()=>{document.title="PeerBooks - Share Books, Build Community"}},[]);const g=Math.ceil(n.length/x),f=(r-1)*x,C=f+x,j=n.slice(f,C),h=t=>{k(t),window.scrollTo({top:0,behavior:"smooth"})},S=()=>{r>1&&h(r-1)},A=()=>{r<g&&h(r+1)};return e.jsx(L,{children:e.jsxs("div",{className:"min-h-screen bg-beige-50",children:[e.jsx("div",{className:"bg-gradient-to-r from-burgundy-600 to-burgundy-700 text-white py-16",children:e.jsxs("div",{className:"container mx-auto px-4 text-center",children:[e.jsx("h1",{className:"text-4xl md:text-5xl font-playfair font-bold mb-4",children:"PeerBooks Blog"}),e.jsx("p",{className:"text-xl md:text-2xl text-burgundy-100 max-w-2xl mx-auto",children:"Discover stories, insights, and updates from our book-sharing community"})]})}),e.jsx("div",{className:"container mx-auto px-4 py-12",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[i&&e.jsx("div",{className:"space-y-8",role:"status","aria-label":"Loading blog posts",children:[...Array(3)].map((t,s)=>e.jsx(R,{},s))}),c&&!i&&e.jsx(W,{onRetry:u,error:c}),!i&&!c&&n.length===0&&e.jsxs("div",{className:"text-center py-12",role:"status","aria-live":"polite",children:[e.jsx("div",{className:"text-6xl mb-4","aria-hidden":"true",children:"📚"}),e.jsx("h2",{className:"text-2xl font-bold text-navy-800 mb-2",children:"No Blog Posts Yet"}),e.jsx("p",{className:"text-gray-600 max-w-md mx-auto",children:"We're working on creating amazing content for you. Check back soon for the latest updates and stories from our book-sharing community!"})]}),!i&&!c&&n.length>0&&e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[j.map(t=>e.jsxs("article",{className:"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 group",children:[t.coverImageUrl&&e.jsx(b,{to:`/blog/${t.slug}`,className:"block",children:e.jsx("div",{className:"relative h-48 overflow-hidden",children:e.jsx("img",{src:t.coverImageUrl,alt:t.title,className:"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300",loading:"lazy"})})}),e.jsxs("div",{className:"p-6",children:[e.jsx(b,{to:`/blog/${t.slug}`,className:"block mb-3",children:e.jsx("h2",{className:"text-xl font-playfair font-bold text-navy-800 hover:text-burgundy-600 transition-colors duration-200 overflow-hidden",style:{display:"-webkit-box",WebkitLineClamp:2,WebkitBoxOrient:"vertical"},children:t.title})}),e.jsxs("div",{className:"flex flex-wrap items-center gap-3 text-xs text-gray-600 mb-3",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx($,{className:"h-3 w-3 mr-1"}),e.jsx("span",{children:t.author})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(I,{className:"h-3 w-3 mr-1"}),e.jsx("span",{children:B(t.createdAt)})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(T,{className:"h-3 w-3 mr-1"}),e.jsxs("span",{children:[t.readTime," min read"]})]})]}),e.jsx("p",{className:"text-gray-700 text-sm leading-relaxed mb-4 overflow-hidden",style:{display:"-webkit-box",WebkitLineClamp:3,WebkitBoxOrient:"vertical"},children:P(t.content,t.excerpt)}),t.tags&&t.tags.length>0&&e.jsxs("div",{className:"flex flex-wrap gap-1 mb-4",children:[t.tags.slice(0,3).map((s,a)=>e.jsx(v,{variant:"secondary",className:"bg-beige-100 text-navy-700 text-xs px-2 py-1",children:s},a)),t.tags.length>3&&e.jsxs(v,{variant:"secondary",className:"bg-gray-100 text-gray-600 text-xs px-2 py-1",children:["+",t.tags.length-3]})]}),e.jsxs(b,{to:`/blog/${t.slug}`,className:"inline-flex items-center text-burgundy-600 hover:text-burgundy-700 font-medium text-sm transition-colors duration-200",children:["Read More",e.jsx(M,{className:"h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform duration-200"})]})]})]},t.id)),g>1&&e.jsxs("div",{className:"flex justify-center items-center space-x-2 mt-12",children:[e.jsx("button",{onClick:S,disabled:r===1,className:"px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed","aria-label":"Previous page",children:"Previous"}),e.jsx("div",{className:"flex space-x-1",children:Array.from({length:g},(t,s)=>s+1).map(t=>e.jsx("button",{onClick:()=>h(t),className:`px-3 py-2 text-sm font-medium rounded-md ${r===t?"bg-burgundy-600 text-white":"text-gray-700 bg-white border border-gray-300 hover:bg-gray-50"}`,"aria-label":`Go to page ${t}`,"aria-current":r===t?"page":void 0,children:t},t))}),e.jsx("button",{onClick:A,disabled:r===g,className:"px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed","aria-label":"Next page",children:"Next"})]})]})]})}),!i&&!c&&n.length>0&&e.jsx("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"Blog",name:"PeerBooks Blog",description:"Discover stories, insights, and updates from our book-sharing community",url:window.location.href,publisher:{"@type":"Organization",name:"PeerBooks",logo:{"@type":"ImageObject",url:`${window.location.origin}/logo.png`}},blogPost:j.map(t=>({"@type":"BlogPosting",headline:t.title,description:t.excerpt||t.content.substring(0,160),image:t.coverImageUrl||"",author:{"@type":"Person",name:t.author},datePublished:t.createdAt.toISOString(),dateModified:t.updatedAt.toISOString(),url:`${window.location.origin}/blog/${t.slug}`}))})}})]})})};export{G as default};
