const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.esm-CI7pLi7O.js","assets/index.esm2017-H7c5Bkvh.js","assets/index-Bm_kDzMk.js","assets/index-CM4Ud3ZP.css","assets/index.esm-ehpEbksy.js","assets/blogService-CUQdar90.js","assets/storageService-eCr3LQAS.js","assets/index.esm-3FY_JRiK.js"])))=>i.map(i=>d[i]);
import{y as Je,bg as ge,r as ne,b2 as Ur,bh as Hr,j as v,aD as vr,a8 as zr,z as ye,h as Kr,v as Vr,J as re,G as ve,A as Ge,X as qt,x as ce,i as Gr,k as Ae,l as Oe,m as Ne,n as je,I as mt,o as qe,as as Te,u as ke,a3 as Fe,d as et,C as Ue,e as He,N as $e,W as Wr,w as Zr,_ as Se,V as ht}from"./index-Bm_kDzMk.js";import{F as yt,A as Yr}from"./AdminLayout-BhV0nOiW.js";import{m as Xr,p as Qr}from"./purify.es-CKTREcYp.js";import{T as bt}from"./textarea-3apq9z-y.js";import{S as Jr}from"./switch-VgXrWVxu.js";import{D as en,a as tn,b as rn,c as nn,d as an,e as on}from"./dialog-DGwTPBRT.js";import{c as Tt,u as ln,d as sn,t as St,e as kt,p as xt,f as gr,g as un,B as Ke,i as mr,v as yr,h as cn,j as fn,k as dn,l as hn,m as pn,n as vn}from"./blogService-CUQdar90.js";import{S as br}from"./save-Ha4TCqpm.js";import{T as Pt}from"./trash-2-lEwRhIcV.js";import{E as gn}from"./eye-off-DwKNGezW.js";import{E as _t}from"./eye-BoIGsHz8.js";import{I as mn}from"./image-C6TIrua_.js";import{C as tt,a as rt,b as nt,d as it,c as at}from"./card-C-mOyesG.js";import{D as ze}from"./database-BfLJs0aL.js";import{S as wt}from"./shield-BmG7fYuo.js";import{A as yn,a as bn,b as xn,c as _n,d as wn,e as On,f as Nn,g as En}from"./alert-dialog-DalAdHKY.js";import{P as Bt}from"./plus-CnnKz7PL.js";import{S as An}from"./square-pen-tiuppb-P.js";import"./users-vJ223Oqr.js";import"./index-Cr71giPO.js";import"./storageService-eCr3LQAS.js";import"./index.esm-3FY_JRiK.js";import"./index.esm2017-H7c5Bkvh.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jn=Je("Bug",[["path",{d:"m8 2 1.88 1.88",key:"fmnt4t"}],["path",{d:"M14.12 3.88 16 2",key:"qol33r"}],["path",{d:"M9 7.13v-1a3.003 3.003 0 1 1 6 0v1",key:"d7y7pr"}],["path",{d:"M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6",key:"xs1cw7"}],["path",{d:"M12 20v-9",key:"1qisl0"}],["path",{d:"M6.53 9C4.6 8.8 3 7.1 3 5",key:"32zzws"}],["path",{d:"M6 13H2",key:"82j7cp"}],["path",{d:"M3 21c0-2.1 1.7-3.9 3.8-4",key:"4p0ekp"}],["path",{d:"M20.97 5c0 2.1-1.6 3.8-3.5 4",key:"18gb23"}],["path",{d:"M22 13h-4",key:"1jl80f"}],["path",{d:"M17.2 17c2.1.1 3.8 1.9 3.8 4",key:"k3fwyw"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tn=Je("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sn=Je("HardDrive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kn=Je("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]);function Pn(){this.__data__=[],this.size=0}var Ln=Pn;function In(p,k){return p===k||p!==p&&k!==k}var xr=In,Cn=xr;function Rn(p,k){for(var x=p.length;x--;)if(Cn(p[x][0],k))return x;return-1}var ot=Rn,Dn=ot,qn=Array.prototype,Bn=qn.splice;function Mn(p){var k=this.__data__,x=Dn(k,p);if(x<0)return!1;var u=k.length-1;return x==u?k.pop():Bn.call(k,x,1),--this.size,!0}var $n=Mn,Fn=ot;function Un(p){var k=this.__data__,x=Fn(k,p);return x<0?void 0:k[x][1]}var Hn=Un,zn=ot;function Kn(p){return zn(this.__data__,p)>-1}var Vn=Kn,Gn=ot;function Wn(p,k){var x=this.__data__,u=Gn(x,p);return u<0?(++this.size,x.push([p,k])):x[u][1]=k,this}var Zn=Wn,Yn=Ln,Xn=$n,Qn=Hn,Jn=Vn,ei=Zn;function Pe(p){var k=-1,x=p==null?0:p.length;for(this.clear();++k<x;){var u=p[k];this.set(u[0],u[1])}}Pe.prototype.clear=Yn;Pe.prototype.delete=Xn;Pe.prototype.get=Qn;Pe.prototype.has=Jn;Pe.prototype.set=ei;var lt=Pe,ti=lt;function ri(){this.__data__=new ti,this.size=0}var ni=ri;function ii(p){var k=this.__data__,x=k.delete(p);return this.size=k.size,x}var ai=ii;function oi(p){return this.__data__.get(p)}var li=oi;function si(p){return this.__data__.has(p)}var ui=si,ci=typeof ge=="object"&&ge&&ge.Object===Object&&ge,_r=ci,fi=_r,di=typeof self=="object"&&self&&self.Object===Object&&self,hi=fi||di||Function("return this")(),_e=hi,pi=_e,vi=pi.Symbol,Lt=vi,Mt=Lt,wr=Object.prototype,gi=wr.hasOwnProperty,mi=wr.toString,Be=Mt?Mt.toStringTag:void 0;function yi(p){var k=gi.call(p,Be),x=p[Be];try{p[Be]=void 0;var u=!0}catch{}var c=mi.call(p);return u&&(k?p[Be]=x:delete p[Be]),c}var bi=yi,xi=Object.prototype,_i=xi.toString;function wi(p){return _i.call(p)}var Oi=wi,$t=Lt,Ni=bi,Ei=Oi,Ai="[object Null]",ji="[object Undefined]",Ft=$t?$t.toStringTag:void 0;function Ti(p){return p==null?p===void 0?ji:Ai:Ft&&Ft in Object(p)?Ni(p):Ei(p)}var st=Ti;function Si(p){var k=typeof p;return p!=null&&(k=="object"||k=="function")}var Or=Si,ki=st,Pi=Or,Li="[object AsyncFunction]",Ii="[object Function]",Ci="[object GeneratorFunction]",Ri="[object Proxy]";function Di(p){if(!Pi(p))return!1;var k=ki(p);return k==Ii||k==Ci||k==Li||k==Ri}var Nr=Di,qi=_e,Bi=qi["__core-js_shared__"],Mi=Bi,pt=Mi,Ut=function(){var p=/[^.]+$/.exec(pt&&pt.keys&&pt.keys.IE_PROTO||"");return p?"Symbol(src)_1."+p:""}();function $i(p){return!!Ut&&Ut in p}var Fi=$i,Ui=Function.prototype,Hi=Ui.toString;function zi(p){if(p!=null){try{return Hi.call(p)}catch{}try{return p+""}catch{}}return""}var Er=zi,Ki=Nr,Vi=Fi,Gi=Or,Wi=Er,Zi=/[\\^$.*+?()[\]{}|]/g,Yi=/^\[object .+?Constructor\]$/,Xi=Function.prototype,Qi=Object.prototype,Ji=Xi.toString,ea=Qi.hasOwnProperty,ta=RegExp("^"+Ji.call(ea).replace(Zi,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function ra(p){if(!Gi(p)||Vi(p))return!1;var k=Ki(p)?ta:Yi;return k.test(Wi(p))}var na=ra;function ia(p,k){return p==null?void 0:p[k]}var aa=ia,oa=na,la=aa;function sa(p,k){var x=la(p,k);return oa(x)?x:void 0}var Le=sa,ua=Le,ca=_e,fa=ua(ca,"Map"),It=fa,da=Le,ha=da(Object,"create"),ut=ha,Ht=ut;function pa(){this.__data__=Ht?Ht(null):{},this.size=0}var va=pa;function ga(p){var k=this.has(p)&&delete this.__data__[p];return this.size-=k?1:0,k}var ma=ga,ya=ut,ba="__lodash_hash_undefined__",xa=Object.prototype,_a=xa.hasOwnProperty;function wa(p){var k=this.__data__;if(ya){var x=k[p];return x===ba?void 0:x}return _a.call(k,p)?k[p]:void 0}var Oa=wa,Na=ut,Ea=Object.prototype,Aa=Ea.hasOwnProperty;function ja(p){var k=this.__data__;return Na?k[p]!==void 0:Aa.call(k,p)}var Ta=ja,Sa=ut,ka="__lodash_hash_undefined__";function Pa(p,k){var x=this.__data__;return this.size+=this.has(p)?0:1,x[p]=Sa&&k===void 0?ka:k,this}var La=Pa,Ia=va,Ca=ma,Ra=Oa,Da=Ta,qa=La;function Ie(p){var k=-1,x=p==null?0:p.length;for(this.clear();++k<x;){var u=p[k];this.set(u[0],u[1])}}Ie.prototype.clear=Ia;Ie.prototype.delete=Ca;Ie.prototype.get=Ra;Ie.prototype.has=Da;Ie.prototype.set=qa;var Ba=Ie,zt=Ba,Ma=lt,$a=It;function Fa(){this.size=0,this.__data__={hash:new zt,map:new($a||Ma),string:new zt}}var Ua=Fa;function Ha(p){var k=typeof p;return k=="string"||k=="number"||k=="symbol"||k=="boolean"?p!=="__proto__":p===null}var za=Ha,Ka=za;function Va(p,k){var x=p.__data__;return Ka(k)?x[typeof k=="string"?"string":"hash"]:x.map}var ct=Va,Ga=ct;function Wa(p){var k=Ga(this,p).delete(p);return this.size-=k?1:0,k}var Za=Wa,Ya=ct;function Xa(p){return Ya(this,p).get(p)}var Qa=Xa,Ja=ct;function eo(p){return Ja(this,p).has(p)}var to=eo,ro=ct;function no(p,k){var x=ro(this,p),u=x.size;return x.set(p,k),this.size+=x.size==u?0:1,this}var io=no,ao=Ua,oo=Za,lo=Qa,so=to,uo=io;function Ce(p){var k=-1,x=p==null?0:p.length;for(this.clear();++k<x;){var u=p[k];this.set(u[0],u[1])}}Ce.prototype.clear=ao;Ce.prototype.delete=oo;Ce.prototype.get=lo;Ce.prototype.has=so;Ce.prototype.set=uo;var Ar=Ce,co=lt,fo=It,ho=Ar,po=200;function vo(p,k){var x=this.__data__;if(x instanceof co){var u=x.__data__;if(!fo||u.length<po-1)return u.push([p,k]),this.size=++x.size,this;x=this.__data__=new ho(u)}return x.set(p,k),this.size=x.size,this}var go=vo,mo=lt,yo=ni,bo=ai,xo=li,_o=ui,wo=go;function Re(p){var k=this.__data__=new mo(p);this.size=k.size}Re.prototype.clear=yo;Re.prototype.delete=bo;Re.prototype.get=xo;Re.prototype.has=_o;Re.prototype.set=wo;var Oo=Re,No="__lodash_hash_undefined__";function Eo(p){return this.__data__.set(p,No),this}var Ao=Eo;function jo(p){return this.__data__.has(p)}var To=jo,So=Ar,ko=Ao,Po=To;function We(p){var k=-1,x=p==null?0:p.length;for(this.__data__=new So;++k<x;)this.add(p[k])}We.prototype.add=We.prototype.push=ko;We.prototype.has=Po;var Lo=We;function Io(p,k){for(var x=-1,u=p==null?0:p.length;++x<u;)if(k(p[x],x,p))return!0;return!1}var Co=Io;function Ro(p,k){return p.has(k)}var Do=Ro,qo=Lo,Bo=Co,Mo=Do,$o=1,Fo=2;function Uo(p,k,x,u,c,N){var w=x&$o,y=p.length,g=k.length;if(y!=g&&!(w&&g>y))return!1;var m=N.get(p),d=N.get(k);if(m&&d)return m==k&&d==p;var o=-1,e=!0,t=x&Fo?new qo:void 0;for(N.set(p,k),N.set(k,p);++o<y;){var a=p[o],s=k[o];if(u)var l=w?u(s,a,o,k,p,N):u(a,s,o,p,k,N);if(l!==void 0){if(l)continue;e=!1;break}if(t){if(!Bo(k,function(r,n){if(!Mo(t,n)&&(a===r||c(a,r,x,u,N)))return t.push(n)})){e=!1;break}}else if(!(a===s||c(a,s,x,u,N))){e=!1;break}}return N.delete(p),N.delete(k),e}var jr=Uo,Ho=_e,zo=Ho.Uint8Array,Ko=zo;function Vo(p){var k=-1,x=Array(p.size);return p.forEach(function(u,c){x[++k]=[c,u]}),x}var Go=Vo;function Wo(p){var k=-1,x=Array(p.size);return p.forEach(function(u){x[++k]=u}),x}var Zo=Wo,Kt=Lt,Vt=Ko,Yo=xr,Xo=jr,Qo=Go,Jo=Zo,el=1,tl=2,rl="[object Boolean]",nl="[object Date]",il="[object Error]",al="[object Map]",ol="[object Number]",ll="[object RegExp]",sl="[object Set]",ul="[object String]",cl="[object Symbol]",fl="[object ArrayBuffer]",dl="[object DataView]",Gt=Kt?Kt.prototype:void 0,vt=Gt?Gt.valueOf:void 0;function hl(p,k,x,u,c,N,w){switch(x){case dl:if(p.byteLength!=k.byteLength||p.byteOffset!=k.byteOffset)return!1;p=p.buffer,k=k.buffer;case fl:return!(p.byteLength!=k.byteLength||!N(new Vt(p),new Vt(k)));case rl:case nl:case ol:return Yo(+p,+k);case il:return p.name==k.name&&p.message==k.message;case ll:case ul:return p==k+"";case al:var y=Qo;case sl:var g=u&el;if(y||(y=Jo),p.size!=k.size&&!g)return!1;var m=w.get(p);if(m)return m==k;u|=tl,w.set(p,k);var d=Xo(y(p),y(k),u,c,N,w);return w.delete(p),d;case cl:if(vt)return vt.call(p)==vt.call(k)}return!1}var pl=hl;function vl(p,k){for(var x=-1,u=k.length,c=p.length;++x<u;)p[c+x]=k[x];return p}var gl=vl,ml=Array.isArray,Ct=ml,yl=gl,bl=Ct;function xl(p,k,x){var u=k(p);return bl(p)?u:yl(u,x(p))}var _l=xl;function wl(p,k){for(var x=-1,u=p==null?0:p.length,c=0,N=[];++x<u;){var w=p[x];k(w,x,p)&&(N[c++]=w)}return N}var Ol=wl;function Nl(){return[]}var El=Nl,Al=Ol,jl=El,Tl=Object.prototype,Sl=Tl.propertyIsEnumerable,Wt=Object.getOwnPropertySymbols,kl=Wt?function(p){return p==null?[]:(p=Object(p),Al(Wt(p),function(k){return Sl.call(p,k)}))}:jl,Pl=kl;function Ll(p,k){for(var x=-1,u=Array(p);++x<p;)u[x]=k(x);return u}var Il=Ll;function Cl(p){return p!=null&&typeof p=="object"}var ft=Cl,Rl=st,Dl=ft,ql="[object Arguments]";function Bl(p){return Dl(p)&&Rl(p)==ql}var Ml=Bl,Zt=Ml,$l=ft,Tr=Object.prototype,Fl=Tr.hasOwnProperty,Ul=Tr.propertyIsEnumerable,Hl=Zt(function(){return arguments}())?Zt:function(p){return $l(p)&&Fl.call(p,"callee")&&!Ul.call(p,"callee")},zl=Hl,Ze={exports:{}};function Kl(){return!1}var Vl=Kl;Ze.exports;(function(p,k){var x=_e,u=Vl,c=k&&!k.nodeType&&k,N=c&&!0&&p&&!p.nodeType&&p,w=N&&N.exports===c,y=w?x.Buffer:void 0,g=y?y.isBuffer:void 0,m=g||u;p.exports=m})(Ze,Ze.exports);var Sr=Ze.exports,Gl=9007199254740991,Wl=/^(?:0|[1-9]\d*)$/;function Zl(p,k){var x=typeof p;return k=k??Gl,!!k&&(x=="number"||x!="symbol"&&Wl.test(p))&&p>-1&&p%1==0&&p<k}var Yl=Zl,Xl=9007199254740991;function Ql(p){return typeof p=="number"&&p>-1&&p%1==0&&p<=Xl}var kr=Ql,Jl=st,es=kr,ts=ft,rs="[object Arguments]",ns="[object Array]",is="[object Boolean]",as="[object Date]",os="[object Error]",ls="[object Function]",ss="[object Map]",us="[object Number]",cs="[object Object]",fs="[object RegExp]",ds="[object Set]",hs="[object String]",ps="[object WeakMap]",vs="[object ArrayBuffer]",gs="[object DataView]",ms="[object Float32Array]",ys="[object Float64Array]",bs="[object Int8Array]",xs="[object Int16Array]",_s="[object Int32Array]",ws="[object Uint8Array]",Os="[object Uint8ClampedArray]",Ns="[object Uint16Array]",Es="[object Uint32Array]",fe={};fe[ms]=fe[ys]=fe[bs]=fe[xs]=fe[_s]=fe[ws]=fe[Os]=fe[Ns]=fe[Es]=!0;fe[rs]=fe[ns]=fe[vs]=fe[is]=fe[gs]=fe[as]=fe[os]=fe[ls]=fe[ss]=fe[us]=fe[cs]=fe[fs]=fe[ds]=fe[hs]=fe[ps]=!1;function As(p){return ts(p)&&es(p.length)&&!!fe[Jl(p)]}var js=As;function Ts(p){return function(k){return p(k)}}var Ss=Ts,Ye={exports:{}};Ye.exports;(function(p,k){var x=_r,u=k&&!k.nodeType&&k,c=u&&!0&&p&&!p.nodeType&&p,N=c&&c.exports===u,w=N&&x.process,y=function(){try{var g=c&&c.require&&c.require("util").types;return g||w&&w.binding&&w.binding("util")}catch{}}();p.exports=y})(Ye,Ye.exports);var ks=Ye.exports,Ps=js,Ls=Ss,Yt=ks,Xt=Yt&&Yt.isTypedArray,Is=Xt?Ls(Xt):Ps,Pr=Is,Cs=Il,Rs=zl,Ds=Ct,qs=Sr,Bs=Yl,Ms=Pr,$s=Object.prototype,Fs=$s.hasOwnProperty;function Us(p,k){var x=Ds(p),u=!x&&Rs(p),c=!x&&!u&&qs(p),N=!x&&!u&&!c&&Ms(p),w=x||u||c||N,y=w?Cs(p.length,String):[],g=y.length;for(var m in p)(k||Fs.call(p,m))&&!(w&&(m=="length"||c&&(m=="offset"||m=="parent")||N&&(m=="buffer"||m=="byteLength"||m=="byteOffset")||Bs(m,g)))&&y.push(m);return y}var Hs=Us,zs=Object.prototype;function Ks(p){var k=p&&p.constructor,x=typeof k=="function"&&k.prototype||zs;return p===x}var Vs=Ks;function Gs(p,k){return function(x){return p(k(x))}}var Ws=Gs,Zs=Ws,Ys=Zs(Object.keys,Object),Xs=Ys,Qs=Vs,Js=Xs,eu=Object.prototype,tu=eu.hasOwnProperty;function ru(p){if(!Qs(p))return Js(p);var k=[];for(var x in Object(p))tu.call(p,x)&&x!="constructor"&&k.push(x);return k}var nu=ru,iu=Nr,au=kr;function ou(p){return p!=null&&au(p.length)&&!iu(p)}var lu=ou,su=Hs,uu=nu,cu=lu;function fu(p){return cu(p)?su(p):uu(p)}var du=fu,hu=_l,pu=Pl,vu=du;function gu(p){return hu(p,vu,pu)}var mu=gu,Qt=mu,yu=1,bu=Object.prototype,xu=bu.hasOwnProperty;function _u(p,k,x,u,c,N){var w=x&yu,y=Qt(p),g=y.length,m=Qt(k),d=m.length;if(g!=d&&!w)return!1;for(var o=g;o--;){var e=y[o];if(!(w?e in k:xu.call(k,e)))return!1}var t=N.get(p),a=N.get(k);if(t&&a)return t==k&&a==p;var s=!0;N.set(p,k),N.set(k,p);for(var l=w;++o<g;){e=y[o];var r=p[e],n=k[e];if(u)var f=w?u(n,r,e,k,p,N):u(r,n,e,p,k,N);if(!(f===void 0?r===n||c(r,n,x,u,N):f)){s=!1;break}l||(l=e=="constructor")}if(s&&!l){var i=p.constructor,h=k.constructor;i!=h&&"constructor"in p&&"constructor"in k&&!(typeof i=="function"&&i instanceof i&&typeof h=="function"&&h instanceof h)&&(s=!1)}return N.delete(p),N.delete(k),s}var wu=_u,Ou=Le,Nu=_e,Eu=Ou(Nu,"DataView"),Au=Eu,ju=Le,Tu=_e,Su=ju(Tu,"Promise"),ku=Su,Pu=Le,Lu=_e,Iu=Pu(Lu,"Set"),Cu=Iu,Ru=Le,Du=_e,qu=Ru(Du,"WeakMap"),Bu=qu,Ot=Au,Nt=It,Et=ku,At=Cu,jt=Bu,Lr=st,De=Er,Jt="[object Map]",Mu="[object Object]",er="[object Promise]",tr="[object Set]",rr="[object WeakMap]",nr="[object DataView]",$u=De(Ot),Fu=De(Nt),Uu=De(Et),Hu=De(At),zu=De(jt),Ee=Lr;(Ot&&Ee(new Ot(new ArrayBuffer(1)))!=nr||Nt&&Ee(new Nt)!=Jt||Et&&Ee(Et.resolve())!=er||At&&Ee(new At)!=tr||jt&&Ee(new jt)!=rr)&&(Ee=function(p){var k=Lr(p),x=k==Mu?p.constructor:void 0,u=x?De(x):"";if(u)switch(u){case $u:return nr;case Fu:return Jt;case Uu:return er;case Hu:return tr;case zu:return rr}return k});var Ku=Ee,gt=Oo,Vu=jr,Gu=pl,Wu=wu,ir=Ku,ar=Ct,or=Sr,Zu=Pr,Yu=1,lr="[object Arguments]",sr="[object Array]",Ve="[object Object]",Xu=Object.prototype,ur=Xu.hasOwnProperty;function Qu(p,k,x,u,c,N){var w=ar(p),y=ar(k),g=w?sr:ir(p),m=y?sr:ir(k);g=g==lr?Ve:g,m=m==lr?Ve:m;var d=g==Ve,o=m==Ve,e=g==m;if(e&&or(p)){if(!or(k))return!1;w=!0,d=!1}if(e&&!d)return N||(N=new gt),w||Zu(p)?Vu(p,k,x,u,c,N):Gu(p,k,g,x,u,c,N);if(!(x&Yu)){var t=d&&ur.call(p,"__wrapped__"),a=o&&ur.call(k,"__wrapped__");if(t||a){var s=t?p.value():p,l=a?k.value():k;return N||(N=new gt),c(s,l,x,u,N)}}return e?(N||(N=new gt),Wu(p,k,x,u,c,N)):!1}var Ju=Qu,ec=Ju,cr=ft;function Ir(p,k,x,u,c){return p===k?!0:p==null||k==null||!cr(p)&&!cr(k)?p!==p&&k!==k:ec(p,k,x,u,Ir,c)}var tc=Ir,rc=tc;function nc(p,k){return rc(p,k)}var ic=nc,Cr={exports:{}};/*!
 * Quill Editor v1.3.7
 * https://quilljs.com/
 * Copyright (c) 2014, Jason Chen
 * Copyright (c) 2013, salesforce.com
 */(function(p,k){(function(u,c){p.exports=c()})(typeof self<"u"?self:ge,function(){return function(x){var u={};function c(N){if(u[N])return u[N].exports;var w=u[N]={i:N,l:!1,exports:{}};return x[N].call(w.exports,w,w.exports,c),w.l=!0,w.exports}return c.m=x,c.c=u,c.d=function(N,w,y){c.o(N,w)||Object.defineProperty(N,w,{configurable:!1,enumerable:!0,get:y})},c.n=function(N){var w=N&&N.__esModule?function(){return N.default}:function(){return N};return c.d(w,"a",w),w},c.o=function(N,w){return Object.prototype.hasOwnProperty.call(N,w)},c.p="",c(c.s=109)}([function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=c(17),w=c(18),y=c(19),g=c(45),m=c(46),d=c(47),o=c(48),e=c(49),t=c(12),a=c(32),s=c(33),l=c(31),r=c(1),n={Scope:r.Scope,create:r.create,find:r.find,query:r.query,register:r.register,Container:N.default,Format:w.default,Leaf:y.default,Embed:o.default,Scroll:g.default,Block:d.default,Inline:m.default,Text:e.default,Attributor:{Attribute:t.default,Class:a.default,Style:s.default,Store:l.default}};u.default=n},function(x,u,c){var N=this&&this.__extends||function(){var l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,n){r.__proto__=n}||function(r,n){for(var f in n)n.hasOwnProperty(f)&&(r[f]=n[f])};return function(r,n){l(r,n);function f(){this.constructor=r}r.prototype=n===null?Object.create(n):(f.prototype=n.prototype,new f)}}();Object.defineProperty(u,"__esModule",{value:!0});var w=function(l){N(r,l);function r(n){var f=this;return n="[Parchment] "+n,f=l.call(this,n)||this,f.message=n,f.name=f.constructor.name,f}return r}(Error);u.ParchmentError=w;var y={},g={},m={},d={};u.DATA_KEY="__blot";var o;(function(l){l[l.TYPE=3]="TYPE",l[l.LEVEL=12]="LEVEL",l[l.ATTRIBUTE=13]="ATTRIBUTE",l[l.BLOT=14]="BLOT",l[l.INLINE=7]="INLINE",l[l.BLOCK=11]="BLOCK",l[l.BLOCK_BLOT=10]="BLOCK_BLOT",l[l.INLINE_BLOT=6]="INLINE_BLOT",l[l.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE",l[l.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE",l[l.ANY=15]="ANY"})(o=u.Scope||(u.Scope={}));function e(l,r){var n=a(l);if(n==null)throw new w("Unable to create "+l+" blot");var f=n,i=l instanceof Node||l.nodeType===Node.TEXT_NODE?l:f.create(r);return new f(i,r)}u.create=e;function t(l,r){return r===void 0&&(r=!1),l==null?null:l[u.DATA_KEY]!=null?l[u.DATA_KEY].blot:r?t(l.parentNode,r):null}u.find=t;function a(l,r){r===void 0&&(r=o.ANY);var n;if(typeof l=="string")n=d[l]||y[l];else if(l instanceof Text||l.nodeType===Node.TEXT_NODE)n=d.text;else if(typeof l=="number")l&o.LEVEL&o.BLOCK?n=d.block:l&o.LEVEL&o.INLINE&&(n=d.inline);else if(l instanceof HTMLElement){var f=(l.getAttribute("class")||"").split(/\s+/);for(var i in f)if(n=g[f[i]],n)break;n=n||m[l.tagName]}return n==null?null:r&o.LEVEL&n.scope&&r&o.TYPE&n.scope?n:null}u.query=a;function s(){for(var l=[],r=0;r<arguments.length;r++)l[r]=arguments[r];if(l.length>1)return l.map(function(i){return s(i)});var n=l[0];if(typeof n.blotName!="string"&&typeof n.attrName!="string")throw new w("Invalid definition");if(n.blotName==="abstract")throw new w("Cannot register abstract class");if(d[n.blotName||n.attrName]=n,typeof n.keyName=="string")y[n.keyName]=n;else if(n.className!=null&&(g[n.className]=n),n.tagName!=null){Array.isArray(n.tagName)?n.tagName=n.tagName.map(function(i){return i.toUpperCase()}):n.tagName=n.tagName.toUpperCase();var f=Array.isArray(n.tagName)?n.tagName:[n.tagName];f.forEach(function(i){(m[i]==null||n.className==null)&&(m[i]=n)})}return n}u.register=s},function(x,u,c){var N=c(51),w=c(11),y=c(3),g=c(20),m="\0",d=function(o){Array.isArray(o)?this.ops=o:o!=null&&Array.isArray(o.ops)?this.ops=o.ops:this.ops=[]};d.prototype.insert=function(o,e){var t={};return o.length===0?this:(t.insert=o,e!=null&&typeof e=="object"&&Object.keys(e).length>0&&(t.attributes=e),this.push(t))},d.prototype.delete=function(o){return o<=0?this:this.push({delete:o})},d.prototype.retain=function(o,e){if(o<=0)return this;var t={retain:o};return e!=null&&typeof e=="object"&&Object.keys(e).length>0&&(t.attributes=e),this.push(t)},d.prototype.push=function(o){var e=this.ops.length,t=this.ops[e-1];if(o=y(!0,{},o),typeof t=="object"){if(typeof o.delete=="number"&&typeof t.delete=="number")return this.ops[e-1]={delete:t.delete+o.delete},this;if(typeof t.delete=="number"&&o.insert!=null&&(e-=1,t=this.ops[e-1],typeof t!="object"))return this.ops.unshift(o),this;if(w(o.attributes,t.attributes)){if(typeof o.insert=="string"&&typeof t.insert=="string")return this.ops[e-1]={insert:t.insert+o.insert},typeof o.attributes=="object"&&(this.ops[e-1].attributes=o.attributes),this;if(typeof o.retain=="number"&&typeof t.retain=="number")return this.ops[e-1]={retain:t.retain+o.retain},typeof o.attributes=="object"&&(this.ops[e-1].attributes=o.attributes),this}}return e===this.ops.length?this.ops.push(o):this.ops.splice(e,0,o),this},d.prototype.chop=function(){var o=this.ops[this.ops.length-1];return o&&o.retain&&!o.attributes&&this.ops.pop(),this},d.prototype.filter=function(o){return this.ops.filter(o)},d.prototype.forEach=function(o){this.ops.forEach(o)},d.prototype.map=function(o){return this.ops.map(o)},d.prototype.partition=function(o){var e=[],t=[];return this.forEach(function(a){var s=o(a)?e:t;s.push(a)}),[e,t]},d.prototype.reduce=function(o,e){return this.ops.reduce(o,e)},d.prototype.changeLength=function(){return this.reduce(function(o,e){return e.insert?o+g.length(e):e.delete?o-e.delete:o},0)},d.prototype.length=function(){return this.reduce(function(o,e){return o+g.length(e)},0)},d.prototype.slice=function(o,e){o=o||0,typeof e!="number"&&(e=1/0);for(var t=[],a=g.iterator(this.ops),s=0;s<e&&a.hasNext();){var l;s<o?l=a.next(o-s):(l=a.next(e-s),t.push(l)),s+=g.length(l)}return new d(t)},d.prototype.compose=function(o){var e=g.iterator(this.ops),t=g.iterator(o.ops),a=[],s=t.peek();if(s!=null&&typeof s.retain=="number"&&s.attributes==null){for(var l=s.retain;e.peekType()==="insert"&&e.peekLength()<=l;)l-=e.peekLength(),a.push(e.next());s.retain-l>0&&t.next(s.retain-l)}for(var r=new d(a);e.hasNext()||t.hasNext();)if(t.peekType()==="insert")r.push(t.next());else if(e.peekType()==="delete")r.push(e.next());else{var n=Math.min(e.peekLength(),t.peekLength()),f=e.next(n),i=t.next(n);if(typeof i.retain=="number"){var h={};typeof f.retain=="number"?h.retain=n:h.insert=f.insert;var T=g.attributes.compose(f.attributes,i.attributes,typeof f.retain=="number");if(T&&(h.attributes=T),r.push(h),!t.hasNext()&&w(r.ops[r.ops.length-1],h)){var E=new d(e.rest());return r.concat(E).chop()}}else typeof i.delete=="number"&&typeof f.retain=="number"&&r.push(i)}return r.chop()},d.prototype.concat=function(o){var e=new d(this.ops.slice());return o.ops.length>0&&(e.push(o.ops[0]),e.ops=e.ops.concat(o.ops.slice(1))),e},d.prototype.diff=function(o,e){if(this.ops===o.ops)return new d;var t=[this,o].map(function(n){return n.map(function(f){if(f.insert!=null)return typeof f.insert=="string"?f.insert:m;var i=n===o?"on":"with";throw new Error("diff() called "+i+" non-document")}).join("")}),a=new d,s=N(t[0],t[1],e),l=g.iterator(this.ops),r=g.iterator(o.ops);return s.forEach(function(n){for(var f=n[1].length;f>0;){var i=0;switch(n[0]){case N.INSERT:i=Math.min(r.peekLength(),f),a.push(r.next(i));break;case N.DELETE:i=Math.min(f,l.peekLength()),l.next(i),a.delete(i);break;case N.EQUAL:i=Math.min(l.peekLength(),r.peekLength(),f);var h=l.next(i),T=r.next(i);w(h.insert,T.insert)?a.retain(i,g.attributes.diff(h.attributes,T.attributes)):a.push(T).delete(i);break}f-=i}}),a.chop()},d.prototype.eachLine=function(o,e){e=e||`
`;for(var t=g.iterator(this.ops),a=new d,s=0;t.hasNext();){if(t.peekType()!=="insert")return;var l=t.peek(),r=g.length(l)-t.peekLength(),n=typeof l.insert=="string"?l.insert.indexOf(e,r)-r:-1;if(n<0)a.push(t.next());else if(n>0)a.push(t.next(n));else{if(o(a,t.next(1).attributes||{},s)===!1)return;s+=1,a=new d}}a.length()>0&&o(a,{},s)},d.prototype.transform=function(o,e){if(e=!!e,typeof o=="number")return this.transformPosition(o,e);for(var t=g.iterator(this.ops),a=g.iterator(o.ops),s=new d;t.hasNext()||a.hasNext();)if(t.peekType()==="insert"&&(e||a.peekType()!=="insert"))s.retain(g.length(t.next()));else if(a.peekType()==="insert")s.push(a.next());else{var l=Math.min(t.peekLength(),a.peekLength()),r=t.next(l),n=a.next(l);if(r.delete)continue;n.delete?s.push(n):s.retain(l,g.attributes.transform(r.attributes,n.attributes,e))}return s.chop()},d.prototype.transformPosition=function(o,e){e=!!e;for(var t=g.iterator(this.ops),a=0;t.hasNext()&&a<=o;){var s=t.peekLength(),l=t.peekType();if(t.next(),l==="delete"){o-=Math.min(s,o-a);continue}else l==="insert"&&(a<o||!e)&&(o+=s);a+=s}return o},x.exports=d},function(x,u){var c=Object.prototype.hasOwnProperty,N=Object.prototype.toString,w=Object.defineProperty,y=Object.getOwnPropertyDescriptor,g=function(t){return typeof Array.isArray=="function"?Array.isArray(t):N.call(t)==="[object Array]"},m=function(t){if(!t||N.call(t)!=="[object Object]")return!1;var a=c.call(t,"constructor"),s=t.constructor&&t.constructor.prototype&&c.call(t.constructor.prototype,"isPrototypeOf");if(t.constructor&&!a&&!s)return!1;var l;for(l in t);return typeof l>"u"||c.call(t,l)},d=function(t,a){w&&a.name==="__proto__"?w(t,a.name,{enumerable:!0,configurable:!0,value:a.newValue,writable:!0}):t[a.name]=a.newValue},o=function(t,a){if(a==="__proto__")if(c.call(t,a)){if(y)return y(t,a).value}else return;return t[a]};x.exports=function e(){var t,a,s,l,r,n,f=arguments[0],i=1,h=arguments.length,T=!1;for(typeof f=="boolean"&&(T=f,f=arguments[1]||{},i=2),(f==null||typeof f!="object"&&typeof f!="function")&&(f={});i<h;++i)if(t=arguments[i],t!=null)for(a in t)s=o(f,a),l=o(t,a),f!==l&&(T&&l&&(m(l)||(r=g(l)))?(r?(r=!1,n=s&&g(s)?s:[]):n=s&&m(s)?s:{},d(f,{name:a,newValue:e(T,n,l)})):typeof l<"u"&&d(f,{name:a,newValue:l}));return f}},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0}),u.default=u.BlockEmbed=u.bubbleFormats=void 0;var N=function(){function b(_,S){for(var L=0;L<S.length;L++){var R=S[L];R.enumerable=R.enumerable||!1,R.configurable=!0,"value"in R&&(R.writable=!0),Object.defineProperty(_,R.key,R)}}return function(_,S,L){return S&&b(_.prototype,S),L&&b(_,L),_}}(),w=function b(_,S,L){_===null&&(_=Function.prototype);var R=Object.getOwnPropertyDescriptor(_,S);if(R===void 0){var $=Object.getPrototypeOf(_);return $===null?void 0:b($,S,L)}else{if("value"in R)return R.value;var U=R.get;return U===void 0?void 0:U.call(L)}},y=c(3),g=f(y),m=c(2),d=f(m),o=c(0),e=f(o),t=c(16),a=f(t),s=c(6),l=f(s),r=c(7),n=f(r);function f(b){return b&&b.__esModule?b:{default:b}}function i(b,_){if(!(b instanceof _))throw new TypeError("Cannot call a class as a function")}function h(b,_){if(!b)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return _&&(typeof _=="object"||typeof _=="function")?_:b}function T(b,_){if(typeof _!="function"&&_!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof _);b.prototype=Object.create(_&&_.prototype,{constructor:{value:b,enumerable:!1,writable:!0,configurable:!0}}),_&&(Object.setPrototypeOf?Object.setPrototypeOf(b,_):b.__proto__=_)}var E=1,A=function(b){T(_,b);function _(){return i(this,_),h(this,(_.__proto__||Object.getPrototypeOf(_)).apply(this,arguments))}return N(_,[{key:"attach",value:function(){w(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"attach",this).call(this),this.attributes=new e.default.Attributor.Store(this.domNode)}},{key:"delta",value:function(){return new d.default().insert(this.value(),(0,g.default)(this.formats(),this.attributes.values()))}},{key:"format",value:function(L,R){var $=e.default.query(L,e.default.Scope.BLOCK_ATTRIBUTE);$!=null&&this.attributes.attribute($,R)}},{key:"formatAt",value:function(L,R,$,U){this.format($,U)}},{key:"insertAt",value:function(L,R,$){if(typeof R=="string"&&R.endsWith(`
`)){var U=e.default.create(P.blotName);this.parent.insertBefore(U,L===0?this:this.next),U.insertAt(0,R.slice(0,-1))}else w(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"insertAt",this).call(this,L,R,$)}}]),_}(e.default.Embed);A.scope=e.default.Scope.BLOCK_BLOT;var P=function(b){T(_,b);function _(S){i(this,_);var L=h(this,(_.__proto__||Object.getPrototypeOf(_)).call(this,S));return L.cache={},L}return N(_,[{key:"delta",value:function(){return this.cache.delta==null&&(this.cache.delta=this.descendants(e.default.Leaf).reduce(function(L,R){return R.length()===0?L:L.insert(R.value(),O(R))},new d.default).insert(`
`,O(this))),this.cache.delta}},{key:"deleteAt",value:function(L,R){w(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"deleteAt",this).call(this,L,R),this.cache={}}},{key:"formatAt",value:function(L,R,$,U){R<=0||(e.default.query($,e.default.Scope.BLOCK)?L+R===this.length()&&this.format($,U):w(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"formatAt",this).call(this,L,Math.min(R,this.length()-L-1),$,U),this.cache={})}},{key:"insertAt",value:function(L,R,$){if($!=null)return w(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"insertAt",this).call(this,L,R,$);if(R.length!==0){var U=R.split(`
`),Y=U.shift();Y.length>0&&(L<this.length()-1||this.children.tail==null?w(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"insertAt",this).call(this,Math.min(L,this.length()-1),Y):this.children.tail.insertAt(this.children.tail.length(),Y),this.cache={});var M=this;U.reduce(function(q,j){return M=M.split(q,!0),M.insertAt(0,j),j.length},L+Y.length)}}},{key:"insertBefore",value:function(L,R){var $=this.children.head;w(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"insertBefore",this).call(this,L,R),$ instanceof a.default&&$.remove(),this.cache={}}},{key:"length",value:function(){return this.cache.length==null&&(this.cache.length=w(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"length",this).call(this)+E),this.cache.length}},{key:"moveChildren",value:function(L,R){w(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"moveChildren",this).call(this,L,R),this.cache={}}},{key:"optimize",value:function(L){w(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"optimize",this).call(this,L),this.cache={}}},{key:"path",value:function(L){return w(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"path",this).call(this,L,!0)}},{key:"removeChild",value:function(L){w(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"removeChild",this).call(this,L),this.cache={}}},{key:"split",value:function(L){var R=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(R&&(L===0||L>=this.length()-E)){var $=this.clone();return L===0?(this.parent.insertBefore($,this),this):(this.parent.insertBefore($,this.next),$)}else{var U=w(_.prototype.__proto__||Object.getPrototypeOf(_.prototype),"split",this).call(this,L,R);return this.cache={},U}}}]),_}(e.default.Block);P.blotName="block",P.tagName="P",P.defaultChild="break",P.allowedChildren=[l.default,e.default.Embed,n.default];function O(b){var _=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return b==null||(typeof b.formats=="function"&&(_=(0,g.default)(_,b.formats())),b.parent==null||b.parent.blotName=="scroll"||b.parent.statics.scope!==b.statics.scope)?_:O(b.parent,_)}u.bubbleFormats=O,u.BlockEmbed=A,u.default=P},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0}),u.default=u.overload=u.expandConfig=void 0;var N=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(M){return typeof M}:function(M){return M&&typeof Symbol=="function"&&M.constructor===Symbol&&M!==Symbol.prototype?"symbol":typeof M},w=function(){function M(q,j){var D=[],B=!0,H=!1,F=void 0;try{for(var I=q[Symbol.iterator](),C;!(B=(C=I.next()).done)&&(D.push(C.value),!(j&&D.length===j));B=!0);}catch(z){H=!0,F=z}finally{try{!B&&I.return&&I.return()}finally{if(H)throw F}}return D}return function(q,j){if(Array.isArray(q))return q;if(Symbol.iterator in Object(q))return M(q,j);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),y=function(){function M(q,j){for(var D=0;D<j.length;D++){var B=j[D];B.enumerable=B.enumerable||!1,B.configurable=!0,"value"in B&&(B.writable=!0),Object.defineProperty(q,B.key,B)}}return function(q,j,D){return j&&M(q.prototype,j),D&&M(q,D),q}}();c(50);var g=c(2),m=O(g),d=c(14),o=O(d),e=c(8),t=O(e),a=c(9),s=O(a),l=c(0),r=O(l),n=c(15),f=O(n),i=c(3),h=O(i),T=c(10),E=O(T),A=c(34),P=O(A);function O(M){return M&&M.__esModule?M:{default:M}}function b(M,q,j){return q in M?Object.defineProperty(M,q,{value:j,enumerable:!0,configurable:!0,writable:!0}):M[q]=j,M}function _(M,q){if(!(M instanceof q))throw new TypeError("Cannot call a class as a function")}var S=(0,E.default)("quill"),L=function(){y(M,null,[{key:"debug",value:function(j){j===!0&&(j="log"),E.default.level(j)}},{key:"find",value:function(j){return j.__quill||r.default.find(j)}},{key:"import",value:function(j){return this.imports[j]==null&&S.error("Cannot import "+j+". Are you sure it was registered?"),this.imports[j]}},{key:"register",value:function(j,D){var B=this,H=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;if(typeof j!="string"){var F=j.attrName||j.blotName;typeof F=="string"?this.register("formats/"+F,j,D):Object.keys(j).forEach(function(I){B.register(I,j[I],D)})}else this.imports[j]!=null&&!H&&S.warn("Overwriting "+j+" with",D),this.imports[j]=D,(j.startsWith("blots/")||j.startsWith("formats/"))&&D.blotName!=="abstract"?r.default.register(D):j.startsWith("modules")&&typeof D.register=="function"&&D.register()}}]);function M(q){var j=this,D=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(_(this,M),this.options=R(q,D),this.container=this.options.container,this.container==null)return S.error("Invalid Quill container",q);this.options.debug&&M.debug(this.options.debug);var B=this.container.innerHTML.trim();this.container.classList.add("ql-container"),this.container.innerHTML="",this.container.__quill=this,this.root=this.addContainer("ql-editor"),this.root.classList.add("ql-blank"),this.root.setAttribute("data-gramm",!1),this.scrollingContainer=this.options.scrollingContainer||this.root,this.emitter=new t.default,this.scroll=r.default.create(this.root,{emitter:this.emitter,whitelist:this.options.formats}),this.editor=new o.default(this.scroll),this.selection=new f.default(this.scroll,this.emitter),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule("keyboard"),this.clipboard=this.theme.addModule("clipboard"),this.history=this.theme.addModule("history"),this.theme.init(),this.emitter.on(t.default.events.EDITOR_CHANGE,function(F){F===t.default.events.TEXT_CHANGE&&j.root.classList.toggle("ql-blank",j.editor.isBlank())}),this.emitter.on(t.default.events.SCROLL_UPDATE,function(F,I){var C=j.selection.lastRange,z=C&&C.length===0?C.index:void 0;$.call(j,function(){return j.editor.update(null,I,z)},F)});var H=this.clipboard.convert(`<div class='ql-editor' style="white-space: normal;">`+B+"<p><br></p></div>");this.setContents(H),this.history.clear(),this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder),this.options.readOnly&&this.disable()}return y(M,[{key:"addContainer",value:function(j){var D=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(typeof j=="string"){var B=j;j=document.createElement("div"),j.classList.add(B)}return this.container.insertBefore(j,D),j}},{key:"blur",value:function(){this.selection.setRange(null)}},{key:"deleteText",value:function(j,D,B){var H=this,F=U(j,D,B),I=w(F,4);return j=I[0],D=I[1],B=I[3],$.call(this,function(){return H.editor.deleteText(j,D)},B,j,-1*D)}},{key:"disable",value:function(){this.enable(!1)}},{key:"enable",value:function(){var j=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.scroll.enable(j),this.container.classList.toggle("ql-disabled",!j)}},{key:"focus",value:function(){var j=this.scrollingContainer.scrollTop;this.selection.focus(),this.scrollingContainer.scrollTop=j,this.scrollIntoView()}},{key:"format",value:function(j,D){var B=this,H=arguments.length>2&&arguments[2]!==void 0?arguments[2]:t.default.sources.API;return $.call(this,function(){var F=B.getSelection(!0),I=new m.default;if(F==null)return I;if(r.default.query(j,r.default.Scope.BLOCK))I=B.editor.formatLine(F.index,F.length,b({},j,D));else{if(F.length===0)return B.selection.format(j,D),I;I=B.editor.formatText(F.index,F.length,b({},j,D))}return B.setSelection(F,t.default.sources.SILENT),I},H)}},{key:"formatLine",value:function(j,D,B,H,F){var I=this,C=void 0,z=U(j,D,B,H,F),K=w(z,4);return j=K[0],D=K[1],C=K[2],F=K[3],$.call(this,function(){return I.editor.formatLine(j,D,C)},F,j,0)}},{key:"formatText",value:function(j,D,B,H,F){var I=this,C=void 0,z=U(j,D,B,H,F),K=w(z,4);return j=K[0],D=K[1],C=K[2],F=K[3],$.call(this,function(){return I.editor.formatText(j,D,C)},F,j,0)}},{key:"getBounds",value:function(j){var D=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,B=void 0;typeof j=="number"?B=this.selection.getBounds(j,D):B=this.selection.getBounds(j.index,j.length);var H=this.container.getBoundingClientRect();return{bottom:B.bottom-H.top,height:B.height,left:B.left-H.left,right:B.right-H.left,top:B.top-H.top,width:B.width}}},{key:"getContents",value:function(){var j=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,D=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.getLength()-j,B=U(j,D),H=w(B,2);return j=H[0],D=H[1],this.editor.getContents(j,D)}},{key:"getFormat",value:function(){var j=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.getSelection(!0),D=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return typeof j=="number"?this.editor.getFormat(j,D):this.editor.getFormat(j.index,j.length)}},{key:"getIndex",value:function(j){return j.offset(this.scroll)}},{key:"getLength",value:function(){return this.scroll.length()}},{key:"getLeaf",value:function(j){return this.scroll.leaf(j)}},{key:"getLine",value:function(j){return this.scroll.line(j)}},{key:"getLines",value:function(){var j=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,D=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE;return typeof j!="number"?this.scroll.lines(j.index,j.length):this.scroll.lines(j,D)}},{key:"getModule",value:function(j){return this.theme.modules[j]}},{key:"getSelection",value:function(){var j=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;return j&&this.focus(),this.update(),this.selection.getRange()[0]}},{key:"getText",value:function(){var j=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,D=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.getLength()-j,B=U(j,D),H=w(B,2);return j=H[0],D=H[1],this.editor.getText(j,D)}},{key:"hasFocus",value:function(){return this.selection.hasFocus()}},{key:"insertEmbed",value:function(j,D,B){var H=this,F=arguments.length>3&&arguments[3]!==void 0?arguments[3]:M.sources.API;return $.call(this,function(){return H.editor.insertEmbed(j,D,B)},F,j)}},{key:"insertText",value:function(j,D,B,H,F){var I=this,C=void 0,z=U(j,0,B,H,F),K=w(z,4);return j=K[0],C=K[2],F=K[3],$.call(this,function(){return I.editor.insertText(j,D,C)},F,j,D.length)}},{key:"isEnabled",value:function(){return!this.container.classList.contains("ql-disabled")}},{key:"off",value:function(){return this.emitter.off.apply(this.emitter,arguments)}},{key:"on",value:function(){return this.emitter.on.apply(this.emitter,arguments)}},{key:"once",value:function(){return this.emitter.once.apply(this.emitter,arguments)}},{key:"pasteHTML",value:function(j,D,B){this.clipboard.dangerouslyPasteHTML(j,D,B)}},{key:"removeFormat",value:function(j,D,B){var H=this,F=U(j,D,B),I=w(F,4);return j=I[0],D=I[1],B=I[3],$.call(this,function(){return H.editor.removeFormat(j,D)},B,j)}},{key:"scrollIntoView",value:function(){this.selection.scrollIntoView(this.scrollingContainer)}},{key:"setContents",value:function(j){var D=this,B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t.default.sources.API;return $.call(this,function(){j=new m.default(j);var H=D.getLength(),F=D.editor.deleteText(0,H),I=D.editor.applyDelta(j),C=I.ops[I.ops.length-1];C!=null&&typeof C.insert=="string"&&C.insert[C.insert.length-1]===`
`&&(D.editor.deleteText(D.getLength()-1,1),I.delete(1));var z=F.compose(I);return z},B)}},{key:"setSelection",value:function(j,D,B){if(j==null)this.selection.setRange(null,D||M.sources.API);else{var H=U(j,D,B),F=w(H,4);j=F[0],D=F[1],B=F[3],this.selection.setRange(new n.Range(j,D),B),B!==t.default.sources.SILENT&&this.selection.scrollIntoView(this.scrollingContainer)}}},{key:"setText",value:function(j){var D=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t.default.sources.API,B=new m.default().insert(j);return this.setContents(B,D)}},{key:"update",value:function(){var j=arguments.length>0&&arguments[0]!==void 0?arguments[0]:t.default.sources.USER,D=this.scroll.update(j);return this.selection.update(j),D}},{key:"updateContents",value:function(j){var D=this,B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t.default.sources.API;return $.call(this,function(){return j=new m.default(j),D.editor.applyDelta(j,B)},B,!0)}}]),M}();L.DEFAULTS={bounds:null,formats:null,modules:{},placeholder:"",readOnly:!1,scrollingContainer:null,strict:!0,theme:"default"},L.events=t.default.events,L.sources=t.default.sources,L.version="1.3.7",L.imports={delta:m.default,parchment:r.default,"core/module":s.default,"core/theme":P.default};function R(M,q){if(q=(0,h.default)(!0,{container:M,modules:{clipboard:!0,keyboard:!0,history:!0}},q),!q.theme||q.theme===L.DEFAULTS.theme)q.theme=P.default;else if(q.theme=L.import("themes/"+q.theme),q.theme==null)throw new Error("Invalid theme "+q.theme+". Did you register it?");var j=(0,h.default)(!0,{},q.theme.DEFAULTS);[j,q].forEach(function(H){H.modules=H.modules||{},Object.keys(H.modules).forEach(function(F){H.modules[F]===!0&&(H.modules[F]={})})});var D=Object.keys(j.modules).concat(Object.keys(q.modules)),B=D.reduce(function(H,F){var I=L.import("modules/"+F);return I==null?S.error("Cannot load "+F+" module. Are you sure you registered it?"):H[F]=I.DEFAULTS||{},H},{});return q.modules!=null&&q.modules.toolbar&&q.modules.toolbar.constructor!==Object&&(q.modules.toolbar={container:q.modules.toolbar}),q=(0,h.default)(!0,{},L.DEFAULTS,{modules:B},j,q),["bounds","container","scrollingContainer"].forEach(function(H){typeof q[H]=="string"&&(q[H]=document.querySelector(q[H]))}),q.modules=Object.keys(q.modules).reduce(function(H,F){return q.modules[F]&&(H[F]=q.modules[F]),H},{}),q}function $(M,q,j,D){if(this.options.strict&&!this.isEnabled()&&q===t.default.sources.USER)return new m.default;var B=j==null?null:this.getSelection(),H=this.editor.delta,F=M();if(B!=null&&(j===!0&&(j=B.index),D==null?B=Y(B,F,q):D!==0&&(B=Y(B,j,D,q)),this.setSelection(B,t.default.sources.SILENT)),F.length()>0){var I,C=[t.default.events.TEXT_CHANGE,F,H,q];if((I=this.emitter).emit.apply(I,[t.default.events.EDITOR_CHANGE].concat(C)),q!==t.default.sources.SILENT){var z;(z=this.emitter).emit.apply(z,C)}}return F}function U(M,q,j,D,B){var H={};return typeof M.index=="number"&&typeof M.length=="number"?typeof q!="number"?(B=D,D=j,j=q,q=M.length,M=M.index):(q=M.length,M=M.index):typeof q!="number"&&(B=D,D=j,j=q,q=0),(typeof j>"u"?"undefined":N(j))==="object"?(H=j,B=D):typeof j=="string"&&(D!=null?H[j]=D:B=j),B=B||t.default.sources.API,[M,q,H,B]}function Y(M,q,j,D){if(M==null)return null;var B=void 0,H=void 0;if(q instanceof m.default){var F=[M.index,M.index+M.length].map(function(K){return q.transformPosition(K,D!==t.default.sources.USER)}),I=w(F,2);B=I[0],H=I[1]}else{var C=[M.index,M.index+M.length].map(function(K){return K<q||K===q&&D===t.default.sources.USER?K:j>=0?K+j:Math.max(q,K+j)}),z=w(C,2);B=z[0],H=z[1]}return new n.Range(B,H-B)}u.expandConfig=R,u.overload=U,u.default=L},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=function(){function l(r,n){for(var f=0;f<n.length;f++){var i=n[f];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(r,i.key,i)}}return function(r,n,f){return n&&l(r.prototype,n),f&&l(r,f),r}}(),w=function l(r,n,f){r===null&&(r=Function.prototype);var i=Object.getOwnPropertyDescriptor(r,n);if(i===void 0){var h=Object.getPrototypeOf(r);return h===null?void 0:l(h,n,f)}else{if("value"in i)return i.value;var T=i.get;return T===void 0?void 0:T.call(f)}},y=c(7),g=o(y),m=c(0),d=o(m);function o(l){return l&&l.__esModule?l:{default:l}}function e(l,r){if(!(l instanceof r))throw new TypeError("Cannot call a class as a function")}function t(l,r){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r&&(typeof r=="object"||typeof r=="function")?r:l}function a(l,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof r);l.prototype=Object.create(r&&r.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(l,r):l.__proto__=r)}var s=function(l){a(r,l);function r(){return e(this,r),t(this,(r.__proto__||Object.getPrototypeOf(r)).apply(this,arguments))}return N(r,[{key:"formatAt",value:function(f,i,h,T){if(r.compare(this.statics.blotName,h)<0&&d.default.query(h,d.default.Scope.BLOT)){var E=this.isolate(f,i);T&&E.wrap(h,T)}else w(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"formatAt",this).call(this,f,i,h,T)}},{key:"optimize",value:function(f){if(w(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"optimize",this).call(this,f),this.parent instanceof r&&r.compare(this.statics.blotName,this.parent.statics.blotName)>0){var i=this.parent.isolate(this.offset(),this.length());this.moveChildren(i),i.wrap(this)}}}],[{key:"compare",value:function(f,i){var h=r.order.indexOf(f),T=r.order.indexOf(i);return h>=0||T>=0?h-T:f===i?0:f<i?-1:1}}]),r}(d.default.Inline);s.allowedChildren=[s,d.default.Embed,g.default],s.order=["cursor","inline","underline","strike","italic","bold","script","link","code"],u.default=s},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=c(0),w=y(N);function y(e){return e&&e.__esModule?e:{default:e}}function g(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function m(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function d(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var o=function(e){d(t,e);function t(){return g(this,t),m(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return t}(w.default.Text);u.default=o},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=function(){function n(f,i){for(var h=0;h<i.length;h++){var T=i[h];T.enumerable=T.enumerable||!1,T.configurable=!0,"value"in T&&(T.writable=!0),Object.defineProperty(f,T.key,T)}}return function(f,i,h){return i&&n(f.prototype,i),h&&n(f,h),f}}(),w=function n(f,i,h){f===null&&(f=Function.prototype);var T=Object.getOwnPropertyDescriptor(f,i);if(T===void 0){var E=Object.getPrototypeOf(f);return E===null?void 0:n(E,i,h)}else{if("value"in T)return T.value;var A=T.get;return A===void 0?void 0:A.call(h)}},y=c(54),g=o(y),m=c(10),d=o(m);function o(n){return n&&n.__esModule?n:{default:n}}function e(n,f){if(!(n instanceof f))throw new TypeError("Cannot call a class as a function")}function t(n,f){if(!n)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return f&&(typeof f=="object"||typeof f=="function")?f:n}function a(n,f){if(typeof f!="function"&&f!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof f);n.prototype=Object.create(f&&f.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),f&&(Object.setPrototypeOf?Object.setPrototypeOf(n,f):n.__proto__=f)}var s=(0,d.default)("quill:events"),l=["selectionchange","mousedown","mouseup","click"];l.forEach(function(n){document.addEventListener(n,function(){for(var f=arguments.length,i=Array(f),h=0;h<f;h++)i[h]=arguments[h];[].slice.call(document.querySelectorAll(".ql-container")).forEach(function(T){if(T.__quill&&T.__quill.emitter){var E;(E=T.__quill.emitter).handleDOM.apply(E,i)}})})});var r=function(n){a(f,n);function f(){e(this,f);var i=t(this,(f.__proto__||Object.getPrototypeOf(f)).call(this));return i.listeners={},i.on("error",s.error),i}return N(f,[{key:"emit",value:function(){s.log.apply(s,arguments),w(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"emit",this).apply(this,arguments)}},{key:"handleDOM",value:function(h){for(var T=arguments.length,E=Array(T>1?T-1:0),A=1;A<T;A++)E[A-1]=arguments[A];(this.listeners[h.type]||[]).forEach(function(P){var O=P.node,b=P.handler;(h.target===O||O.contains(h.target))&&b.apply(void 0,[h].concat(E))})}},{key:"listenDOM",value:function(h,T,E){this.listeners[h]||(this.listeners[h]=[]),this.listeners[h].push({node:T,handler:E})}}]),f}(g.default);r.events={EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change"},r.sources={API:"api",SILENT:"silent",USER:"user"},u.default=r},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});function N(y,g){if(!(y instanceof g))throw new TypeError("Cannot call a class as a function")}var w=function y(g){var m=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};N(this,y),this.quill=g,this.options=m};w.DEFAULTS={},u.default=w},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=["error","warn","log","info"],w="warn";function y(m){if(N.indexOf(m)<=N.indexOf(w)){for(var d,o=arguments.length,e=Array(o>1?o-1:0),t=1;t<o;t++)e[t-1]=arguments[t];(d=console)[m].apply(d,e)}}function g(m){return N.reduce(function(d,o){return d[o]=y.bind(console,o,m),d},{})}y.level=g.level=function(m){w=m},u.default=g},function(x,u,c){var N=Array.prototype.slice,w=c(52),y=c(53),g=x.exports=function(e,t,a){return a||(a={}),e===t?!0:e instanceof Date&&t instanceof Date?e.getTime()===t.getTime():!e||!t||typeof e!="object"&&typeof t!="object"?a.strict?e===t:e==t:o(e,t,a)};function m(e){return e==null}function d(e){return!(!e||typeof e!="object"||typeof e.length!="number"||typeof e.copy!="function"||typeof e.slice!="function"||e.length>0&&typeof e[0]!="number")}function o(e,t,a){var s,l;if(m(e)||m(t)||e.prototype!==t.prototype)return!1;if(y(e))return y(t)?(e=N.call(e),t=N.call(t),g(e,t,a)):!1;if(d(e)){if(!d(t)||e.length!==t.length)return!1;for(s=0;s<e.length;s++)if(e[s]!==t[s])return!1;return!0}try{var r=w(e),n=w(t)}catch{return!1}if(r.length!=n.length)return!1;for(r.sort(),n.sort(),s=r.length-1;s>=0;s--)if(r[s]!=n[s])return!1;for(s=r.length-1;s>=0;s--)if(l=r[s],!g(e[l],t[l],a))return!1;return typeof e==typeof t}},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=c(1),w=function(){function y(g,m,d){d===void 0&&(d={}),this.attrName=g,this.keyName=m;var o=N.Scope.TYPE&N.Scope.ATTRIBUTE;d.scope!=null?this.scope=d.scope&N.Scope.LEVEL|o:this.scope=N.Scope.ATTRIBUTE,d.whitelist!=null&&(this.whitelist=d.whitelist)}return y.keys=function(g){return[].map.call(g.attributes,function(m){return m.name})},y.prototype.add=function(g,m){return this.canAdd(g,m)?(g.setAttribute(this.keyName,m),!0):!1},y.prototype.canAdd=function(g,m){var d=N.query(g,N.Scope.BLOT&(this.scope|N.Scope.TYPE));return d==null?!1:this.whitelist==null?!0:typeof m=="string"?this.whitelist.indexOf(m.replace(/["']/g,""))>-1:this.whitelist.indexOf(m)>-1},y.prototype.remove=function(g){g.removeAttribute(this.keyName)},y.prototype.value=function(g){var m=g.getAttribute(this.keyName);return this.canAdd(g,m)&&m?m:""},y}();u.default=w},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0}),u.default=u.Code=void 0;var N=function(){function A(P,O){var b=[],_=!0,S=!1,L=void 0;try{for(var R=P[Symbol.iterator](),$;!(_=($=R.next()).done)&&(b.push($.value),!(O&&b.length===O));_=!0);}catch(U){S=!0,L=U}finally{try{!_&&R.return&&R.return()}finally{if(S)throw L}}return b}return function(P,O){if(Array.isArray(P))return P;if(Symbol.iterator in Object(P))return A(P,O);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),w=function(){function A(P,O){for(var b=0;b<O.length;b++){var _=O[b];_.enumerable=_.enumerable||!1,_.configurable=!0,"value"in _&&(_.writable=!0),Object.defineProperty(P,_.key,_)}}return function(P,O,b){return O&&A(P.prototype,O),b&&A(P,b),P}}(),y=function A(P,O,b){P===null&&(P=Function.prototype);var _=Object.getOwnPropertyDescriptor(P,O);if(_===void 0){var S=Object.getPrototypeOf(P);return S===null?void 0:A(S,O,b)}else{if("value"in _)return _.value;var L=_.get;return L===void 0?void 0:L.call(b)}},g=c(2),m=n(g),d=c(0),o=n(d),e=c(4),t=n(e),a=c(6),s=n(a),l=c(7),r=n(l);function n(A){return A&&A.__esModule?A:{default:A}}function f(A,P){if(!(A instanceof P))throw new TypeError("Cannot call a class as a function")}function i(A,P){if(!A)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return P&&(typeof P=="object"||typeof P=="function")?P:A}function h(A,P){if(typeof P!="function"&&P!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof P);A.prototype=Object.create(P&&P.prototype,{constructor:{value:A,enumerable:!1,writable:!0,configurable:!0}}),P&&(Object.setPrototypeOf?Object.setPrototypeOf(A,P):A.__proto__=P)}var T=function(A){h(P,A);function P(){return f(this,P),i(this,(P.__proto__||Object.getPrototypeOf(P)).apply(this,arguments))}return P}(s.default);T.blotName="code",T.tagName="CODE";var E=function(A){h(P,A);function P(){return f(this,P),i(this,(P.__proto__||Object.getPrototypeOf(P)).apply(this,arguments))}return w(P,[{key:"delta",value:function(){var b=this,_=this.domNode.textContent;return _.endsWith(`
`)&&(_=_.slice(0,-1)),_.split(`
`).reduce(function(S,L){return S.insert(L).insert(`
`,b.formats())},new m.default)}},{key:"format",value:function(b,_){if(!(b===this.statics.blotName&&_)){var S=this.descendant(r.default,this.length()-1),L=N(S,1),R=L[0];R!=null&&R.deleteAt(R.length()-1,1),y(P.prototype.__proto__||Object.getPrototypeOf(P.prototype),"format",this).call(this,b,_)}}},{key:"formatAt",value:function(b,_,S,L){if(_!==0&&!(o.default.query(S,o.default.Scope.BLOCK)==null||S===this.statics.blotName&&L===this.statics.formats(this.domNode))){var R=this.newlineIndex(b);if(!(R<0||R>=b+_)){var $=this.newlineIndex(b,!0)+1,U=R-$+1,Y=this.isolate($,U),M=Y.next;Y.format(S,L),M instanceof P&&M.formatAt(0,b-$+_-U,S,L)}}}},{key:"insertAt",value:function(b,_,S){if(S==null){var L=this.descendant(r.default,b),R=N(L,2),$=R[0],U=R[1];$.insertAt(U,_)}}},{key:"length",value:function(){var b=this.domNode.textContent.length;return this.domNode.textContent.endsWith(`
`)?b:b+1}},{key:"newlineIndex",value:function(b){var _=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(_)return this.domNode.textContent.slice(0,b).lastIndexOf(`
`);var S=this.domNode.textContent.slice(b).indexOf(`
`);return S>-1?b+S:-1}},{key:"optimize",value:function(b){this.domNode.textContent.endsWith(`
`)||this.appendChild(o.default.create("text",`
`)),y(P.prototype.__proto__||Object.getPrototypeOf(P.prototype),"optimize",this).call(this,b);var _=this.next;_!=null&&_.prev===this&&_.statics.blotName===this.statics.blotName&&this.statics.formats(this.domNode)===_.statics.formats(_.domNode)&&(_.optimize(b),_.moveChildren(this),_.remove())}},{key:"replace",value:function(b){y(P.prototype.__proto__||Object.getPrototypeOf(P.prototype),"replace",this).call(this,b),[].slice.call(this.domNode.querySelectorAll("*")).forEach(function(_){var S=o.default.find(_);S==null?_.parentNode.removeChild(_):S instanceof o.default.Embed?S.remove():S.unwrap()})}}],[{key:"create",value:function(b){var _=y(P.__proto__||Object.getPrototypeOf(P),"create",this).call(this,b);return _.setAttribute("spellcheck",!1),_}},{key:"formats",value:function(){return!0}}]),P}(t.default);E.blotName="code-block",E.tagName="PRE",E.TAB="  ",u.Code=T,u.default=E},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(M){return typeof M}:function(M){return M&&typeof Symbol=="function"&&M.constructor===Symbol&&M!==Symbol.prototype?"symbol":typeof M},w=function(){function M(q,j){var D=[],B=!0,H=!1,F=void 0;try{for(var I=q[Symbol.iterator](),C;!(B=(C=I.next()).done)&&(D.push(C.value),!(j&&D.length===j));B=!0);}catch(z){H=!0,F=z}finally{try{!B&&I.return&&I.return()}finally{if(H)throw F}}return D}return function(q,j){if(Array.isArray(q))return q;if(Symbol.iterator in Object(q))return M(q,j);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),y=function(){function M(q,j){for(var D=0;D<j.length;D++){var B=j[D];B.enumerable=B.enumerable||!1,B.configurable=!0,"value"in B&&(B.writable=!0),Object.defineProperty(q,B.key,B)}}return function(q,j,D){return j&&M(q.prototype,j),D&&M(q,D),q}}(),g=c(2),m=_(g),d=c(20),o=_(d),e=c(0),t=_(e),a=c(13),s=_(a),l=c(24),r=_(l),n=c(4),f=_(n),i=c(16),h=_(i),T=c(21),E=_(T),A=c(11),P=_(A),O=c(3),b=_(O);function _(M){return M&&M.__esModule?M:{default:M}}function S(M,q,j){return q in M?Object.defineProperty(M,q,{value:j,enumerable:!0,configurable:!0,writable:!0}):M[q]=j,M}function L(M,q){if(!(M instanceof q))throw new TypeError("Cannot call a class as a function")}var R=/^[ -~]*$/,$=function(){function M(q){L(this,M),this.scroll=q,this.delta=this.getDelta()}return y(M,[{key:"applyDelta",value:function(j){var D=this,B=!1;this.scroll.update();var H=this.scroll.length();return this.scroll.batchStart(),j=Y(j),j.reduce(function(F,I){var C=I.retain||I.delete||I.insert.length||1,z=I.attributes||{};if(I.insert!=null){if(typeof I.insert=="string"){var K=I.insert;K.endsWith(`
`)&&B&&(B=!1,K=K.slice(0,-1)),F>=H&&!K.endsWith(`
`)&&(B=!0),D.scroll.insertAt(F,K);var Z=D.scroll.line(F),ee=w(Z,2),V=ee[0],Q=ee[1],ie=(0,b.default)({},(0,n.bubbleFormats)(V));if(V instanceof f.default){var se=V.descendant(t.default.Leaf,Q),he=w(se,1),ue=he[0];ie=(0,b.default)(ie,(0,n.bubbleFormats)(ue))}z=o.default.attributes.diff(ie,z)||{}}else if(N(I.insert)==="object"){var G=Object.keys(I.insert)[0];if(G==null)return F;D.scroll.insertAt(F,G,I.insert[G])}H+=C}return Object.keys(z).forEach(function(W){D.scroll.formatAt(F,C,W,z[W])}),F+C},0),j.reduce(function(F,I){return typeof I.delete=="number"?(D.scroll.deleteAt(F,I.delete),F):F+(I.retain||I.insert.length||1)},0),this.scroll.batchEnd(),this.update(j)}},{key:"deleteText",value:function(j,D){return this.scroll.deleteAt(j,D),this.update(new m.default().retain(j).delete(D))}},{key:"formatLine",value:function(j,D){var B=this,H=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return this.scroll.update(),Object.keys(H).forEach(function(F){if(!(B.scroll.whitelist!=null&&!B.scroll.whitelist[F])){var I=B.scroll.lines(j,Math.max(D,1)),C=D;I.forEach(function(z){var K=z.length();if(!(z instanceof s.default))z.format(F,H[F]);else{var Z=j-z.offset(B.scroll),ee=z.newlineIndex(Z+C)-Z+1;z.formatAt(Z,ee,F,H[F])}C-=K})}}),this.scroll.optimize(),this.update(new m.default().retain(j).retain(D,(0,E.default)(H)))}},{key:"formatText",value:function(j,D){var B=this,H=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return Object.keys(H).forEach(function(F){B.scroll.formatAt(j,D,F,H[F])}),this.update(new m.default().retain(j).retain(D,(0,E.default)(H)))}},{key:"getContents",value:function(j,D){return this.delta.slice(j,j+D)}},{key:"getDelta",value:function(){return this.scroll.lines().reduce(function(j,D){return j.concat(D.delta())},new m.default)}},{key:"getFormat",value:function(j){var D=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,B=[],H=[];D===0?this.scroll.path(j).forEach(function(I){var C=w(I,1),z=C[0];z instanceof f.default?B.push(z):z instanceof t.default.Leaf&&H.push(z)}):(B=this.scroll.lines(j,D),H=this.scroll.descendants(t.default.Leaf,j,D));var F=[B,H].map(function(I){if(I.length===0)return{};for(var C=(0,n.bubbleFormats)(I.shift());Object.keys(C).length>0;){var z=I.shift();if(z==null)return C;C=U((0,n.bubbleFormats)(z),C)}return C});return b.default.apply(b.default,F)}},{key:"getText",value:function(j,D){return this.getContents(j,D).filter(function(B){return typeof B.insert=="string"}).map(function(B){return B.insert}).join("")}},{key:"insertEmbed",value:function(j,D,B){return this.scroll.insertAt(j,D,B),this.update(new m.default().retain(j).insert(S({},D,B)))}},{key:"insertText",value:function(j,D){var B=this,H=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return D=D.replace(/\r\n/g,`
`).replace(/\r/g,`
`),this.scroll.insertAt(j,D),Object.keys(H).forEach(function(F){B.scroll.formatAt(j,D.length,F,H[F])}),this.update(new m.default().retain(j).insert(D,(0,E.default)(H)))}},{key:"isBlank",value:function(){if(this.scroll.children.length==0)return!0;if(this.scroll.children.length>1)return!1;var j=this.scroll.children.head;return j.statics.blotName!==f.default.blotName||j.children.length>1?!1:j.children.head instanceof h.default}},{key:"removeFormat",value:function(j,D){var B=this.getText(j,D),H=this.scroll.line(j+D),F=w(H,2),I=F[0],C=F[1],z=0,K=new m.default;I!=null&&(I instanceof s.default?z=I.newlineIndex(C)-C+1:z=I.length()-C,K=I.delta().slice(C,C+z-1).insert(`
`));var Z=this.getContents(j,D+z),ee=Z.diff(new m.default().insert(B).concat(K)),V=new m.default().retain(j).concat(ee);return this.applyDelta(V)}},{key:"update",value:function(j){var D=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],B=arguments.length>2&&arguments[2]!==void 0?arguments[2]:void 0,H=this.delta;if(D.length===1&&D[0].type==="characterData"&&D[0].target.data.match(R)&&t.default.find(D[0].target)){var F=t.default.find(D[0].target),I=(0,n.bubbleFormats)(F),C=F.offset(this.scroll),z=D[0].oldValue.replace(r.default.CONTENTS,""),K=new m.default().insert(z),Z=new m.default().insert(F.value()),ee=new m.default().retain(C).concat(K.diff(Z,B));j=ee.reduce(function(V,Q){return Q.insert?V.insert(Q.insert,I):V.push(Q)},new m.default),this.delta=H.compose(j)}else this.delta=this.getDelta(),(!j||!(0,P.default)(H.compose(j),this.delta))&&(j=H.diff(this.delta,B));return j}}]),M}();function U(M,q){return Object.keys(q).reduce(function(j,D){return M[D]==null||(q[D]===M[D]?j[D]=q[D]:Array.isArray(q[D])?q[D].indexOf(M[D])<0&&(j[D]=q[D].concat([M[D]])):j[D]=[q[D],M[D]]),j},{})}function Y(M){return M.reduce(function(q,j){if(j.insert===1){var D=(0,E.default)(j.attributes);return delete D.image,q.insert({image:j.attributes.image},D)}if(j.attributes!=null&&(j.attributes.list===!0||j.attributes.bullet===!0)&&(j=(0,E.default)(j),j.attributes.list?j.attributes.list="ordered":(j.attributes.list="bullet",delete j.attributes.bullet)),typeof j.insert=="string"){var B=j.insert.replace(/\r\n/g,`
`).replace(/\r/g,`
`);return q.insert(B,j.attributes)}return q.push(j)},new m.default)}u.default=$},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0}),u.default=u.Range=void 0;var N=function(){function A(P,O){var b=[],_=!0,S=!1,L=void 0;try{for(var R=P[Symbol.iterator](),$;!(_=($=R.next()).done)&&(b.push($.value),!(O&&b.length===O));_=!0);}catch(U){S=!0,L=U}finally{try{!_&&R.return&&R.return()}finally{if(S)throw L}}return b}return function(P,O){if(Array.isArray(P))return P;if(Symbol.iterator in Object(P))return A(P,O);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),w=function(){function A(P,O){for(var b=0;b<O.length;b++){var _=O[b];_.enumerable=_.enumerable||!1,_.configurable=!0,"value"in _&&(_.writable=!0),Object.defineProperty(P,_.key,_)}}return function(P,O,b){return O&&A(P.prototype,O),b&&A(P,b),P}}(),y=c(0),g=r(y),m=c(21),d=r(m),o=c(11),e=r(o),t=c(8),a=r(t),s=c(10),l=r(s);function r(A){return A&&A.__esModule?A:{default:A}}function n(A){if(Array.isArray(A)){for(var P=0,O=Array(A.length);P<A.length;P++)O[P]=A[P];return O}else return Array.from(A)}function f(A,P){if(!(A instanceof P))throw new TypeError("Cannot call a class as a function")}var i=(0,l.default)("quill:selection"),h=function A(P){var O=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;f(this,A),this.index=P,this.length=O},T=function(){function A(P,O){var b=this;f(this,A),this.emitter=O,this.scroll=P,this.composing=!1,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=g.default.create("cursor",this),this.lastRange=this.savedRange=new h(0,0),this.handleComposition(),this.handleDragging(),this.emitter.listenDOM("selectionchange",document,function(){b.mouseDown||setTimeout(b.update.bind(b,a.default.sources.USER),1)}),this.emitter.on(a.default.events.EDITOR_CHANGE,function(_,S){_===a.default.events.TEXT_CHANGE&&S.length()>0&&b.update(a.default.sources.SILENT)}),this.emitter.on(a.default.events.SCROLL_BEFORE_UPDATE,function(){if(b.hasFocus()){var _=b.getNativeRange();_!=null&&_.start.node!==b.cursor.textNode&&b.emitter.once(a.default.events.SCROLL_UPDATE,function(){try{b.setNativeRange(_.start.node,_.start.offset,_.end.node,_.end.offset)}catch{}})}}),this.emitter.on(a.default.events.SCROLL_OPTIMIZE,function(_,S){if(S.range){var L=S.range,R=L.startNode,$=L.startOffset,U=L.endNode,Y=L.endOffset;b.setNativeRange(R,$,U,Y)}}),this.update(a.default.sources.SILENT)}return w(A,[{key:"handleComposition",value:function(){var O=this;this.root.addEventListener("compositionstart",function(){O.composing=!0}),this.root.addEventListener("compositionend",function(){if(O.composing=!1,O.cursor.parent){var b=O.cursor.restore();if(!b)return;setTimeout(function(){O.setNativeRange(b.startNode,b.startOffset,b.endNode,b.endOffset)},1)}})}},{key:"handleDragging",value:function(){var O=this;this.emitter.listenDOM("mousedown",document.body,function(){O.mouseDown=!0}),this.emitter.listenDOM("mouseup",document.body,function(){O.mouseDown=!1,O.update(a.default.sources.USER)})}},{key:"focus",value:function(){this.hasFocus()||(this.root.focus(),this.setRange(this.savedRange))}},{key:"format",value:function(O,b){if(!(this.scroll.whitelist!=null&&!this.scroll.whitelist[O])){this.scroll.update();var _=this.getNativeRange();if(!(_==null||!_.native.collapsed||g.default.query(O,g.default.Scope.BLOCK))){if(_.start.node!==this.cursor.textNode){var S=g.default.find(_.start.node,!1);if(S==null)return;if(S instanceof g.default.Leaf){var L=S.split(_.start.offset);S.parent.insertBefore(this.cursor,L)}else S.insertBefore(this.cursor,_.start.node);this.cursor.attach()}this.cursor.format(O,b),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}}}},{key:"getBounds",value:function(O){var b=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,_=this.scroll.length();O=Math.min(O,_-1),b=Math.min(O+b,_-1)-O;var S=void 0,L=this.scroll.leaf(O),R=N(L,2),$=R[0],U=R[1];if($==null)return null;var Y=$.position(U,!0),M=N(Y,2);S=M[0],U=M[1];var q=document.createRange();if(b>0){q.setStart(S,U);var j=this.scroll.leaf(O+b),D=N(j,2);if($=D[0],U=D[1],$==null)return null;var B=$.position(U,!0),H=N(B,2);return S=H[0],U=H[1],q.setEnd(S,U),q.getBoundingClientRect()}else{var F="left",I=void 0;return S instanceof Text?(U<S.data.length?(q.setStart(S,U),q.setEnd(S,U+1)):(q.setStart(S,U-1),q.setEnd(S,U),F="right"),I=q.getBoundingClientRect()):(I=$.domNode.getBoundingClientRect(),U>0&&(F="right")),{bottom:I.top+I.height,height:I.height,left:I[F],right:I[F],top:I.top,width:0}}}},{key:"getNativeRange",value:function(){var O=document.getSelection();if(O==null||O.rangeCount<=0)return null;var b=O.getRangeAt(0);if(b==null)return null;var _=this.normalizeNative(b);return i.info("getNativeRange",_),_}},{key:"getRange",value:function(){var O=this.getNativeRange();if(O==null)return[null,null];var b=this.normalizedToRange(O);return[b,O]}},{key:"hasFocus",value:function(){return document.activeElement===this.root}},{key:"normalizedToRange",value:function(O){var b=this,_=[[O.start.node,O.start.offset]];O.native.collapsed||_.push([O.end.node,O.end.offset]);var S=_.map(function($){var U=N($,2),Y=U[0],M=U[1],q=g.default.find(Y,!0),j=q.offset(b.scroll);return M===0?j:q instanceof g.default.Container?j+q.length():j+q.index(Y,M)}),L=Math.min(Math.max.apply(Math,n(S)),this.scroll.length()-1),R=Math.min.apply(Math,[L].concat(n(S)));return new h(R,L-R)}},{key:"normalizeNative",value:function(O){if(!E(this.root,O.startContainer)||!O.collapsed&&!E(this.root,O.endContainer))return null;var b={start:{node:O.startContainer,offset:O.startOffset},end:{node:O.endContainer,offset:O.endOffset},native:O};return[b.start,b.end].forEach(function(_){for(var S=_.node,L=_.offset;!(S instanceof Text)&&S.childNodes.length>0;)if(S.childNodes.length>L)S=S.childNodes[L],L=0;else if(S.childNodes.length===L)S=S.lastChild,L=S instanceof Text?S.data.length:S.childNodes.length+1;else break;_.node=S,_.offset=L}),b}},{key:"rangeToNative",value:function(O){var b=this,_=O.collapsed?[O.index]:[O.index,O.index+O.length],S=[],L=this.scroll.length();return _.forEach(function(R,$){R=Math.min(L-1,R);var U=void 0,Y=b.scroll.leaf(R),M=N(Y,2),q=M[0],j=M[1],D=q.position(j,$!==0),B=N(D,2);U=B[0],j=B[1],S.push(U,j)}),S.length<2&&(S=S.concat(S)),S}},{key:"scrollIntoView",value:function(O){var b=this.lastRange;if(b!=null){var _=this.getBounds(b.index,b.length);if(_!=null){var S=this.scroll.length()-1,L=this.scroll.line(Math.min(b.index,S)),R=N(L,1),$=R[0],U=$;if(b.length>0){var Y=this.scroll.line(Math.min(b.index+b.length,S)),M=N(Y,1);U=M[0]}if(!($==null||U==null)){var q=O.getBoundingClientRect();_.top<q.top?O.scrollTop-=q.top-_.top:_.bottom>q.bottom&&(O.scrollTop+=_.bottom-q.bottom)}}}}},{key:"setNativeRange",value:function(O,b){var _=arguments.length>2&&arguments[2]!==void 0?arguments[2]:O,S=arguments.length>3&&arguments[3]!==void 0?arguments[3]:b,L=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1;if(i.info("setNativeRange",O,b,_,S),!(O!=null&&(this.root.parentNode==null||O.parentNode==null||_.parentNode==null))){var R=document.getSelection();if(R!=null)if(O!=null){this.hasFocus()||this.root.focus();var $=(this.getNativeRange()||{}).native;if($==null||L||O!==$.startContainer||b!==$.startOffset||_!==$.endContainer||S!==$.endOffset){O.tagName=="BR"&&(b=[].indexOf.call(O.parentNode.childNodes,O),O=O.parentNode),_.tagName=="BR"&&(S=[].indexOf.call(_.parentNode.childNodes,_),_=_.parentNode);var U=document.createRange();U.setStart(O,b),U.setEnd(_,S),R.removeAllRanges(),R.addRange(U)}}else R.removeAllRanges(),this.root.blur(),document.body.focus()}}},{key:"setRange",value:function(O){var b=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,_=arguments.length>2&&arguments[2]!==void 0?arguments[2]:a.default.sources.API;if(typeof b=="string"&&(_=b,b=!1),i.info("setRange",O),O!=null){var S=this.rangeToNative(O);this.setNativeRange.apply(this,n(S).concat([b]))}else this.setNativeRange(null);this.update(_)}},{key:"update",value:function(){var O=arguments.length>0&&arguments[0]!==void 0?arguments[0]:a.default.sources.USER,b=this.lastRange,_=this.getRange(),S=N(_,2),L=S[0],R=S[1];if(this.lastRange=L,this.lastRange!=null&&(this.savedRange=this.lastRange),!(0,e.default)(b,this.lastRange)){var $;!this.composing&&R!=null&&R.native.collapsed&&R.start.node!==this.cursor.textNode&&this.cursor.restore();var U=[a.default.events.SELECTION_CHANGE,(0,d.default)(this.lastRange),(0,d.default)(b),O];if(($=this.emitter).emit.apply($,[a.default.events.EDITOR_CHANGE].concat(U)),O!==a.default.sources.SILENT){var Y;(Y=this.emitter).emit.apply(Y,U)}}}}]),A}();function E(A,P){try{P.parentNode}catch{return!1}return P instanceof Text&&(P=P.parentNode),A.contains(P)}u.Range=h,u.default=T},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=function(){function a(s,l){for(var r=0;r<l.length;r++){var n=l[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(s,n.key,n)}}return function(s,l,r){return l&&a(s.prototype,l),r&&a(s,r),s}}(),w=function a(s,l,r){s===null&&(s=Function.prototype);var n=Object.getOwnPropertyDescriptor(s,l);if(n===void 0){var f=Object.getPrototypeOf(s);return f===null?void 0:a(f,l,r)}else{if("value"in n)return n.value;var i=n.get;return i===void 0?void 0:i.call(r)}},y=c(0),g=m(y);function m(a){return a&&a.__esModule?a:{default:a}}function d(a,s){if(!(a instanceof s))throw new TypeError("Cannot call a class as a function")}function o(a,s){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s&&(typeof s=="object"||typeof s=="function")?s:a}function e(a,s){if(typeof s!="function"&&s!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof s);a.prototype=Object.create(s&&s.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),s&&(Object.setPrototypeOf?Object.setPrototypeOf(a,s):a.__proto__=s)}var t=function(a){e(s,a);function s(){return d(this,s),o(this,(s.__proto__||Object.getPrototypeOf(s)).apply(this,arguments))}return N(s,[{key:"insertInto",value:function(r,n){r.children.length===0?w(s.prototype.__proto__||Object.getPrototypeOf(s.prototype),"insertInto",this).call(this,r,n):this.remove()}},{key:"length",value:function(){return 0}},{key:"value",value:function(){return""}}],[{key:"value",value:function(){}}]),s}(g.default.Embed);t.blotName="break",t.tagName="BR",u.default=t},function(x,u,c){var N=this&&this.__extends||function(){var o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var a in t)t.hasOwnProperty(a)&&(e[a]=t[a])};return function(e,t){o(e,t);function a(){this.constructor=e}e.prototype=t===null?Object.create(t):(a.prototype=t.prototype,new a)}}();Object.defineProperty(u,"__esModule",{value:!0});var w=c(44),y=c(30),g=c(1),m=function(o){N(e,o);function e(t){var a=o.call(this,t)||this;return a.build(),a}return e.prototype.appendChild=function(t){this.insertBefore(t)},e.prototype.attach=function(){o.prototype.attach.call(this),this.children.forEach(function(t){t.attach()})},e.prototype.build=function(){var t=this;this.children=new w.default,[].slice.call(this.domNode.childNodes).reverse().forEach(function(a){try{var s=d(a);t.insertBefore(s,t.children.head||void 0)}catch(l){if(l instanceof g.ParchmentError)return;throw l}})},e.prototype.deleteAt=function(t,a){if(t===0&&a===this.length())return this.remove();this.children.forEachAt(t,a,function(s,l,r){s.deleteAt(l,r)})},e.prototype.descendant=function(t,a){var s=this.children.find(a),l=s[0],r=s[1];return t.blotName==null&&t(l)||t.blotName!=null&&l instanceof t?[l,r]:l instanceof e?l.descendant(t,r):[null,-1]},e.prototype.descendants=function(t,a,s){a===void 0&&(a=0),s===void 0&&(s=Number.MAX_VALUE);var l=[],r=s;return this.children.forEachAt(a,s,function(n,f,i){(t.blotName==null&&t(n)||t.blotName!=null&&n instanceof t)&&l.push(n),n instanceof e&&(l=l.concat(n.descendants(t,f,r))),r-=i}),l},e.prototype.detach=function(){this.children.forEach(function(t){t.detach()}),o.prototype.detach.call(this)},e.prototype.formatAt=function(t,a,s,l){this.children.forEachAt(t,a,function(r,n,f){r.formatAt(n,f,s,l)})},e.prototype.insertAt=function(t,a,s){var l=this.children.find(t),r=l[0],n=l[1];if(r)r.insertAt(n,a,s);else{var f=s==null?g.create("text",a):g.create(a,s);this.appendChild(f)}},e.prototype.insertBefore=function(t,a){if(this.statics.allowedChildren!=null&&!this.statics.allowedChildren.some(function(s){return t instanceof s}))throw new g.ParchmentError("Cannot insert "+t.statics.blotName+" into "+this.statics.blotName);t.insertInto(this,a)},e.prototype.length=function(){return this.children.reduce(function(t,a){return t+a.length()},0)},e.prototype.moveChildren=function(t,a){this.children.forEach(function(s){t.insertBefore(s,a)})},e.prototype.optimize=function(t){if(o.prototype.optimize.call(this,t),this.children.length===0)if(this.statics.defaultChild!=null){var a=g.create(this.statics.defaultChild);this.appendChild(a),a.optimize(t)}else this.remove()},e.prototype.path=function(t,a){a===void 0&&(a=!1);var s=this.children.find(t,a),l=s[0],r=s[1],n=[[this,t]];return l instanceof e?n.concat(l.path(r,a)):(l!=null&&n.push([l,r]),n)},e.prototype.removeChild=function(t){this.children.remove(t)},e.prototype.replace=function(t){t instanceof e&&t.moveChildren(this),o.prototype.replace.call(this,t)},e.prototype.split=function(t,a){if(a===void 0&&(a=!1),!a){if(t===0)return this;if(t===this.length())return this.next}var s=this.clone();return this.parent.insertBefore(s,this.next),this.children.forEachAt(t,this.length(),function(l,r,n){l=l.split(r,a),s.appendChild(l)}),s},e.prototype.unwrap=function(){this.moveChildren(this.parent,this.next),this.remove()},e.prototype.update=function(t,a){var s=this,l=[],r=[];t.forEach(function(n){n.target===s.domNode&&n.type==="childList"&&(l.push.apply(l,n.addedNodes),r.push.apply(r,n.removedNodes))}),r.forEach(function(n){if(!(n.parentNode!=null&&n.tagName!=="IFRAME"&&document.body.compareDocumentPosition(n)&Node.DOCUMENT_POSITION_CONTAINED_BY)){var f=g.find(n);f!=null&&(f.domNode.parentNode==null||f.domNode.parentNode===s.domNode)&&f.detach()}}),l.filter(function(n){return n.parentNode==s.domNode}).sort(function(n,f){return n===f?0:n.compareDocumentPosition(f)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1}).forEach(function(n){var f=null;n.nextSibling!=null&&(f=g.find(n.nextSibling));var i=d(n);(i.next!=f||i.next==null)&&(i.parent!=null&&i.parent.removeChild(s),s.insertBefore(i,f||void 0))})},e}(y.default);function d(o){var e=g.find(o);if(e==null)try{e=g.create(o)}catch{e=g.create(g.Scope.INLINE),[].slice.call(o.childNodes).forEach(function(a){e.domNode.appendChild(a)}),o.parentNode&&o.parentNode.replaceChild(e.domNode,o),e.attach()}return e}u.default=m},function(x,u,c){var N=this&&this.__extends||function(){var o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var a in t)t.hasOwnProperty(a)&&(e[a]=t[a])};return function(e,t){o(e,t);function a(){this.constructor=e}e.prototype=t===null?Object.create(t):(a.prototype=t.prototype,new a)}}();Object.defineProperty(u,"__esModule",{value:!0});var w=c(12),y=c(31),g=c(17),m=c(1),d=function(o){N(e,o);function e(t){var a=o.call(this,t)||this;return a.attributes=new y.default(a.domNode),a}return e.formats=function(t){if(typeof this.tagName=="string")return!0;if(Array.isArray(this.tagName))return t.tagName.toLowerCase()},e.prototype.format=function(t,a){var s=m.query(t);s instanceof w.default?this.attributes.attribute(s,a):a&&s!=null&&(t!==this.statics.blotName||this.formats()[t]!==a)&&this.replaceWith(t,a)},e.prototype.formats=function(){var t=this.attributes.values(),a=this.statics.formats(this.domNode);return a!=null&&(t[this.statics.blotName]=a),t},e.prototype.replaceWith=function(t,a){var s=o.prototype.replaceWith.call(this,t,a);return this.attributes.copy(s),s},e.prototype.update=function(t,a){var s=this;o.prototype.update.call(this,t,a),t.some(function(l){return l.target===s.domNode&&l.type==="attributes"})&&this.attributes.build()},e.prototype.wrap=function(t,a){var s=o.prototype.wrap.call(this,t,a);return s instanceof e&&s.statics.scope===this.statics.scope&&this.attributes.move(s),s},e}(g.default);u.default=d},function(x,u,c){var N=this&&this.__extends||function(){var m=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,o){d.__proto__=o}||function(d,o){for(var e in o)o.hasOwnProperty(e)&&(d[e]=o[e])};return function(d,o){m(d,o);function e(){this.constructor=d}d.prototype=o===null?Object.create(o):(e.prototype=o.prototype,new e)}}();Object.defineProperty(u,"__esModule",{value:!0});var w=c(30),y=c(1),g=function(m){N(d,m);function d(){return m!==null&&m.apply(this,arguments)||this}return d.value=function(o){return!0},d.prototype.index=function(o,e){return this.domNode===o||this.domNode.compareDocumentPosition(o)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(e,1):-1},d.prototype.position=function(o,e){var t=[].indexOf.call(this.parent.domNode.childNodes,this.domNode);return o>0&&(t+=1),[this.parent.domNode,t]},d.prototype.value=function(){var o;return o={},o[this.statics.blotName]=this.statics.value(this.domNode)||!0,o},d.scope=y.Scope.INLINE_BLOT,d}(w.default);u.default=g},function(x,u,c){var N=c(11),w=c(3),y={attributes:{compose:function(m,d,o){typeof m!="object"&&(m={}),typeof d!="object"&&(d={});var e=w(!0,{},d);o||(e=Object.keys(e).reduce(function(a,s){return e[s]!=null&&(a[s]=e[s]),a},{}));for(var t in m)m[t]!==void 0&&d[t]===void 0&&(e[t]=m[t]);return Object.keys(e).length>0?e:void 0},diff:function(m,d){typeof m!="object"&&(m={}),typeof d!="object"&&(d={});var o=Object.keys(m).concat(Object.keys(d)).reduce(function(e,t){return N(m[t],d[t])||(e[t]=d[t]===void 0?null:d[t]),e},{});return Object.keys(o).length>0?o:void 0},transform:function(m,d,o){if(typeof m!="object")return d;if(typeof d=="object"){if(!o)return d;var e=Object.keys(d).reduce(function(t,a){return m[a]===void 0&&(t[a]=d[a]),t},{});return Object.keys(e).length>0?e:void 0}}},iterator:function(m){return new g(m)},length:function(m){return typeof m.delete=="number"?m.delete:typeof m.retain=="number"?m.retain:typeof m.insert=="string"?m.insert.length:1}};function g(m){this.ops=m,this.index=0,this.offset=0}g.prototype.hasNext=function(){return this.peekLength()<1/0},g.prototype.next=function(m){m||(m=1/0);var d=this.ops[this.index];if(d){var o=this.offset,e=y.length(d);if(m>=e-o?(m=e-o,this.index+=1,this.offset=0):this.offset+=m,typeof d.delete=="number")return{delete:m};var t={};return d.attributes&&(t.attributes=d.attributes),typeof d.retain=="number"?t.retain=m:typeof d.insert=="string"?t.insert=d.insert.substr(o,m):t.insert=d.insert,t}else return{retain:1/0}},g.prototype.peek=function(){return this.ops[this.index]},g.prototype.peekLength=function(){return this.ops[this.index]?y.length(this.ops[this.index])-this.offset:1/0},g.prototype.peekType=function(){return this.ops[this.index]?typeof this.ops[this.index].delete=="number"?"delete":typeof this.ops[this.index].retain=="number"?"retain":"insert":"retain"},g.prototype.rest=function(){if(this.hasNext()){if(this.offset===0)return this.ops.slice(this.index);var m=this.offset,d=this.index,o=this.next(),e=this.ops.slice(this.index);return this.offset=m,this.index=d,[o].concat(e)}else return[]},x.exports=y},function(x,u){var c=function(){function N(s,l){return l!=null&&s instanceof l}var w;try{w=Map}catch{w=function(){}}var y;try{y=Set}catch{y=function(){}}var g;try{g=Promise}catch{g=function(){}}function m(s,l,r,n,f){typeof l=="object"&&(r=l.depth,n=l.prototype,f=l.includeNonEnumerable,l=l.circular);var i=[],h=[],T=typeof Buffer<"u";typeof l>"u"&&(l=!0),typeof r>"u"&&(r=1/0);function E(A,P){if(A===null)return null;if(P===0)return A;var O,b;if(typeof A!="object")return A;if(N(A,w))O=new w;else if(N(A,y))O=new y;else if(N(A,g))O=new g(function(q,j){A.then(function(D){q(E(D,P-1))},function(D){j(E(D,P-1))})});else if(m.__isArray(A))O=[];else if(m.__isRegExp(A))O=new RegExp(A.source,a(A)),A.lastIndex&&(O.lastIndex=A.lastIndex);else if(m.__isDate(A))O=new Date(A.getTime());else{if(T&&Buffer.isBuffer(A))return Buffer.allocUnsafe?O=Buffer.allocUnsafe(A.length):O=new Buffer(A.length),A.copy(O),O;N(A,Error)?O=Object.create(A):typeof n>"u"?(b=Object.getPrototypeOf(A),O=Object.create(b)):(O=Object.create(n),b=n)}if(l){var _=i.indexOf(A);if(_!=-1)return h[_];i.push(A),h.push(O)}N(A,w)&&A.forEach(function(q,j){var D=E(j,P-1),B=E(q,P-1);O.set(D,B)}),N(A,y)&&A.forEach(function(q){var j=E(q,P-1);O.add(j)});for(var S in A){var L;b&&(L=Object.getOwnPropertyDescriptor(b,S)),!(L&&L.set==null)&&(O[S]=E(A[S],P-1))}if(Object.getOwnPropertySymbols)for(var R=Object.getOwnPropertySymbols(A),S=0;S<R.length;S++){var $=R[S],U=Object.getOwnPropertyDescriptor(A,$);U&&!U.enumerable&&!f||(O[$]=E(A[$],P-1),U.enumerable||Object.defineProperty(O,$,{enumerable:!1}))}if(f)for(var Y=Object.getOwnPropertyNames(A),S=0;S<Y.length;S++){var M=Y[S],U=Object.getOwnPropertyDescriptor(A,M);U&&U.enumerable||(O[M]=E(A[M],P-1),Object.defineProperty(O,M,{enumerable:!1}))}return O}return E(s,r)}m.clonePrototype=function(l){if(l===null)return null;var r=function(){};return r.prototype=l,new r};function d(s){return Object.prototype.toString.call(s)}m.__objToStr=d;function o(s){return typeof s=="object"&&d(s)==="[object Date]"}m.__isDate=o;function e(s){return typeof s=="object"&&d(s)==="[object Array]"}m.__isArray=e;function t(s){return typeof s=="object"&&d(s)==="[object RegExp]"}m.__isRegExp=t;function a(s){var l="";return s.global&&(l+="g"),s.ignoreCase&&(l+="i"),s.multiline&&(l+="m"),l}return m.__getRegExpFlags=a,m}();typeof x=="object"&&x.exports&&(x.exports=c)},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=function(){function O(b,_){var S=[],L=!0,R=!1,$=void 0;try{for(var U=b[Symbol.iterator](),Y;!(L=(Y=U.next()).done)&&(S.push(Y.value),!(_&&S.length===_));L=!0);}catch(M){R=!0,$=M}finally{try{!L&&U.return&&U.return()}finally{if(R)throw $}}return S}return function(b,_){if(Array.isArray(b))return b;if(Symbol.iterator in Object(b))return O(b,_);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),w=function(){function O(b,_){for(var S=0;S<_.length;S++){var L=_[S];L.enumerable=L.enumerable||!1,L.configurable=!0,"value"in L&&(L.writable=!0),Object.defineProperty(b,L.key,L)}}return function(b,_,S){return _&&O(b.prototype,_),S&&O(b,S),b}}(),y=function O(b,_,S){b===null&&(b=Function.prototype);var L=Object.getOwnPropertyDescriptor(b,_);if(L===void 0){var R=Object.getPrototypeOf(b);return R===null?void 0:O(R,_,S)}else{if("value"in L)return L.value;var $=L.get;return $===void 0?void 0:$.call(S)}},g=c(0),m=i(g),d=c(8),o=i(d),e=c(4),t=i(e),a=c(16),s=i(a),l=c(13),r=i(l),n=c(25),f=i(n);function i(O){return O&&O.__esModule?O:{default:O}}function h(O,b){if(!(O instanceof b))throw new TypeError("Cannot call a class as a function")}function T(O,b){if(!O)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return b&&(typeof b=="object"||typeof b=="function")?b:O}function E(O,b){if(typeof b!="function"&&b!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof b);O.prototype=Object.create(b&&b.prototype,{constructor:{value:O,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(O,b):O.__proto__=b)}function A(O){return O instanceof t.default||O instanceof e.BlockEmbed}var P=function(O){E(b,O);function b(_,S){h(this,b);var L=T(this,(b.__proto__||Object.getPrototypeOf(b)).call(this,_));return L.emitter=S.emitter,Array.isArray(S.whitelist)&&(L.whitelist=S.whitelist.reduce(function(R,$){return R[$]=!0,R},{})),L.domNode.addEventListener("DOMNodeInserted",function(){}),L.optimize(),L.enable(),L}return w(b,[{key:"batchStart",value:function(){this.batch=!0}},{key:"batchEnd",value:function(){this.batch=!1,this.optimize()}},{key:"deleteAt",value:function(S,L){var R=this.line(S),$=N(R,2),U=$[0],Y=$[1],M=this.line(S+L),q=N(M,1),j=q[0];if(y(b.prototype.__proto__||Object.getPrototypeOf(b.prototype),"deleteAt",this).call(this,S,L),j!=null&&U!==j&&Y>0){if(U instanceof e.BlockEmbed||j instanceof e.BlockEmbed){this.optimize();return}if(U instanceof r.default){var D=U.newlineIndex(U.length(),!0);if(D>-1&&(U=U.split(D+1),U===j)){this.optimize();return}}else if(j instanceof r.default){var B=j.newlineIndex(0);B>-1&&j.split(B+1)}var H=j.children.head instanceof s.default?null:j.children.head;U.moveChildren(j,H),U.remove()}this.optimize()}},{key:"enable",value:function(){var S=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.domNode.setAttribute("contenteditable",S)}},{key:"formatAt",value:function(S,L,R,$){this.whitelist!=null&&!this.whitelist[R]||(y(b.prototype.__proto__||Object.getPrototypeOf(b.prototype),"formatAt",this).call(this,S,L,R,$),this.optimize())}},{key:"insertAt",value:function(S,L,R){if(!(R!=null&&this.whitelist!=null&&!this.whitelist[L])){if(S>=this.length())if(R==null||m.default.query(L,m.default.Scope.BLOCK)==null){var $=m.default.create(this.statics.defaultChild);this.appendChild($),R==null&&L.endsWith(`
`)&&(L=L.slice(0,-1)),$.insertAt(0,L,R)}else{var U=m.default.create(L,R);this.appendChild(U)}else y(b.prototype.__proto__||Object.getPrototypeOf(b.prototype),"insertAt",this).call(this,S,L,R);this.optimize()}}},{key:"insertBefore",value:function(S,L){if(S.statics.scope===m.default.Scope.INLINE_BLOT){var R=m.default.create(this.statics.defaultChild);R.appendChild(S),S=R}y(b.prototype.__proto__||Object.getPrototypeOf(b.prototype),"insertBefore",this).call(this,S,L)}},{key:"leaf",value:function(S){return this.path(S).pop()||[null,-1]}},{key:"line",value:function(S){return S===this.length()?this.line(S-1):this.descendant(A,S)}},{key:"lines",value:function(){var S=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,L=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE,R=function $(U,Y,M){var q=[],j=M;return U.children.forEachAt(Y,M,function(D,B,H){A(D)?q.push(D):D instanceof m.default.Container&&(q=q.concat($(D,B,j))),j-=H}),q};return R(this,S,L)}},{key:"optimize",value:function(){var S=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],L=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.batch!==!0&&(y(b.prototype.__proto__||Object.getPrototypeOf(b.prototype),"optimize",this).call(this,S,L),S.length>0&&this.emitter.emit(o.default.events.SCROLL_OPTIMIZE,S,L))}},{key:"path",value:function(S){return y(b.prototype.__proto__||Object.getPrototypeOf(b.prototype),"path",this).call(this,S).slice(1)}},{key:"update",value:function(S){if(this.batch!==!0){var L=o.default.sources.USER;typeof S=="string"&&(L=S),Array.isArray(S)||(S=this.observer.takeRecords()),S.length>0&&this.emitter.emit(o.default.events.SCROLL_BEFORE_UPDATE,L,S),y(b.prototype.__proto__||Object.getPrototypeOf(b.prototype),"update",this).call(this,S.concat([])),S.length>0&&this.emitter.emit(o.default.events.SCROLL_UPDATE,L,S)}}}]),b}(m.default.Scroll);P.blotName="scroll",P.className="ql-editor",P.tagName="DIV",P.defaultChild="block",P.allowedChildren=[t.default,e.BlockEmbed,f.default],u.default=P},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0}),u.SHORTKEY=u.default=void 0;var N=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(I){return typeof I}:function(I){return I&&typeof Symbol=="function"&&I.constructor===Symbol&&I!==Symbol.prototype?"symbol":typeof I},w=function(){function I(C,z){var K=[],Z=!0,ee=!1,V=void 0;try{for(var Q=C[Symbol.iterator](),ie;!(Z=(ie=Q.next()).done)&&(K.push(ie.value),!(z&&K.length===z));Z=!0);}catch(se){ee=!0,V=se}finally{try{!Z&&Q.return&&Q.return()}finally{if(ee)throw V}}return K}return function(C,z){if(Array.isArray(C))return C;if(Symbol.iterator in Object(C))return I(C,z);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),y=function(){function I(C,z){for(var K=0;K<z.length;K++){var Z=z[K];Z.enumerable=Z.enumerable||!1,Z.configurable=!0,"value"in Z&&(Z.writable=!0),Object.defineProperty(C,Z.key,Z)}}return function(C,z,K){return z&&I(C.prototype,z),K&&I(C,K),C}}(),g=c(21),m=O(g),d=c(11),o=O(d),e=c(3),t=O(e),a=c(2),s=O(a),l=c(20),r=O(l),n=c(0),f=O(n),i=c(5),h=O(i),T=c(10),E=O(T),A=c(9),P=O(A);function O(I){return I&&I.__esModule?I:{default:I}}function b(I,C,z){return C in I?Object.defineProperty(I,C,{value:z,enumerable:!0,configurable:!0,writable:!0}):I[C]=z,I}function _(I,C){if(!(I instanceof C))throw new TypeError("Cannot call a class as a function")}function S(I,C){if(!I)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return C&&(typeof C=="object"||typeof C=="function")?C:I}function L(I,C){if(typeof C!="function"&&C!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof C);I.prototype=Object.create(C&&C.prototype,{constructor:{value:I,enumerable:!1,writable:!0,configurable:!0}}),C&&(Object.setPrototypeOf?Object.setPrototypeOf(I,C):I.__proto__=C)}var R=(0,E.default)("quill:keyboard"),$=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey",U=function(I){L(C,I),y(C,null,[{key:"match",value:function(K,Z){return Z=F(Z),["altKey","ctrlKey","metaKey","shiftKey"].some(function(ee){return!!Z[ee]!==K[ee]&&Z[ee]!==null})?!1:Z.key===(K.which||K.keyCode)}}]);function C(z,K){_(this,C);var Z=S(this,(C.__proto__||Object.getPrototypeOf(C)).call(this,z,K));return Z.bindings={},Object.keys(Z.options.bindings).forEach(function(ee){ee==="list autofill"&&z.scroll.whitelist!=null&&!z.scroll.whitelist.list||Z.options.bindings[ee]&&Z.addBinding(Z.options.bindings[ee])}),Z.addBinding({key:C.keys.ENTER,shiftKey:null},D),Z.addBinding({key:C.keys.ENTER,metaKey:null,ctrlKey:null,altKey:null},function(){}),/Firefox/i.test(navigator.userAgent)?(Z.addBinding({key:C.keys.BACKSPACE},{collapsed:!0},M),Z.addBinding({key:C.keys.DELETE},{collapsed:!0},q)):(Z.addBinding({key:C.keys.BACKSPACE},{collapsed:!0,prefix:/^.?$/},M),Z.addBinding({key:C.keys.DELETE},{collapsed:!0,suffix:/^.?$/},q)),Z.addBinding({key:C.keys.BACKSPACE},{collapsed:!1},j),Z.addBinding({key:C.keys.DELETE},{collapsed:!1},j),Z.addBinding({key:C.keys.BACKSPACE,altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},M),Z.listen(),Z}return y(C,[{key:"addBinding",value:function(K){var Z=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},ee=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},V=F(K);if(V==null||V.key==null)return R.warn("Attempted to add invalid keyboard binding",V);typeof Z=="function"&&(Z={handler:Z}),typeof ee=="function"&&(ee={handler:ee}),V=(0,t.default)(V,Z,ee),this.bindings[V.key]=this.bindings[V.key]||[],this.bindings[V.key].push(V)}},{key:"listen",value:function(){var K=this;this.quill.root.addEventListener("keydown",function(Z){if(!Z.defaultPrevented){var ee=Z.which||Z.keyCode,V=(K.bindings[ee]||[]).filter(function(pe){return C.match(Z,pe)});if(V.length!==0){var Q=K.quill.getSelection();if(!(Q==null||!K.quill.hasFocus())){var ie=K.quill.getLine(Q.index),se=w(ie,2),he=se[0],ue=se[1],G=K.quill.getLeaf(Q.index),W=w(G,2),J=W[0],te=W[1],X=Q.length===0?[J,te]:K.quill.getLeaf(Q.index+Q.length),ae=w(X,2),oe=ae[0],le=ae[1],me=J instanceof f.default.Text?J.value().slice(0,te):"",xe=oe instanceof f.default.Text?oe.value().slice(le):"",de={collapsed:Q.length===0,empty:Q.length===0&&he.length()<=1,format:K.quill.getFormat(Q),offset:ue,prefix:me,suffix:xe},Fr=V.some(function(pe){if(pe.collapsed!=null&&pe.collapsed!==de.collapsed||pe.empty!=null&&pe.empty!==de.empty||pe.offset!=null&&pe.offset!==de.offset)return!1;if(Array.isArray(pe.format)){if(pe.format.every(function(we){return de.format[we]==null}))return!1}else if(N(pe.format)==="object"&&!Object.keys(pe.format).every(function(we){return pe.format[we]===!0?de.format[we]!=null:pe.format[we]===!1?de.format[we]==null:(0,o.default)(pe.format[we],de.format[we])}))return!1;return pe.prefix!=null&&!pe.prefix.test(de.prefix)||pe.suffix!=null&&!pe.suffix.test(de.suffix)?!1:pe.handler.call(K,Q,de)!==!0});Fr&&Z.preventDefault()}}}})}}]),C}(P.default);U.keys={BACKSPACE:8,TAB:9,ENTER:13,ESCAPE:27,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46},U.DEFAULTS={bindings:{bold:H("bold"),italic:H("italic"),underline:H("underline"),indent:{key:U.keys.TAB,format:["blockquote","indent","list"],handler:function(C,z){if(z.collapsed&&z.offset!==0)return!0;this.quill.format("indent","+1",h.default.sources.USER)}},outdent:{key:U.keys.TAB,shiftKey:!0,format:["blockquote","indent","list"],handler:function(C,z){if(z.collapsed&&z.offset!==0)return!0;this.quill.format("indent","-1",h.default.sources.USER)}},"outdent backspace":{key:U.keys.BACKSPACE,collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler:function(C,z){z.format.indent!=null?this.quill.format("indent","-1",h.default.sources.USER):z.format.list!=null&&this.quill.format("list",!1,h.default.sources.USER)}},"indent code-block":B(!0),"outdent code-block":B(!1),"remove tab":{key:U.keys.TAB,shiftKey:!0,collapsed:!0,prefix:/\t$/,handler:function(C){this.quill.deleteText(C.index-1,1,h.default.sources.USER)}},tab:{key:U.keys.TAB,handler:function(C){this.quill.history.cutoff();var z=new s.default().retain(C.index).delete(C.length).insert("	");this.quill.updateContents(z,h.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(C.index+1,h.default.sources.SILENT)}},"list empty enter":{key:U.keys.ENTER,collapsed:!0,format:["list"],empty:!0,handler:function(C,z){this.quill.format("list",!1,h.default.sources.USER),z.format.indent&&this.quill.format("indent",!1,h.default.sources.USER)}},"checklist enter":{key:U.keys.ENTER,collapsed:!0,format:{list:"checked"},handler:function(C){var z=this.quill.getLine(C.index),K=w(z,2),Z=K[0],ee=K[1],V=(0,t.default)({},Z.formats(),{list:"checked"}),Q=new s.default().retain(C.index).insert(`
`,V).retain(Z.length()-ee-1).retain(1,{list:"unchecked"});this.quill.updateContents(Q,h.default.sources.USER),this.quill.setSelection(C.index+1,h.default.sources.SILENT),this.quill.scrollIntoView()}},"header enter":{key:U.keys.ENTER,collapsed:!0,format:["header"],suffix:/^$/,handler:function(C,z){var K=this.quill.getLine(C.index),Z=w(K,2),ee=Z[0],V=Z[1],Q=new s.default().retain(C.index).insert(`
`,z.format).retain(ee.length()-V-1).retain(1,{header:null});this.quill.updateContents(Q,h.default.sources.USER),this.quill.setSelection(C.index+1,h.default.sources.SILENT),this.quill.scrollIntoView()}},"list autofill":{key:" ",collapsed:!0,format:{list:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler:function(C,z){var K=z.prefix.length,Z=this.quill.getLine(C.index),ee=w(Z,2),V=ee[0],Q=ee[1];if(Q>K)return!0;var ie=void 0;switch(z.prefix.trim()){case"[]":case"[ ]":ie="unchecked";break;case"[x]":ie="checked";break;case"-":case"*":ie="bullet";break;default:ie="ordered"}this.quill.insertText(C.index," ",h.default.sources.USER),this.quill.history.cutoff();var se=new s.default().retain(C.index-Q).delete(K+1).retain(V.length()-2-Q).retain(1,{list:ie});this.quill.updateContents(se,h.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(C.index-K,h.default.sources.SILENT)}},"code exit":{key:U.keys.ENTER,collapsed:!0,format:["code-block"],prefix:/\n\n$/,suffix:/^\s+$/,handler:function(C){var z=this.quill.getLine(C.index),K=w(z,2),Z=K[0],ee=K[1],V=new s.default().retain(C.index+Z.length()-ee-2).retain(1,{"code-block":null}).delete(1);this.quill.updateContents(V,h.default.sources.USER)}},"embed left":Y(U.keys.LEFT,!1),"embed left shift":Y(U.keys.LEFT,!0),"embed right":Y(U.keys.RIGHT,!1),"embed right shift":Y(U.keys.RIGHT,!0)}};function Y(I,C){var z,K=I===U.keys.LEFT?"prefix":"suffix";return z={key:I,shiftKey:C,altKey:null},b(z,K,/^$/),b(z,"handler",function(ee){var V=ee.index;I===U.keys.RIGHT&&(V+=ee.length+1);var Q=this.quill.getLeaf(V),ie=w(Q,1),se=ie[0];return se instanceof f.default.Embed?(I===U.keys.LEFT?C?this.quill.setSelection(ee.index-1,ee.length+1,h.default.sources.USER):this.quill.setSelection(ee.index-1,h.default.sources.USER):C?this.quill.setSelection(ee.index,ee.length+1,h.default.sources.USER):this.quill.setSelection(ee.index+ee.length+1,h.default.sources.USER),!1):!0}),z}function M(I,C){if(!(I.index===0||this.quill.getLength()<=1)){var z=this.quill.getLine(I.index),K=w(z,1),Z=K[0],ee={};if(C.offset===0){var V=this.quill.getLine(I.index-1),Q=w(V,1),ie=Q[0];if(ie!=null&&ie.length()>1){var se=Z.formats(),he=this.quill.getFormat(I.index-1,1);ee=r.default.attributes.diff(se,he)||{}}}var ue=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(C.prefix)?2:1;this.quill.deleteText(I.index-ue,ue,h.default.sources.USER),Object.keys(ee).length>0&&this.quill.formatLine(I.index-ue,ue,ee,h.default.sources.USER),this.quill.focus()}}function q(I,C){var z=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(C.suffix)?2:1;if(!(I.index>=this.quill.getLength()-z)){var K={},Z=0,ee=this.quill.getLine(I.index),V=w(ee,1),Q=V[0];if(C.offset>=Q.length()-1){var ie=this.quill.getLine(I.index+1),se=w(ie,1),he=se[0];if(he){var ue=Q.formats(),G=this.quill.getFormat(I.index,1);K=r.default.attributes.diff(ue,G)||{},Z=he.length()}}this.quill.deleteText(I.index,z,h.default.sources.USER),Object.keys(K).length>0&&this.quill.formatLine(I.index+Z-1,z,K,h.default.sources.USER)}}function j(I){var C=this.quill.getLines(I),z={};if(C.length>1){var K=C[0].formats(),Z=C[C.length-1].formats();z=r.default.attributes.diff(Z,K)||{}}this.quill.deleteText(I,h.default.sources.USER),Object.keys(z).length>0&&this.quill.formatLine(I.index,1,z,h.default.sources.USER),this.quill.setSelection(I.index,h.default.sources.SILENT),this.quill.focus()}function D(I,C){var z=this;I.length>0&&this.quill.scroll.deleteAt(I.index,I.length);var K=Object.keys(C.format).reduce(function(Z,ee){return f.default.query(ee,f.default.Scope.BLOCK)&&!Array.isArray(C.format[ee])&&(Z[ee]=C.format[ee]),Z},{});this.quill.insertText(I.index,`
`,K,h.default.sources.USER),this.quill.setSelection(I.index+1,h.default.sources.SILENT),this.quill.focus(),Object.keys(C.format).forEach(function(Z){K[Z]==null&&(Array.isArray(C.format[Z])||Z!=="link"&&z.quill.format(Z,C.format[Z],h.default.sources.USER))})}function B(I){return{key:U.keys.TAB,shiftKey:!I,format:{"code-block":!0},handler:function(z){var K=f.default.query("code-block"),Z=z.index,ee=z.length,V=this.quill.scroll.descendant(K,Z),Q=w(V,2),ie=Q[0],se=Q[1];if(ie!=null){var he=this.quill.getIndex(ie),ue=ie.newlineIndex(se,!0)+1,G=ie.newlineIndex(he+se+ee),W=ie.domNode.textContent.slice(ue,G).split(`
`);se=0,W.forEach(function(J,te){I?(ie.insertAt(ue+se,K.TAB),se+=K.TAB.length,te===0?Z+=K.TAB.length:ee+=K.TAB.length):J.startsWith(K.TAB)&&(ie.deleteAt(ue+se,K.TAB.length),se-=K.TAB.length,te===0?Z-=K.TAB.length:ee-=K.TAB.length),se+=J.length+1}),this.quill.update(h.default.sources.USER),this.quill.setSelection(Z,ee,h.default.sources.SILENT)}}}}function H(I){return{key:I[0].toUpperCase(),shortKey:!0,handler:function(z,K){this.quill.format(I,!K.format[I],h.default.sources.USER)}}}function F(I){if(typeof I=="string"||typeof I=="number")return F({key:I});if((typeof I>"u"?"undefined":N(I))==="object"&&(I=(0,m.default)(I,!1)),typeof I.key=="string")if(U.keys[I.key.toUpperCase()]!=null)I.key=U.keys[I.key.toUpperCase()];else if(I.key.length===1)I.key=I.key.toUpperCase().charCodeAt(0);else return null;return I.shortKey&&(I[$]=I.shortKey,delete I.shortKey),I}u.default=U,u.SHORTKEY=$},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=function(){function r(n,f){var i=[],h=!0,T=!1,E=void 0;try{for(var A=n[Symbol.iterator](),P;!(h=(P=A.next()).done)&&(i.push(P.value),!(f&&i.length===f));h=!0);}catch(O){T=!0,E=O}finally{try{!h&&A.return&&A.return()}finally{if(T)throw E}}return i}return function(n,f){if(Array.isArray(n))return n;if(Symbol.iterator in Object(n))return r(n,f);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),w=function r(n,f,i){n===null&&(n=Function.prototype);var h=Object.getOwnPropertyDescriptor(n,f);if(h===void 0){var T=Object.getPrototypeOf(n);return T===null?void 0:r(T,f,i)}else{if("value"in h)return h.value;var E=h.get;return E===void 0?void 0:E.call(i)}},y=function(){function r(n,f){for(var i=0;i<f.length;i++){var h=f[i];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(n,h.key,h)}}return function(n,f,i){return f&&r(n.prototype,f),i&&r(n,i),n}}(),g=c(0),m=e(g),d=c(7),o=e(d);function e(r){return r&&r.__esModule?r:{default:r}}function t(r,n){if(!(r instanceof n))throw new TypeError("Cannot call a class as a function")}function a(r,n){if(!r)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return n&&(typeof n=="object"||typeof n=="function")?n:r}function s(r,n){if(typeof n!="function"&&n!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof n);r.prototype=Object.create(n&&n.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),n&&(Object.setPrototypeOf?Object.setPrototypeOf(r,n):r.__proto__=n)}var l=function(r){s(n,r),y(n,null,[{key:"value",value:function(){}}]);function n(f,i){t(this,n);var h=a(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,f));return h.selection=i,h.textNode=document.createTextNode(n.CONTENTS),h.domNode.appendChild(h.textNode),h._length=0,h}return y(n,[{key:"detach",value:function(){this.parent!=null&&this.parent.removeChild(this)}},{key:"format",value:function(i,h){if(this._length!==0)return w(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"format",this).call(this,i,h);for(var T=this,E=0;T!=null&&T.statics.scope!==m.default.Scope.BLOCK_BLOT;)E+=T.offset(T.parent),T=T.parent;T!=null&&(this._length=n.CONTENTS.length,T.optimize(),T.formatAt(E,n.CONTENTS.length,i,h),this._length=0)}},{key:"index",value:function(i,h){return i===this.textNode?0:w(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"index",this).call(this,i,h)}},{key:"length",value:function(){return this._length}},{key:"position",value:function(){return[this.textNode,this.textNode.data.length]}},{key:"remove",value:function(){w(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"remove",this).call(this),this.parent=null}},{key:"restore",value:function(){if(!(this.selection.composing||this.parent==null)){var i=this.textNode,h=this.selection.getNativeRange(),T=void 0,E=void 0,A=void 0;if(h!=null&&h.start.node===i&&h.end.node===i){var P=[i,h.start.offset,h.end.offset];T=P[0],E=P[1],A=P[2]}for(;this.domNode.lastChild!=null&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);if(this.textNode.data!==n.CONTENTS){var O=this.textNode.data.split(n.CONTENTS).join("");this.next instanceof o.default?(T=this.next.domNode,this.next.insertAt(0,O),this.textNode.data=n.CONTENTS):(this.textNode.data=O,this.parent.insertBefore(m.default.create(this.textNode),this),this.textNode=document.createTextNode(n.CONTENTS),this.domNode.appendChild(this.textNode))}if(this.remove(),E!=null){var b=[E,A].map(function(S){return Math.max(0,Math.min(T.data.length,S-1))}),_=N(b,2);return E=_[0],A=_[1],{startNode:T,startOffset:E,endNode:T,endOffset:A}}}}},{key:"update",value:function(i,h){var T=this;if(i.some(function(A){return A.type==="characterData"&&A.target===T.textNode})){var E=this.restore();E&&(h.range=E)}}},{key:"value",value:function(){return""}}]),n}(m.default.Embed);l.blotName="cursor",l.className="ql-cursor",l.tagName="span",l.CONTENTS="\uFEFF",u.default=l},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=c(0),w=m(N),y=c(4),g=m(y);function m(a){return a&&a.__esModule?a:{default:a}}function d(a,s){if(!(a instanceof s))throw new TypeError("Cannot call a class as a function")}function o(a,s){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s&&(typeof s=="object"||typeof s=="function")?s:a}function e(a,s){if(typeof s!="function"&&s!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof s);a.prototype=Object.create(s&&s.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),s&&(Object.setPrototypeOf?Object.setPrototypeOf(a,s):a.__proto__=s)}var t=function(a){e(s,a);function s(){return d(this,s),o(this,(s.__proto__||Object.getPrototypeOf(s)).apply(this,arguments))}return s}(w.default.Container);t.allowedChildren=[g.default,y.BlockEmbed,t],u.default=t},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0}),u.ColorStyle=u.ColorClass=u.ColorAttributor=void 0;var N=function(){function l(r,n){for(var f=0;f<n.length;f++){var i=n[f];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(r,i.key,i)}}return function(r,n,f){return n&&l(r.prototype,n),f&&l(r,f),r}}(),w=function l(r,n,f){r===null&&(r=Function.prototype);var i=Object.getOwnPropertyDescriptor(r,n);if(i===void 0){var h=Object.getPrototypeOf(r);return h===null?void 0:l(h,n,f)}else{if("value"in i)return i.value;var T=i.get;return T===void 0?void 0:T.call(f)}},y=c(0),g=m(y);function m(l){return l&&l.__esModule?l:{default:l}}function d(l,r){if(!(l instanceof r))throw new TypeError("Cannot call a class as a function")}function o(l,r){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r&&(typeof r=="object"||typeof r=="function")?r:l}function e(l,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof r);l.prototype=Object.create(r&&r.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(l,r):l.__proto__=r)}var t=function(l){e(r,l);function r(){return d(this,r),o(this,(r.__proto__||Object.getPrototypeOf(r)).apply(this,arguments))}return N(r,[{key:"value",value:function(f){var i=w(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"value",this).call(this,f);return i.startsWith("rgb(")?(i=i.replace(/^[^\d]+/,"").replace(/[^\d]+$/,""),"#"+i.split(",").map(function(h){return("00"+parseInt(h).toString(16)).slice(-2)}).join("")):i}}]),r}(g.default.Attributor.Style),a=new g.default.Attributor.Class("color","ql-color",{scope:g.default.Scope.INLINE}),s=new t("color","color",{scope:g.default.Scope.INLINE});u.ColorAttributor=t,u.ColorClass=a,u.ColorStyle=s},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0}),u.sanitize=u.default=void 0;var N=function(){function s(l,r){for(var n=0;n<r.length;n++){var f=r[n];f.enumerable=f.enumerable||!1,f.configurable=!0,"value"in f&&(f.writable=!0),Object.defineProperty(l,f.key,f)}}return function(l,r,n){return r&&s(l.prototype,r),n&&s(l,n),l}}(),w=function s(l,r,n){l===null&&(l=Function.prototype);var f=Object.getOwnPropertyDescriptor(l,r);if(f===void 0){var i=Object.getPrototypeOf(l);return i===null?void 0:s(i,r,n)}else{if("value"in f)return f.value;var h=f.get;return h===void 0?void 0:h.call(n)}},y=c(6),g=m(y);function m(s){return s&&s.__esModule?s:{default:s}}function d(s,l){if(!(s instanceof l))throw new TypeError("Cannot call a class as a function")}function o(s,l){if(!s)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:s}function e(s,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);s.prototype=Object.create(l&&l.prototype,{constructor:{value:s,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(s,l):s.__proto__=l)}var t=function(s){e(l,s);function l(){return d(this,l),o(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}return N(l,[{key:"format",value:function(n,f){if(n!==this.statics.blotName||!f)return w(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"format",this).call(this,n,f);f=this.constructor.sanitize(f),this.domNode.setAttribute("href",f)}}],[{key:"create",value:function(n){var f=w(l.__proto__||Object.getPrototypeOf(l),"create",this).call(this,n);return n=this.sanitize(n),f.setAttribute("href",n),f.setAttribute("rel","noopener noreferrer"),f.setAttribute("target","_blank"),f}},{key:"formats",value:function(n){return n.getAttribute("href")}},{key:"sanitize",value:function(n){return a(n,this.PROTOCOL_WHITELIST)?n:this.SANITIZED_URL}}]),l}(g.default);t.blotName="link",t.tagName="A",t.SANITIZED_URL="about:blank",t.PROTOCOL_WHITELIST=["http","https","mailto","tel"];function a(s,l){var r=document.createElement("a");r.href=s;var n=r.href.slice(0,r.href.indexOf(":"));return l.indexOf(n)>-1}u.default=t,u.sanitize=a},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(l){return typeof l}:function(l){return l&&typeof Symbol=="function"&&l.constructor===Symbol&&l!==Symbol.prototype?"symbol":typeof l},w=function(){function l(r,n){for(var f=0;f<n.length;f++){var i=n[f];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(r,i.key,i)}}return function(r,n,f){return n&&l(r.prototype,n),f&&l(r,f),r}}(),y=c(23),g=o(y),m=c(107),d=o(m);function o(l){return l&&l.__esModule?l:{default:l}}function e(l,r){if(!(l instanceof r))throw new TypeError("Cannot call a class as a function")}var t=0;function a(l,r){l.setAttribute(r,l.getAttribute(r)!=="true")}var s=function(){function l(r){var n=this;e(this,l),this.select=r,this.container=document.createElement("span"),this.buildPicker(),this.select.style.display="none",this.select.parentNode.insertBefore(this.container,this.select),this.label.addEventListener("mousedown",function(){n.togglePicker()}),this.label.addEventListener("keydown",function(f){switch(f.keyCode){case g.default.keys.ENTER:n.togglePicker();break;case g.default.keys.ESCAPE:n.escape(),f.preventDefault();break}}),this.select.addEventListener("change",this.update.bind(this))}return w(l,[{key:"togglePicker",value:function(){this.container.classList.toggle("ql-expanded"),a(this.label,"aria-expanded"),a(this.options,"aria-hidden")}},{key:"buildItem",value:function(n){var f=this,i=document.createElement("span");return i.tabIndex="0",i.setAttribute("role","button"),i.classList.add("ql-picker-item"),n.hasAttribute("value")&&i.setAttribute("data-value",n.getAttribute("value")),n.textContent&&i.setAttribute("data-label",n.textContent),i.addEventListener("click",function(){f.selectItem(i,!0)}),i.addEventListener("keydown",function(h){switch(h.keyCode){case g.default.keys.ENTER:f.selectItem(i,!0),h.preventDefault();break;case g.default.keys.ESCAPE:f.escape(),h.preventDefault();break}}),i}},{key:"buildLabel",value:function(){var n=document.createElement("span");return n.classList.add("ql-picker-label"),n.innerHTML=d.default,n.tabIndex="0",n.setAttribute("role","button"),n.setAttribute("aria-expanded","false"),this.container.appendChild(n),n}},{key:"buildOptions",value:function(){var n=this,f=document.createElement("span");f.classList.add("ql-picker-options"),f.setAttribute("aria-hidden","true"),f.tabIndex="-1",f.id="ql-picker-options-"+t,t+=1,this.label.setAttribute("aria-controls",f.id),this.options=f,[].slice.call(this.select.options).forEach(function(i){var h=n.buildItem(i);f.appendChild(h),i.selected===!0&&n.selectItem(h)}),this.container.appendChild(f)}},{key:"buildPicker",value:function(){var n=this;[].slice.call(this.select.attributes).forEach(function(f){n.container.setAttribute(f.name,f.value)}),this.container.classList.add("ql-picker"),this.label=this.buildLabel(),this.buildOptions()}},{key:"escape",value:function(){var n=this;this.close(),setTimeout(function(){return n.label.focus()},1)}},{key:"close",value:function(){this.container.classList.remove("ql-expanded"),this.label.setAttribute("aria-expanded","false"),this.options.setAttribute("aria-hidden","true")}},{key:"selectItem",value:function(n){var f=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,i=this.container.querySelector(".ql-selected");if(n!==i&&(i!=null&&i.classList.remove("ql-selected"),n!=null&&(n.classList.add("ql-selected"),this.select.selectedIndex=[].indexOf.call(n.parentNode.children,n),n.hasAttribute("data-value")?this.label.setAttribute("data-value",n.getAttribute("data-value")):this.label.removeAttribute("data-value"),n.hasAttribute("data-label")?this.label.setAttribute("data-label",n.getAttribute("data-label")):this.label.removeAttribute("data-label"),f))){if(typeof Event=="function")this.select.dispatchEvent(new Event("change"));else if((typeof Event>"u"?"undefined":N(Event))==="object"){var h=document.createEvent("Event");h.initEvent("change",!0,!0),this.select.dispatchEvent(h)}this.close()}}},{key:"update",value:function(){var n=void 0;if(this.select.selectedIndex>-1){var f=this.container.querySelector(".ql-picker-options").children[this.select.selectedIndex];n=this.select.options[this.select.selectedIndex],this.selectItem(f)}else this.selectItem(null);var i=n!=null&&n!==this.select.querySelector("option[selected]");this.label.classList.toggle("ql-active",i)}}]),l}();u.default=s},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=c(0),w=R(N),y=c(5),g=R(y),m=c(4),d=R(m),o=c(16),e=R(o),t=c(25),a=R(t),s=c(24),l=R(s),r=c(35),n=R(r),f=c(6),i=R(f),h=c(22),T=R(h),E=c(7),A=R(E),P=c(55),O=R(P),b=c(42),_=R(b),S=c(23),L=R(S);function R($){return $&&$.__esModule?$:{default:$}}g.default.register({"blots/block":d.default,"blots/block/embed":m.BlockEmbed,"blots/break":e.default,"blots/container":a.default,"blots/cursor":l.default,"blots/embed":n.default,"blots/inline":i.default,"blots/scroll":T.default,"blots/text":A.default,"modules/clipboard":O.default,"modules/history":_.default,"modules/keyboard":L.default}),w.default.register(d.default,e.default,l.default,i.default,T.default,A.default),u.default=g.default},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=c(1),w=function(){function y(g){this.domNode=g,this.domNode[N.DATA_KEY]={blot:this}}return Object.defineProperty(y.prototype,"statics",{get:function(){return this.constructor},enumerable:!0,configurable:!0}),y.create=function(g){if(this.tagName==null)throw new N.ParchmentError("Blot definition missing tagName");var m;return Array.isArray(this.tagName)?(typeof g=="string"&&(g=g.toUpperCase(),parseInt(g).toString()===g&&(g=parseInt(g))),typeof g=="number"?m=document.createElement(this.tagName[g-1]):this.tagName.indexOf(g)>-1?m=document.createElement(g):m=document.createElement(this.tagName[0])):m=document.createElement(this.tagName),this.className&&m.classList.add(this.className),m},y.prototype.attach=function(){this.parent!=null&&(this.scroll=this.parent.scroll)},y.prototype.clone=function(){var g=this.domNode.cloneNode(!1);return N.create(g)},y.prototype.detach=function(){this.parent!=null&&this.parent.removeChild(this),delete this.domNode[N.DATA_KEY]},y.prototype.deleteAt=function(g,m){var d=this.isolate(g,m);d.remove()},y.prototype.formatAt=function(g,m,d,o){var e=this.isolate(g,m);if(N.query(d,N.Scope.BLOT)!=null&&o)e.wrap(d,o);else if(N.query(d,N.Scope.ATTRIBUTE)!=null){var t=N.create(this.statics.scope);e.wrap(t),t.format(d,o)}},y.prototype.insertAt=function(g,m,d){var o=d==null?N.create("text",m):N.create(m,d),e=this.split(g);this.parent.insertBefore(o,e)},y.prototype.insertInto=function(g,m){m===void 0&&(m=null),this.parent!=null&&this.parent.children.remove(this);var d=null;g.children.insertBefore(this,m),m!=null&&(d=m.domNode),(this.domNode.parentNode!=g.domNode||this.domNode.nextSibling!=d)&&g.domNode.insertBefore(this.domNode,d),this.parent=g,this.attach()},y.prototype.isolate=function(g,m){var d=this.split(g);return d.split(m),d},y.prototype.length=function(){return 1},y.prototype.offset=function(g){return g===void 0&&(g=this.parent),this.parent==null||this==g?0:this.parent.children.offset(this)+this.parent.offset(g)},y.prototype.optimize=function(g){this.domNode[N.DATA_KEY]!=null&&delete this.domNode[N.DATA_KEY].mutations},y.prototype.remove=function(){this.domNode.parentNode!=null&&this.domNode.parentNode.removeChild(this.domNode),this.detach()},y.prototype.replace=function(g){g.parent!=null&&(g.parent.insertBefore(this,g.next),g.remove())},y.prototype.replaceWith=function(g,m){var d=typeof g=="string"?N.create(g,m):g;return d.replace(this),d},y.prototype.split=function(g,m){return g===0?this:this.next},y.prototype.update=function(g,m){},y.prototype.wrap=function(g,m){var d=typeof g=="string"?N.create(g,m):g;return this.parent!=null&&this.parent.insertBefore(d,this.next),d.appendChild(this),d},y.blotName="abstract",y}();u.default=w},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=c(12),w=c(32),y=c(33),g=c(1),m=function(){function d(o){this.attributes={},this.domNode=o,this.build()}return d.prototype.attribute=function(o,e){e?o.add(this.domNode,e)&&(o.value(this.domNode)!=null?this.attributes[o.attrName]=o:delete this.attributes[o.attrName]):(o.remove(this.domNode),delete this.attributes[o.attrName])},d.prototype.build=function(){var o=this;this.attributes={};var e=N.default.keys(this.domNode),t=w.default.keys(this.domNode),a=y.default.keys(this.domNode);e.concat(t).concat(a).forEach(function(s){var l=g.query(s,g.Scope.ATTRIBUTE);l instanceof N.default&&(o.attributes[l.attrName]=l)})},d.prototype.copy=function(o){var e=this;Object.keys(this.attributes).forEach(function(t){var a=e.attributes[t].value(e.domNode);o.format(t,a)})},d.prototype.move=function(o){var e=this;this.copy(o),Object.keys(this.attributes).forEach(function(t){e.attributes[t].remove(e.domNode)}),this.attributes={}},d.prototype.values=function(){var o=this;return Object.keys(this.attributes).reduce(function(e,t){return e[t]=o.attributes[t].value(o.domNode),e},{})},d}();u.default=m},function(x,u,c){var N=this&&this.__extends||function(){var m=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,o){d.__proto__=o}||function(d,o){for(var e in o)o.hasOwnProperty(e)&&(d[e]=o[e])};return function(d,o){m(d,o);function e(){this.constructor=d}d.prototype=o===null?Object.create(o):(e.prototype=o.prototype,new e)}}();Object.defineProperty(u,"__esModule",{value:!0});var w=c(12);function y(m,d){var o=m.getAttribute("class")||"";return o.split(/\s+/).filter(function(e){return e.indexOf(d+"-")===0})}var g=function(m){N(d,m);function d(){return m!==null&&m.apply(this,arguments)||this}return d.keys=function(o){return(o.getAttribute("class")||"").split(/\s+/).map(function(e){return e.split("-").slice(0,-1).join("-")})},d.prototype.add=function(o,e){return this.canAdd(o,e)?(this.remove(o),o.classList.add(this.keyName+"-"+e),!0):!1},d.prototype.remove=function(o){var e=y(o,this.keyName);e.forEach(function(t){o.classList.remove(t)}),o.classList.length===0&&o.removeAttribute("class")},d.prototype.value=function(o){var e=y(o,this.keyName)[0]||"",t=e.slice(this.keyName.length+1);return this.canAdd(o,t)?t:""},d}(w.default);u.default=g},function(x,u,c){var N=this&&this.__extends||function(){var m=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,o){d.__proto__=o}||function(d,o){for(var e in o)o.hasOwnProperty(e)&&(d[e]=o[e])};return function(d,o){m(d,o);function e(){this.constructor=d}d.prototype=o===null?Object.create(o):(e.prototype=o.prototype,new e)}}();Object.defineProperty(u,"__esModule",{value:!0});var w=c(12);function y(m){var d=m.split("-"),o=d.slice(1).map(function(e){return e[0].toUpperCase()+e.slice(1)}).join("");return d[0]+o}var g=function(m){N(d,m);function d(){return m!==null&&m.apply(this,arguments)||this}return d.keys=function(o){return(o.getAttribute("style")||"").split(";").map(function(e){var t=e.split(":");return t[0].trim()})},d.prototype.add=function(o,e){return this.canAdd(o,e)?(o.style[y(this.keyName)]=e,!0):!1},d.prototype.remove=function(o){o.style[y(this.keyName)]="",o.getAttribute("style")||o.removeAttribute("style")},d.prototype.value=function(o){var e=o.style[y(this.keyName)];return this.canAdd(o,e)?e:""},d}(w.default);u.default=g},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=function(){function g(m,d){for(var o=0;o<d.length;o++){var e=d[o];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(m,e.key,e)}}return function(m,d,o){return d&&g(m.prototype,d),o&&g(m,o),m}}();function w(g,m){if(!(g instanceof m))throw new TypeError("Cannot call a class as a function")}var y=function(){function g(m,d){w(this,g),this.quill=m,this.options=d,this.modules={}}return N(g,[{key:"init",value:function(){var d=this;Object.keys(this.options.modules).forEach(function(o){d.modules[o]==null&&d.addModule(o)})}},{key:"addModule",value:function(d){var o=this.quill.constructor.import("modules/"+d);return this.modules[d]=new o(this.quill,this.options.modules[d]||{}),this.modules[d]}}]),g}();y.DEFAULTS={modules:{}},y.themes={default:y},u.default=y},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=function(){function r(n,f){for(var i=0;i<f.length;i++){var h=f[i];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(n,h.key,h)}}return function(n,f,i){return f&&r(n.prototype,f),i&&r(n,i),n}}(),w=function r(n,f,i){n===null&&(n=Function.prototype);var h=Object.getOwnPropertyDescriptor(n,f);if(h===void 0){var T=Object.getPrototypeOf(n);return T===null?void 0:r(T,f,i)}else{if("value"in h)return h.value;var E=h.get;return E===void 0?void 0:E.call(i)}},y=c(0),g=o(y),m=c(7),d=o(m);function o(r){return r&&r.__esModule?r:{default:r}}function e(r,n){if(!(r instanceof n))throw new TypeError("Cannot call a class as a function")}function t(r,n){if(!r)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return n&&(typeof n=="object"||typeof n=="function")?n:r}function a(r,n){if(typeof n!="function"&&n!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof n);r.prototype=Object.create(n&&n.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),n&&(Object.setPrototypeOf?Object.setPrototypeOf(r,n):r.__proto__=n)}var s="\uFEFF",l=function(r){a(n,r);function n(f){e(this,n);var i=t(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,f));return i.contentNode=document.createElement("span"),i.contentNode.setAttribute("contenteditable",!1),[].slice.call(i.domNode.childNodes).forEach(function(h){i.contentNode.appendChild(h)}),i.leftGuard=document.createTextNode(s),i.rightGuard=document.createTextNode(s),i.domNode.appendChild(i.leftGuard),i.domNode.appendChild(i.contentNode),i.domNode.appendChild(i.rightGuard),i}return N(n,[{key:"index",value:function(i,h){return i===this.leftGuard?0:i===this.rightGuard?1:w(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"index",this).call(this,i,h)}},{key:"restore",value:function(i){var h=void 0,T=void 0,E=i.data.split(s).join("");if(i===this.leftGuard)if(this.prev instanceof d.default){var A=this.prev.length();this.prev.insertAt(A,E),h={startNode:this.prev.domNode,startOffset:A+E.length}}else T=document.createTextNode(E),this.parent.insertBefore(g.default.create(T),this),h={startNode:T,startOffset:E.length};else i===this.rightGuard&&(this.next instanceof d.default?(this.next.insertAt(0,E),h={startNode:this.next.domNode,startOffset:E.length}):(T=document.createTextNode(E),this.parent.insertBefore(g.default.create(T),this.next),h={startNode:T,startOffset:E.length}));return i.data=s,h}},{key:"update",value:function(i,h){var T=this;i.forEach(function(E){if(E.type==="characterData"&&(E.target===T.leftGuard||E.target===T.rightGuard)){var A=T.restore(E.target);A&&(h.range=A)}})}}]),n}(g.default.Embed);u.default=l},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0}),u.AlignStyle=u.AlignClass=u.AlignAttribute=void 0;var N=c(0),w=y(N);function y(e){return e&&e.__esModule?e:{default:e}}var g={scope:w.default.Scope.BLOCK,whitelist:["right","center","justify"]},m=new w.default.Attributor.Attribute("align","align",g),d=new w.default.Attributor.Class("align","ql-align",g),o=new w.default.Attributor.Style("align","text-align",g);u.AlignAttribute=m,u.AlignClass=d,u.AlignStyle=o},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0}),u.BackgroundStyle=u.BackgroundClass=void 0;var N=c(0),w=g(N),y=c(26);function g(o){return o&&o.__esModule?o:{default:o}}var m=new w.default.Attributor.Class("background","ql-bg",{scope:w.default.Scope.INLINE}),d=new y.ColorAttributor("background","background-color",{scope:w.default.Scope.INLINE});u.BackgroundClass=m,u.BackgroundStyle=d},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0}),u.DirectionStyle=u.DirectionClass=u.DirectionAttribute=void 0;var N=c(0),w=y(N);function y(e){return e&&e.__esModule?e:{default:e}}var g={scope:w.default.Scope.BLOCK,whitelist:["rtl"]},m=new w.default.Attributor.Attribute("direction","dir",g),d=new w.default.Attributor.Class("direction","ql-direction",g),o=new w.default.Attributor.Style("direction","direction",g);u.DirectionAttribute=m,u.DirectionClass=d,u.DirectionStyle=o},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0}),u.FontClass=u.FontStyle=void 0;var N=function(){function r(n,f){for(var i=0;i<f.length;i++){var h=f[i];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(n,h.key,h)}}return function(n,f,i){return f&&r(n.prototype,f),i&&r(n,i),n}}(),w=function r(n,f,i){n===null&&(n=Function.prototype);var h=Object.getOwnPropertyDescriptor(n,f);if(h===void 0){var T=Object.getPrototypeOf(n);return T===null?void 0:r(T,f,i)}else{if("value"in h)return h.value;var E=h.get;return E===void 0?void 0:E.call(i)}},y=c(0),g=m(y);function m(r){return r&&r.__esModule?r:{default:r}}function d(r,n){if(!(r instanceof n))throw new TypeError("Cannot call a class as a function")}function o(r,n){if(!r)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return n&&(typeof n=="object"||typeof n=="function")?n:r}function e(r,n){if(typeof n!="function"&&n!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof n);r.prototype=Object.create(n&&n.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),n&&(Object.setPrototypeOf?Object.setPrototypeOf(r,n):r.__proto__=n)}var t={scope:g.default.Scope.INLINE,whitelist:["serif","monospace"]},a=new g.default.Attributor.Class("font","ql-font",t),s=function(r){e(n,r);function n(){return d(this,n),o(this,(n.__proto__||Object.getPrototypeOf(n)).apply(this,arguments))}return N(n,[{key:"value",value:function(i){return w(n.prototype.__proto__||Object.getPrototypeOf(n.prototype),"value",this).call(this,i).replace(/["']/g,"")}}]),n}(g.default.Attributor.Style),l=new s("font","font-family",t);u.FontStyle=l,u.FontClass=a},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0}),u.SizeStyle=u.SizeClass=void 0;var N=c(0),w=y(N);function y(d){return d&&d.__esModule?d:{default:d}}var g=new w.default.Attributor.Class("size","ql-size",{scope:w.default.Scope.INLINE,whitelist:["small","large","huge"]}),m=new w.default.Attributor.Style("size","font-size",{scope:w.default.Scope.INLINE,whitelist:["10px","18px","32px"]});u.SizeClass=g,u.SizeStyle=m},function(x,u,c){x.exports={align:{"":c(76),center:c(77),right:c(78),justify:c(79)},background:c(80),blockquote:c(81),bold:c(82),clean:c(83),code:c(58),"code-block":c(58),color:c(84),direction:{"":c(85),rtl:c(86)},float:{center:c(87),full:c(88),left:c(89),right:c(90)},formula:c(91),header:{1:c(92),2:c(93)},italic:c(94),image:c(95),indent:{"+1":c(96),"-1":c(97)},link:c(98),list:{ordered:c(99),bullet:c(100),check:c(101)},script:{sub:c(102),super:c(103)},strike:c(104),underline:c(105),video:c(106)}},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0}),u.getLastChangeIndex=u.default=void 0;var N=function(){function f(i,h){for(var T=0;T<h.length;T++){var E=h[T];E.enumerable=E.enumerable||!1,E.configurable=!0,"value"in E&&(E.writable=!0),Object.defineProperty(i,E.key,E)}}return function(i,h,T){return h&&f(i.prototype,h),T&&f(i,T),i}}(),w=c(0),y=e(w),g=c(5),m=e(g),d=c(9),o=e(d);function e(f){return f&&f.__esModule?f:{default:f}}function t(f,i){if(!(f instanceof i))throw new TypeError("Cannot call a class as a function")}function a(f,i){if(!f)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return i&&(typeof i=="object"||typeof i=="function")?i:f}function s(f,i){if(typeof i!="function"&&i!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof i);f.prototype=Object.create(i&&i.prototype,{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}}),i&&(Object.setPrototypeOf?Object.setPrototypeOf(f,i):f.__proto__=i)}var l=function(f){s(i,f);function i(h,T){t(this,i);var E=a(this,(i.__proto__||Object.getPrototypeOf(i)).call(this,h,T));return E.lastRecorded=0,E.ignoreChange=!1,E.clear(),E.quill.on(m.default.events.EDITOR_CHANGE,function(A,P,O,b){A!==m.default.events.TEXT_CHANGE||E.ignoreChange||(!E.options.userOnly||b===m.default.sources.USER?E.record(P,O):E.transform(P))}),E.quill.keyboard.addBinding({key:"Z",shortKey:!0},E.undo.bind(E)),E.quill.keyboard.addBinding({key:"Z",shortKey:!0,shiftKey:!0},E.redo.bind(E)),/Win/i.test(navigator.platform)&&E.quill.keyboard.addBinding({key:"Y",shortKey:!0},E.redo.bind(E)),E}return N(i,[{key:"change",value:function(T,E){if(this.stack[T].length!==0){var A=this.stack[T].pop();this.stack[E].push(A),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(A[T],m.default.sources.USER),this.ignoreChange=!1;var P=n(A[T]);this.quill.setSelection(P)}}},{key:"clear",value:function(){this.stack={undo:[],redo:[]}}},{key:"cutoff",value:function(){this.lastRecorded=0}},{key:"record",value:function(T,E){if(T.ops.length!==0){this.stack.redo=[];var A=this.quill.getContents().diff(E),P=Date.now();if(this.lastRecorded+this.options.delay>P&&this.stack.undo.length>0){var O=this.stack.undo.pop();A=A.compose(O.undo),T=O.redo.compose(T)}else this.lastRecorded=P;this.stack.undo.push({redo:T,undo:A}),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift()}}},{key:"redo",value:function(){this.change("redo","undo")}},{key:"transform",value:function(T){this.stack.undo.forEach(function(E){E.undo=T.transform(E.undo,!0),E.redo=T.transform(E.redo,!0)}),this.stack.redo.forEach(function(E){E.undo=T.transform(E.undo,!0),E.redo=T.transform(E.redo,!0)})}},{key:"undo",value:function(){this.change("undo","redo")}}]),i}(o.default);l.DEFAULTS={delay:1e3,maxStack:100,userOnly:!1};function r(f){var i=f.ops[f.ops.length-1];return i==null?!1:i.insert!=null?typeof i.insert=="string"&&i.insert.endsWith(`
`):i.attributes!=null?Object.keys(i.attributes).some(function(h){return y.default.query(h,y.default.Scope.BLOCK)!=null}):!1}function n(f){var i=f.reduce(function(T,E){return T+=E.delete||0,T},0),h=f.length()-i;return r(f)&&(h-=1),h}u.default=l,u.getLastChangeIndex=n},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0}),u.default=u.BaseTooltip=void 0;var N=function(){function D(B,H){for(var F=0;F<H.length;F++){var I=H[F];I.enumerable=I.enumerable||!1,I.configurable=!0,"value"in I&&(I.writable=!0),Object.defineProperty(B,I.key,I)}}return function(B,H,F){return H&&D(B.prototype,H),F&&D(B,F),B}}(),w=function D(B,H,F){B===null&&(B=Function.prototype);var I=Object.getOwnPropertyDescriptor(B,H);if(I===void 0){var C=Object.getPrototypeOf(B);return C===null?void 0:D(C,H,F)}else{if("value"in I)return I.value;var z=I.get;return z===void 0?void 0:z.call(F)}},y=c(3),g=P(y),m=c(2),d=P(m),o=c(8),e=P(o),t=c(23),a=P(t),s=c(34),l=P(s),r=c(59),n=P(r),f=c(60),i=P(f),h=c(28),T=P(h),E=c(61),A=P(E);function P(D){return D&&D.__esModule?D:{default:D}}function O(D,B){if(!(D instanceof B))throw new TypeError("Cannot call a class as a function")}function b(D,B){if(!D)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return B&&(typeof B=="object"||typeof B=="function")?B:D}function _(D,B){if(typeof B!="function"&&B!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof B);D.prototype=Object.create(B&&B.prototype,{constructor:{value:D,enumerable:!1,writable:!0,configurable:!0}}),B&&(Object.setPrototypeOf?Object.setPrototypeOf(D,B):D.__proto__=B)}var S=[!1,"center","right","justify"],L=["#000000","#e60000","#ff9900","#ffff00","#008a00","#0066cc","#9933ff","#ffffff","#facccc","#ffebcc","#ffffcc","#cce8cc","#cce0f5","#ebd6ff","#bbbbbb","#f06666","#ffc266","#ffff66","#66b966","#66a3e0","#c285ff","#888888","#a10000","#b26b00","#b2b200","#006100","#0047b2","#6b24b2","#444444","#5c0000","#663d00","#666600","#003700","#002966","#3d1466"],R=[!1,"serif","monospace"],$=["1","2","3",!1],U=["small",!1,"large","huge"],Y=function(D){_(B,D);function B(H,F){O(this,B);var I=b(this,(B.__proto__||Object.getPrototypeOf(B)).call(this,H,F)),C=function z(K){if(!document.body.contains(H.root))return document.body.removeEventListener("click",z);I.tooltip!=null&&!I.tooltip.root.contains(K.target)&&document.activeElement!==I.tooltip.textbox&&!I.quill.hasFocus()&&I.tooltip.hide(),I.pickers!=null&&I.pickers.forEach(function(Z){Z.container.contains(K.target)||Z.close()})};return H.emitter.listenDOM("click",document.body,C),I}return N(B,[{key:"addModule",value:function(F){var I=w(B.prototype.__proto__||Object.getPrototypeOf(B.prototype),"addModule",this).call(this,F);return F==="toolbar"&&this.extendToolbar(I),I}},{key:"buildButtons",value:function(F,I){F.forEach(function(C){var z=C.getAttribute("class")||"";z.split(/\s+/).forEach(function(K){if(K.startsWith("ql-")&&(K=K.slice(3),I[K]!=null))if(K==="direction")C.innerHTML=I[K][""]+I[K].rtl;else if(typeof I[K]=="string")C.innerHTML=I[K];else{var Z=C.value||"";Z!=null&&I[K][Z]&&(C.innerHTML=I[K][Z])}})})}},{key:"buildPickers",value:function(F,I){var C=this;this.pickers=F.map(function(K){if(K.classList.contains("ql-align"))return K.querySelector("option")==null&&j(K,S),new i.default(K,I.align);if(K.classList.contains("ql-background")||K.classList.contains("ql-color")){var Z=K.classList.contains("ql-background")?"background":"color";return K.querySelector("option")==null&&j(K,L,Z==="background"?"#ffffff":"#000000"),new n.default(K,I[Z])}else return K.querySelector("option")==null&&(K.classList.contains("ql-font")?j(K,R):K.classList.contains("ql-header")?j(K,$):K.classList.contains("ql-size")&&j(K,U)),new T.default(K)});var z=function(){C.pickers.forEach(function(Z){Z.update()})};this.quill.on(e.default.events.EDITOR_CHANGE,z)}}]),B}(l.default);Y.DEFAULTS=(0,g.default)(!0,{},l.default.DEFAULTS,{modules:{toolbar:{handlers:{formula:function(){this.quill.theme.tooltip.edit("formula")},image:function(){var B=this,H=this.container.querySelector("input.ql-image[type=file]");H==null&&(H=document.createElement("input"),H.setAttribute("type","file"),H.setAttribute("accept","image/png, image/gif, image/jpeg, image/bmp, image/x-icon"),H.classList.add("ql-image"),H.addEventListener("change",function(){if(H.files!=null&&H.files[0]!=null){var F=new FileReader;F.onload=function(I){var C=B.quill.getSelection(!0);B.quill.updateContents(new d.default().retain(C.index).delete(C.length).insert({image:I.target.result}),e.default.sources.USER),B.quill.setSelection(C.index+1,e.default.sources.SILENT),H.value=""},F.readAsDataURL(H.files[0])}}),this.container.appendChild(H)),H.click()},video:function(){this.quill.theme.tooltip.edit("video")}}}}});var M=function(D){_(B,D);function B(H,F){O(this,B);var I=b(this,(B.__proto__||Object.getPrototypeOf(B)).call(this,H,F));return I.textbox=I.root.querySelector('input[type="text"]'),I.listen(),I}return N(B,[{key:"listen",value:function(){var F=this;this.textbox.addEventListener("keydown",function(I){a.default.match(I,"enter")?(F.save(),I.preventDefault()):a.default.match(I,"escape")&&(F.cancel(),I.preventDefault())})}},{key:"cancel",value:function(){this.hide()}},{key:"edit",value:function(){var F=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"link",I=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;this.root.classList.remove("ql-hidden"),this.root.classList.add("ql-editing"),I!=null?this.textbox.value=I:F!==this.root.getAttribute("data-mode")&&(this.textbox.value=""),this.position(this.quill.getBounds(this.quill.selection.savedRange)),this.textbox.select(),this.textbox.setAttribute("placeholder",this.textbox.getAttribute("data-"+F)||""),this.root.setAttribute("data-mode",F)}},{key:"restoreFocus",value:function(){var F=this.quill.scrollingContainer.scrollTop;this.quill.focus(),this.quill.scrollingContainer.scrollTop=F}},{key:"save",value:function(){var F=this.textbox.value;switch(this.root.getAttribute("data-mode")){case"link":{var I=this.quill.root.scrollTop;this.linkRange?(this.quill.formatText(this.linkRange,"link",F,e.default.sources.USER),delete this.linkRange):(this.restoreFocus(),this.quill.format("link",F,e.default.sources.USER)),this.quill.root.scrollTop=I;break}case"video":F=q(F);case"formula":{if(!F)break;var C=this.quill.getSelection(!0);if(C!=null){var z=C.index+C.length;this.quill.insertEmbed(z,this.root.getAttribute("data-mode"),F,e.default.sources.USER),this.root.getAttribute("data-mode")==="formula"&&this.quill.insertText(z+1," ",e.default.sources.USER),this.quill.setSelection(z+2,e.default.sources.USER)}break}}this.textbox.value="",this.hide()}}]),B}(A.default);function q(D){var B=D.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtube\.com\/watch.*v=([a-zA-Z0-9_-]+)/)||D.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtu\.be\/([a-zA-Z0-9_-]+)/);return B?(B[1]||"https")+"://www.youtube.com/embed/"+B[2]+"?showinfo=0":(B=D.match(/^(?:(https?):\/\/)?(?:www\.)?vimeo\.com\/(\d+)/))?(B[1]||"https")+"://player.vimeo.com/video/"+B[2]+"/":D}function j(D,B){var H=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;B.forEach(function(F){var I=document.createElement("option");F===H?I.setAttribute("selected","selected"):I.setAttribute("value",F),D.appendChild(I)})}u.BaseTooltip=M,u.default=Y},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=function(){function w(){this.head=this.tail=null,this.length=0}return w.prototype.append=function(){for(var y=[],g=0;g<arguments.length;g++)y[g]=arguments[g];this.insertBefore(y[0],null),y.length>1&&this.append.apply(this,y.slice(1))},w.prototype.contains=function(y){for(var g,m=this.iterator();g=m();)if(g===y)return!0;return!1},w.prototype.insertBefore=function(y,g){y&&(y.next=g,g!=null?(y.prev=g.prev,g.prev!=null&&(g.prev.next=y),g.prev=y,g===this.head&&(this.head=y)):this.tail!=null?(this.tail.next=y,y.prev=this.tail,this.tail=y):(y.prev=null,this.head=this.tail=y),this.length+=1)},w.prototype.offset=function(y){for(var g=0,m=this.head;m!=null;){if(m===y)return g;g+=m.length(),m=m.next}return-1},w.prototype.remove=function(y){this.contains(y)&&(y.prev!=null&&(y.prev.next=y.next),y.next!=null&&(y.next.prev=y.prev),y===this.head&&(this.head=y.next),y===this.tail&&(this.tail=y.prev),this.length-=1)},w.prototype.iterator=function(y){return y===void 0&&(y=this.head),function(){var g=y;return y!=null&&(y=y.next),g}},w.prototype.find=function(y,g){g===void 0&&(g=!1);for(var m,d=this.iterator();m=d();){var o=m.length();if(y<o||g&&y===o&&(m.next==null||m.next.length()!==0))return[m,y];y-=o}return[null,0]},w.prototype.forEach=function(y){for(var g,m=this.iterator();g=m();)y(g)},w.prototype.forEachAt=function(y,g,m){if(!(g<=0))for(var d=this.find(y),o=d[0],e=d[1],t,a=y-e,s=this.iterator(o);(t=s())&&a<y+g;){var l=t.length();y>a?m(t,y-a,Math.min(g,a+l-y)):m(t,0,Math.min(l,y+g-a)),a+=l}},w.prototype.map=function(y){return this.reduce(function(g,m){return g.push(y(m)),g},[])},w.prototype.reduce=function(y,g){for(var m,d=this.iterator();m=d();)g=y(g,m);return g},w}();u.default=N},function(x,u,c){var N=this&&this.__extends||function(){var o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var a in t)t.hasOwnProperty(a)&&(e[a]=t[a])};return function(e,t){o(e,t);function a(){this.constructor=e}e.prototype=t===null?Object.create(t):(a.prototype=t.prototype,new a)}}();Object.defineProperty(u,"__esModule",{value:!0});var w=c(17),y=c(1),g={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0},m=100,d=function(o){N(e,o);function e(t){var a=o.call(this,t)||this;return a.scroll=a,a.observer=new MutationObserver(function(s){a.update(s)}),a.observer.observe(a.domNode,g),a.attach(),a}return e.prototype.detach=function(){o.prototype.detach.call(this),this.observer.disconnect()},e.prototype.deleteAt=function(t,a){this.update(),t===0&&a===this.length()?this.children.forEach(function(s){s.remove()}):o.prototype.deleteAt.call(this,t,a)},e.prototype.formatAt=function(t,a,s,l){this.update(),o.prototype.formatAt.call(this,t,a,s,l)},e.prototype.insertAt=function(t,a,s){this.update(),o.prototype.insertAt.call(this,t,a,s)},e.prototype.optimize=function(t,a){var s=this;t===void 0&&(t=[]),a===void 0&&(a={}),o.prototype.optimize.call(this,a);for(var l=[].slice.call(this.observer.takeRecords());l.length>0;)t.push(l.pop());for(var r=function(h,T){T===void 0&&(T=!0),!(h==null||h===s)&&h.domNode.parentNode!=null&&(h.domNode[y.DATA_KEY].mutations==null&&(h.domNode[y.DATA_KEY].mutations=[]),T&&r(h.parent))},n=function(h){h.domNode[y.DATA_KEY]==null||h.domNode[y.DATA_KEY].mutations==null||(h instanceof w.default&&h.children.forEach(n),h.optimize(a))},f=t,i=0;f.length>0;i+=1){if(i>=m)throw new Error("[Parchment] Maximum optimize iterations reached");for(f.forEach(function(h){var T=y.find(h.target,!0);T!=null&&(T.domNode===h.target&&(h.type==="childList"?(r(y.find(h.previousSibling,!1)),[].forEach.call(h.addedNodes,function(E){var A=y.find(E,!1);r(A,!1),A instanceof w.default&&A.children.forEach(function(P){r(P,!1)})})):h.type==="attributes"&&r(T.prev)),r(T))}),this.children.forEach(n),f=[].slice.call(this.observer.takeRecords()),l=f.slice();l.length>0;)t.push(l.pop())}},e.prototype.update=function(t,a){var s=this;a===void 0&&(a={}),t=t||this.observer.takeRecords(),t.map(function(l){var r=y.find(l.target,!0);return r==null?null:r.domNode[y.DATA_KEY].mutations==null?(r.domNode[y.DATA_KEY].mutations=[l],r):(r.domNode[y.DATA_KEY].mutations.push(l),null)}).forEach(function(l){l==null||l===s||l.domNode[y.DATA_KEY]==null||l.update(l.domNode[y.DATA_KEY].mutations||[],a)}),this.domNode[y.DATA_KEY].mutations!=null&&o.prototype.update.call(this,this.domNode[y.DATA_KEY].mutations,a),this.optimize(t,a)},e.blotName="scroll",e.defaultChild="block",e.scope=y.Scope.BLOCK_BLOT,e.tagName="DIV",e}(w.default);u.default=d},function(x,u,c){var N=this&&this.__extends||function(){var d=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(o,e){o.__proto__=e}||function(o,e){for(var t in e)e.hasOwnProperty(t)&&(o[t]=e[t])};return function(o,e){d(o,e);function t(){this.constructor=o}o.prototype=e===null?Object.create(e):(t.prototype=e.prototype,new t)}}();Object.defineProperty(u,"__esModule",{value:!0});var w=c(18),y=c(1);function g(d,o){if(Object.keys(d).length!==Object.keys(o).length)return!1;for(var e in d)if(d[e]!==o[e])return!1;return!0}var m=function(d){N(o,d);function o(){return d!==null&&d.apply(this,arguments)||this}return o.formats=function(e){if(e.tagName!==o.tagName)return d.formats.call(this,e)},o.prototype.format=function(e,t){var a=this;e===this.statics.blotName&&!t?(this.children.forEach(function(s){s instanceof w.default||(s=s.wrap(o.blotName,!0)),a.attributes.copy(s)}),this.unwrap()):d.prototype.format.call(this,e,t)},o.prototype.formatAt=function(e,t,a,s){if(this.formats()[a]!=null||y.query(a,y.Scope.ATTRIBUTE)){var l=this.isolate(e,t);l.format(a,s)}else d.prototype.formatAt.call(this,e,t,a,s)},o.prototype.optimize=function(e){d.prototype.optimize.call(this,e);var t=this.formats();if(Object.keys(t).length===0)return this.unwrap();var a=this.next;a instanceof o&&a.prev===this&&g(t,a.formats())&&(a.moveChildren(this),a.remove())},o.blotName="inline",o.scope=y.Scope.INLINE_BLOT,o.tagName="SPAN",o}(w.default);u.default=m},function(x,u,c){var N=this&&this.__extends||function(){var m=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,o){d.__proto__=o}||function(d,o){for(var e in o)o.hasOwnProperty(e)&&(d[e]=o[e])};return function(d,o){m(d,o);function e(){this.constructor=d}d.prototype=o===null?Object.create(o):(e.prototype=o.prototype,new e)}}();Object.defineProperty(u,"__esModule",{value:!0});var w=c(18),y=c(1),g=function(m){N(d,m);function d(){return m!==null&&m.apply(this,arguments)||this}return d.formats=function(o){var e=y.query(d.blotName).tagName;if(o.tagName!==e)return m.formats.call(this,o)},d.prototype.format=function(o,e){y.query(o,y.Scope.BLOCK)!=null&&(o===this.statics.blotName&&!e?this.replaceWith(d.blotName):m.prototype.format.call(this,o,e))},d.prototype.formatAt=function(o,e,t,a){y.query(t,y.Scope.BLOCK)!=null?this.format(t,a):m.prototype.formatAt.call(this,o,e,t,a)},d.prototype.insertAt=function(o,e,t){if(t==null||y.query(e,y.Scope.INLINE)!=null)m.prototype.insertAt.call(this,o,e,t);else{var a=this.split(o),s=y.create(e,t);a.parent.insertBefore(s,a)}},d.prototype.update=function(o,e){navigator.userAgent.match(/Trident/)?this.build():m.prototype.update.call(this,o,e)},d.blotName="block",d.scope=y.Scope.BLOCK_BLOT,d.tagName="P",d}(w.default);u.default=g},function(x,u,c){var N=this&&this.__extends||function(){var g=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(m,d){m.__proto__=d}||function(m,d){for(var o in d)d.hasOwnProperty(o)&&(m[o]=d[o])};return function(m,d){g(m,d);function o(){this.constructor=m}m.prototype=d===null?Object.create(d):(o.prototype=d.prototype,new o)}}();Object.defineProperty(u,"__esModule",{value:!0});var w=c(19),y=function(g){N(m,g);function m(){return g!==null&&g.apply(this,arguments)||this}return m.formats=function(d){},m.prototype.format=function(d,o){g.prototype.formatAt.call(this,0,this.length(),d,o)},m.prototype.formatAt=function(d,o,e,t){d===0&&o===this.length()?this.format(e,t):g.prototype.formatAt.call(this,d,o,e,t)},m.prototype.formats=function(){return this.statics.formats(this.domNode)},m}(w.default);u.default=y},function(x,u,c){var N=this&&this.__extends||function(){var m=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,o){d.__proto__=o}||function(d,o){for(var e in o)o.hasOwnProperty(e)&&(d[e]=o[e])};return function(d,o){m(d,o);function e(){this.constructor=d}d.prototype=o===null?Object.create(o):(e.prototype=o.prototype,new e)}}();Object.defineProperty(u,"__esModule",{value:!0});var w=c(19),y=c(1),g=function(m){N(d,m);function d(o){var e=m.call(this,o)||this;return e.text=e.statics.value(e.domNode),e}return d.create=function(o){return document.createTextNode(o)},d.value=function(o){var e=o.data;return e.normalize&&(e=e.normalize()),e},d.prototype.deleteAt=function(o,e){this.domNode.data=this.text=this.text.slice(0,o)+this.text.slice(o+e)},d.prototype.index=function(o,e){return this.domNode===o?e:-1},d.prototype.insertAt=function(o,e,t){t==null?(this.text=this.text.slice(0,o)+e+this.text.slice(o),this.domNode.data=this.text):m.prototype.insertAt.call(this,o,e,t)},d.prototype.length=function(){return this.text.length},d.prototype.optimize=function(o){m.prototype.optimize.call(this,o),this.text=this.statics.value(this.domNode),this.text.length===0?this.remove():this.next instanceof d&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())},d.prototype.position=function(o,e){return[this.domNode,o]},d.prototype.split=function(o,e){if(e===void 0&&(e=!1),!e){if(o===0)return this;if(o===this.length())return this.next}var t=y.create(this.domNode.splitText(o));return this.parent.insertBefore(t,this.next),this.text=this.statics.value(this.domNode),t},d.prototype.update=function(o,e){var t=this;o.some(function(a){return a.type==="characterData"&&a.target===t.domNode})&&(this.text=this.statics.value(this.domNode))},d.prototype.value=function(){return this.text},d.blotName="text",d.scope=y.Scope.INLINE_BLOT,d}(w.default);u.default=g},function(x,u,c){var N=document.createElement("div");if(N.classList.toggle("test-class",!1),N.classList.contains("test-class")){var w=DOMTokenList.prototype.toggle;DOMTokenList.prototype.toggle=function(y,g){return arguments.length>1&&!this.contains(y)==!g?g:w.call(this,y)}}String.prototype.startsWith||(String.prototype.startsWith=function(y,g){return g=g||0,this.substr(g,y.length)===y}),String.prototype.endsWith||(String.prototype.endsWith=function(y,g){var m=this.toString();(typeof g!="number"||!isFinite(g)||Math.floor(g)!==g||g>m.length)&&(g=m.length),g-=y.length;var d=m.indexOf(y,g);return d!==-1&&d===g}),Array.prototype.find||Object.defineProperty(Array.prototype,"find",{value:function(g){if(this===null)throw new TypeError("Array.prototype.find called on null or undefined");if(typeof g!="function")throw new TypeError("predicate must be a function");for(var m=Object(this),d=m.length>>>0,o=arguments[1],e,t=0;t<d;t++)if(e=m[t],g.call(o,e,t,m))return e}}),document.addEventListener("DOMContentLoaded",function(){document.execCommand("enableObjectResizing",!1,!1),document.execCommand("autoUrlDetect",!1,!1)})},function(x,u){var c=-1,N=1,w=0;function y(i,h,T){if(i==h)return i?[[w,i]]:[];(T<0||i.length<T)&&(T=null);var E=o(i,h),A=i.substring(0,E);i=i.substring(E),h=h.substring(E),E=e(i,h);var P=i.substring(i.length-E);i=i.substring(0,i.length-E),h=h.substring(0,h.length-E);var O=g(i,h);return A&&O.unshift([w,A]),P&&O.push([w,P]),a(O),T!=null&&(O=r(O,T)),O=n(O),O}function g(i,h){var T;if(!i)return[[N,h]];if(!h)return[[c,i]];var E=i.length>h.length?i:h,A=i.length>h.length?h:i,P=E.indexOf(A);if(P!=-1)return T=[[N,E.substring(0,P)],[w,A],[N,E.substring(P+A.length)]],i.length>h.length&&(T[0][0]=T[2][0]=c),T;if(A.length==1)return[[c,i],[N,h]];var O=t(i,h);if(O){var b=O[0],_=O[1],S=O[2],L=O[3],R=O[4],$=y(b,S),U=y(_,L);return $.concat([[w,R]],U)}return m(i,h)}function m(i,h){for(var T=i.length,E=h.length,A=Math.ceil((T+E)/2),P=A,O=2*A,b=new Array(O),_=new Array(O),S=0;S<O;S++)b[S]=-1,_[S]=-1;b[P+1]=0,_[P+1]=0;for(var L=T-E,R=L%2!=0,$=0,U=0,Y=0,M=0,q=0;q<A;q++){for(var j=-q+$;j<=q-U;j+=2){var D=P+j,B;j==-q||j!=q&&b[D-1]<b[D+1]?B=b[D+1]:B=b[D-1]+1;for(var H=B-j;B<T&&H<E&&i.charAt(B)==h.charAt(H);)B++,H++;if(b[D]=B,B>T)U+=2;else if(H>E)$+=2;else if(R){var F=P+L-j;if(F>=0&&F<O&&_[F]!=-1){var I=T-_[F];if(B>=I)return d(i,h,B,H)}}}for(var C=-q+Y;C<=q-M;C+=2){var F=P+C,I;C==-q||C!=q&&_[F-1]<_[F+1]?I=_[F+1]:I=_[F-1]+1;for(var z=I-C;I<T&&z<E&&i.charAt(T-I-1)==h.charAt(E-z-1);)I++,z++;if(_[F]=I,I>T)M+=2;else if(z>E)Y+=2;else if(!R){var D=P+L-C;if(D>=0&&D<O&&b[D]!=-1){var B=b[D],H=P+B-D;if(I=T-I,B>=I)return d(i,h,B,H)}}}}return[[c,i],[N,h]]}function d(i,h,T,E){var A=i.substring(0,T),P=h.substring(0,E),O=i.substring(T),b=h.substring(E),_=y(A,P),S=y(O,b);return _.concat(S)}function o(i,h){if(!i||!h||i.charAt(0)!=h.charAt(0))return 0;for(var T=0,E=Math.min(i.length,h.length),A=E,P=0;T<A;)i.substring(P,A)==h.substring(P,A)?(T=A,P=T):E=A,A=Math.floor((E-T)/2+T);return A}function e(i,h){if(!i||!h||i.charAt(i.length-1)!=h.charAt(h.length-1))return 0;for(var T=0,E=Math.min(i.length,h.length),A=E,P=0;T<A;)i.substring(i.length-A,i.length-P)==h.substring(h.length-A,h.length-P)?(T=A,P=T):E=A,A=Math.floor((E-T)/2+T);return A}function t(i,h){var T=i.length>h.length?i:h,E=i.length>h.length?h:i;if(T.length<4||E.length*2<T.length)return null;function A(U,Y,M){for(var q=U.substring(M,M+Math.floor(U.length/4)),j=-1,D="",B,H,F,I;(j=Y.indexOf(q,j+1))!=-1;){var C=o(U.substring(M),Y.substring(j)),z=e(U.substring(0,M),Y.substring(0,j));D.length<z+C&&(D=Y.substring(j-z,j)+Y.substring(j,j+C),B=U.substring(0,M-z),H=U.substring(M+C),F=Y.substring(0,j-z),I=Y.substring(j+C))}return D.length*2>=U.length?[B,H,F,I,D]:null}var P=A(T,E,Math.ceil(T.length/4)),O=A(T,E,Math.ceil(T.length/2)),b;if(!P&&!O)return null;O?P?b=P[4].length>O[4].length?P:O:b=O:b=P;var _,S,L,R;i.length>h.length?(_=b[0],S=b[1],L=b[2],R=b[3]):(L=b[0],R=b[1],_=b[2],S=b[3]);var $=b[4];return[_,S,L,R,$]}function a(i){i.push([w,""]);for(var h=0,T=0,E=0,A="",P="",O;h<i.length;)switch(i[h][0]){case N:E++,P+=i[h][1],h++;break;case c:T++,A+=i[h][1],h++;break;case w:T+E>1?(T!==0&&E!==0&&(O=o(P,A),O!==0&&(h-T-E>0&&i[h-T-E-1][0]==w?i[h-T-E-1][1]+=P.substring(0,O):(i.splice(0,0,[w,P.substring(0,O)]),h++),P=P.substring(O),A=A.substring(O)),O=e(P,A),O!==0&&(i[h][1]=P.substring(P.length-O)+i[h][1],P=P.substring(0,P.length-O),A=A.substring(0,A.length-O))),T===0?i.splice(h-E,T+E,[N,P]):E===0?i.splice(h-T,T+E,[c,A]):i.splice(h-T-E,T+E,[c,A],[N,P]),h=h-T-E+(T?1:0)+(E?1:0)+1):h!==0&&i[h-1][0]==w?(i[h-1][1]+=i[h][1],i.splice(h,1)):h++,E=0,T=0,A="",P="";break}i[i.length-1][1]===""&&i.pop();var b=!1;for(h=1;h<i.length-1;)i[h-1][0]==w&&i[h+1][0]==w&&(i[h][1].substring(i[h][1].length-i[h-1][1].length)==i[h-1][1]?(i[h][1]=i[h-1][1]+i[h][1].substring(0,i[h][1].length-i[h-1][1].length),i[h+1][1]=i[h-1][1]+i[h+1][1],i.splice(h-1,1),b=!0):i[h][1].substring(0,i[h+1][1].length)==i[h+1][1]&&(i[h-1][1]+=i[h+1][1],i[h][1]=i[h][1].substring(i[h+1][1].length)+i[h+1][1],i.splice(h+1,1),b=!0)),h++;b&&a(i)}var s=y;s.INSERT=N,s.DELETE=c,s.EQUAL=w,x.exports=s;function l(i,h){if(h===0)return[w,i];for(var T=0,E=0;E<i.length;E++){var A=i[E];if(A[0]===c||A[0]===w){var P=T+A[1].length;if(h===P)return[E+1,i];if(h<P){i=i.slice();var O=h-T,b=[A[0],A[1].slice(0,O)],_=[A[0],A[1].slice(O)];return i.splice(E,1,b,_),[E+1,i]}else T=P}}throw new Error("cursor_pos is out of bounds!")}function r(i,h){var T=l(i,h),E=T[1],A=T[0],P=E[A],O=E[A+1];if(P==null)return i;if(P[0]!==w)return i;if(O!=null&&P[1]+O[1]===O[1]+P[1])return E.splice(A,2,O,P),f(E,A,2);if(O!=null&&O[1].indexOf(P[1])===0){E.splice(A,2,[O[0],P[1]],[0,P[1]]);var b=O[1].slice(P[1].length);return b.length>0&&E.splice(A+2,0,[O[0],b]),f(E,A,3)}else return i}function n(i){for(var h=!1,T=function(O){return O.charCodeAt(0)>=56320&&O.charCodeAt(0)<=57343},E=function(O){return O.charCodeAt(O.length-1)>=55296&&O.charCodeAt(O.length-1)<=56319},A=2;A<i.length;A+=1)i[A-2][0]===w&&E(i[A-2][1])&&i[A-1][0]===c&&T(i[A-1][1])&&i[A][0]===N&&T(i[A][1])&&(h=!0,i[A-1][1]=i[A-2][1].slice(-1)+i[A-1][1],i[A][1]=i[A-2][1].slice(-1)+i[A][1],i[A-2][1]=i[A-2][1].slice(0,-1));if(!h)return i;for(var P=[],A=0;A<i.length;A+=1)i[A][1].length>0&&P.push(i[A]);return P}function f(i,h,T){for(var E=h+T-1;E>=0&&E>=h-1;E--)if(E+1<i.length){var A=i[E],P=i[E+1];A[0]===P[1]&&i.splice(E,2,[A[0],A[1]+P[1]])}return i}},function(x,u){u=x.exports=typeof Object.keys=="function"?Object.keys:c,u.shim=c;function c(N){var w=[];for(var y in N)w.push(y);return w}},function(x,u){var c=function(){return Object.prototype.toString.call(arguments)}()=="[object Arguments]";u=x.exports=c?N:w,u.supported=N;function N(y){return Object.prototype.toString.call(y)=="[object Arguments]"}u.unsupported=w;function w(y){return y&&typeof y=="object"&&typeof y.length=="number"&&Object.prototype.hasOwnProperty.call(y,"callee")&&!Object.prototype.propertyIsEnumerable.call(y,"callee")||!1}},function(x,u){var c=Object.prototype.hasOwnProperty,N="~";function w(){}Object.create&&(w.prototype=Object.create(null),new w().__proto__||(N=!1));function y(m,d,o){this.fn=m,this.context=d,this.once=o||!1}function g(){this._events=new w,this._eventsCount=0}g.prototype.eventNames=function(){var d=[],o,e;if(this._eventsCount===0)return d;for(e in o=this._events)c.call(o,e)&&d.push(N?e.slice(1):e);return Object.getOwnPropertySymbols?d.concat(Object.getOwnPropertySymbols(o)):d},g.prototype.listeners=function(d,o){var e=N?N+d:d,t=this._events[e];if(o)return!!t;if(!t)return[];if(t.fn)return[t.fn];for(var a=0,s=t.length,l=new Array(s);a<s;a++)l[a]=t[a].fn;return l},g.prototype.emit=function(d,o,e,t,a,s){var l=N?N+d:d;if(!this._events[l])return!1;var r=this._events[l],n=arguments.length,f,i;if(r.fn){switch(r.once&&this.removeListener(d,r.fn,void 0,!0),n){case 1:return r.fn.call(r.context),!0;case 2:return r.fn.call(r.context,o),!0;case 3:return r.fn.call(r.context,o,e),!0;case 4:return r.fn.call(r.context,o,e,t),!0;case 5:return r.fn.call(r.context,o,e,t,a),!0;case 6:return r.fn.call(r.context,o,e,t,a,s),!0}for(i=1,f=new Array(n-1);i<n;i++)f[i-1]=arguments[i];r.fn.apply(r.context,f)}else{var h=r.length,T;for(i=0;i<h;i++)switch(r[i].once&&this.removeListener(d,r[i].fn,void 0,!0),n){case 1:r[i].fn.call(r[i].context);break;case 2:r[i].fn.call(r[i].context,o);break;case 3:r[i].fn.call(r[i].context,o,e);break;case 4:r[i].fn.call(r[i].context,o,e,t);break;default:if(!f)for(T=1,f=new Array(n-1);T<n;T++)f[T-1]=arguments[T];r[i].fn.apply(r[i].context,f)}}return!0},g.prototype.on=function(d,o,e){var t=new y(o,e||this),a=N?N+d:d;return this._events[a]?this._events[a].fn?this._events[a]=[this._events[a],t]:this._events[a].push(t):(this._events[a]=t,this._eventsCount++),this},g.prototype.once=function(d,o,e){var t=new y(o,e||this,!0),a=N?N+d:d;return this._events[a]?this._events[a].fn?this._events[a]=[this._events[a],t]:this._events[a].push(t):(this._events[a]=t,this._eventsCount++),this},g.prototype.removeListener=function(d,o,e,t){var a=N?N+d:d;if(!this._events[a])return this;if(!o)return--this._eventsCount===0?this._events=new w:delete this._events[a],this;var s=this._events[a];if(s.fn)s.fn===o&&(!t||s.once)&&(!e||s.context===e)&&(--this._eventsCount===0?this._events=new w:delete this._events[a]);else{for(var l=0,r=[],n=s.length;l<n;l++)(s[l].fn!==o||t&&!s[l].once||e&&s[l].context!==e)&&r.push(s[l]);r.length?this._events[a]=r.length===1?r[0]:r:--this._eventsCount===0?this._events=new w:delete this._events[a]}return this},g.prototype.removeAllListeners=function(d){var o;return d?(o=N?N+d:d,this._events[o]&&(--this._eventsCount===0?this._events=new w:delete this._events[o])):(this._events=new w,this._eventsCount=0),this},g.prototype.off=g.prototype.removeListener,g.prototype.addListener=g.prototype.on,g.prototype.setMaxListeners=function(){return this},g.prefixed=N,g.EventEmitter=g,typeof x<"u"&&(x.exports=g)},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0}),u.matchText=u.matchSpacing=u.matchNewline=u.matchBlot=u.matchAttributor=u.default=void 0;var N=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},w=function(){function G(W,J){var te=[],X=!0,ae=!1,oe=void 0;try{for(var le=W[Symbol.iterator](),me;!(X=(me=le.next()).done)&&(te.push(me.value),!(J&&te.length===J));X=!0);}catch(xe){ae=!0,oe=xe}finally{try{!X&&le.return&&le.return()}finally{if(ae)throw oe}}return te}return function(W,J){if(Array.isArray(W))return W;if(Symbol.iterator in Object(W))return G(W,J);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),y=function(){function G(W,J){for(var te=0;te<J.length;te++){var X=J[te];X.enumerable=X.enumerable||!1,X.configurable=!0,"value"in X&&(X.writable=!0),Object.defineProperty(W,X.key,X)}}return function(W,J,te){return J&&G(W.prototype,J),te&&G(W,te),W}}(),g=c(3),m=_(g),d=c(2),o=_(d),e=c(0),t=_(e),a=c(5),s=_(a),l=c(10),r=_(l),n=c(9),f=_(n),i=c(36),h=c(37),T=c(13),E=_(T),A=c(26),P=c(38),O=c(39),b=c(40);function _(G){return G&&G.__esModule?G:{default:G}}function S(G,W,J){return W in G?Object.defineProperty(G,W,{value:J,enumerable:!0,configurable:!0,writable:!0}):G[W]=J,G}function L(G,W){if(!(G instanceof W))throw new TypeError("Cannot call a class as a function")}function R(G,W){if(!G)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return W&&(typeof W=="object"||typeof W=="function")?W:G}function $(G,W){if(typeof W!="function"&&W!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof W);G.prototype=Object.create(W&&W.prototype,{constructor:{value:G,enumerable:!1,writable:!0,configurable:!0}}),W&&(Object.setPrototypeOf?Object.setPrototypeOf(G,W):G.__proto__=W)}var U=(0,r.default)("quill:clipboard"),Y="__ql-matcher",M=[[Node.TEXT_NODE,ue],[Node.TEXT_NODE,ie],["br",ee],[Node.ELEMENT_NODE,ie],[Node.ELEMENT_NODE,Z],[Node.ELEMENT_NODE,se],[Node.ELEMENT_NODE,K],[Node.ELEMENT_NODE,he],["li",Q],["b",z.bind(z,"bold")],["i",z.bind(z,"italic")],["style",V]],q=[i.AlignAttribute,P.DirectionAttribute].reduce(function(G,W){return G[W.keyName]=W,G},{}),j=[i.AlignStyle,h.BackgroundStyle,A.ColorStyle,P.DirectionStyle,O.FontStyle,b.SizeStyle].reduce(function(G,W){return G[W.keyName]=W,G},{}),D=function(G){$(W,G);function W(J,te){L(this,W);var X=R(this,(W.__proto__||Object.getPrototypeOf(W)).call(this,J,te));return X.quill.root.addEventListener("paste",X.onPaste.bind(X)),X.container=X.quill.addContainer("ql-clipboard"),X.container.setAttribute("contenteditable",!0),X.container.setAttribute("tabindex",-1),X.matchers=[],M.concat(X.options.matchers).forEach(function(ae){var oe=w(ae,2),le=oe[0],me=oe[1];!te.matchVisual&&me===se||X.addMatcher(le,me)}),X}return y(W,[{key:"addMatcher",value:function(te,X){this.matchers.push([te,X])}},{key:"convert",value:function(te){if(typeof te=="string")return this.container.innerHTML=te.replace(/\>\r?\n +\</g,"><"),this.convert();var X=this.quill.getFormat(this.quill.selection.savedRange.index);if(X[E.default.blotName]){var ae=this.container.innerText;return this.container.innerHTML="",new o.default().insert(ae,S({},E.default.blotName,X[E.default.blotName]))}var oe=this.prepareMatching(),le=w(oe,2),me=le[0],xe=le[1],de=C(this.container,me,xe);return F(de,`
`)&&de.ops[de.ops.length-1].attributes==null&&(de=de.compose(new o.default().retain(de.length()-1).delete(1))),U.log("convert",this.container.innerHTML,de),this.container.innerHTML="",de}},{key:"dangerouslyPasteHTML",value:function(te,X){var ae=arguments.length>2&&arguments[2]!==void 0?arguments[2]:s.default.sources.API;if(typeof te=="string")this.quill.setContents(this.convert(te),X),this.quill.setSelection(0,s.default.sources.SILENT);else{var oe=this.convert(X);this.quill.updateContents(new o.default().retain(te).concat(oe),ae),this.quill.setSelection(te+oe.length(),s.default.sources.SILENT)}}},{key:"onPaste",value:function(te){var X=this;if(!(te.defaultPrevented||!this.quill.isEnabled())){var ae=this.quill.getSelection(),oe=new o.default().retain(ae.index),le=this.quill.scrollingContainer.scrollTop;this.container.focus(),this.quill.selection.update(s.default.sources.SILENT),setTimeout(function(){oe=oe.concat(X.convert()).delete(ae.length),X.quill.updateContents(oe,s.default.sources.USER),X.quill.setSelection(oe.length()-ae.length,s.default.sources.SILENT),X.quill.scrollingContainer.scrollTop=le,X.quill.focus()},1)}}},{key:"prepareMatching",value:function(){var te=this,X=[],ae=[];return this.matchers.forEach(function(oe){var le=w(oe,2),me=le[0],xe=le[1];switch(me){case Node.TEXT_NODE:ae.push(xe);break;case Node.ELEMENT_NODE:X.push(xe);break;default:[].forEach.call(te.container.querySelectorAll(me),function(de){de[Y]=de[Y]||[],de[Y].push(xe)});break}}),[X,ae]}}]),W}(f.default);D.DEFAULTS={matchers:[],matchVisual:!0};function B(G,W,J){return(typeof W>"u"?"undefined":N(W))==="object"?Object.keys(W).reduce(function(te,X){return B(te,X,W[X])},G):G.reduce(function(te,X){return X.attributes&&X.attributes[W]?te.push(X):te.insert(X.insert,(0,m.default)({},S({},W,J),X.attributes))},new o.default)}function H(G){if(G.nodeType!==Node.ELEMENT_NODE)return{};var W="__ql-computed-style";return G[W]||(G[W]=window.getComputedStyle(G))}function F(G,W){for(var J="",te=G.ops.length-1;te>=0&&J.length<W.length;--te){var X=G.ops[te];if(typeof X.insert!="string")break;J=X.insert+J}return J.slice(-1*W.length)===W}function I(G){if(G.childNodes.length===0)return!1;var W=H(G);return["block","list-item"].indexOf(W.display)>-1}function C(G,W,J){return G.nodeType===G.TEXT_NODE?J.reduce(function(te,X){return X(G,te)},new o.default):G.nodeType===G.ELEMENT_NODE?[].reduce.call(G.childNodes||[],function(te,X){var ae=C(X,W,J);return X.nodeType===G.ELEMENT_NODE&&(ae=W.reduce(function(oe,le){return le(X,oe)},ae),ae=(X[Y]||[]).reduce(function(oe,le){return le(X,oe)},ae)),te.concat(ae)},new o.default):new o.default}function z(G,W,J){return B(J,G,!0)}function K(G,W){var J=t.default.Attributor.Attribute.keys(G),te=t.default.Attributor.Class.keys(G),X=t.default.Attributor.Style.keys(G),ae={};return J.concat(te).concat(X).forEach(function(oe){var le=t.default.query(oe,t.default.Scope.ATTRIBUTE);le!=null&&(ae[le.attrName]=le.value(G),ae[le.attrName])||(le=q[oe],le!=null&&(le.attrName===oe||le.keyName===oe)&&(ae[le.attrName]=le.value(G)||void 0),le=j[oe],le!=null&&(le.attrName===oe||le.keyName===oe)&&(le=j[oe],ae[le.attrName]=le.value(G)||void 0))}),Object.keys(ae).length>0&&(W=B(W,ae)),W}function Z(G,W){var J=t.default.query(G);if(J==null)return W;if(J.prototype instanceof t.default.Embed){var te={},X=J.value(G);X!=null&&(te[J.blotName]=X,W=new o.default().insert(te,J.formats(G)))}else typeof J.formats=="function"&&(W=B(W,J.blotName,J.formats(G)));return W}function ee(G,W){return F(W,`
`)||W.insert(`
`),W}function V(){return new o.default}function Q(G,W){var J=t.default.query(G);if(J==null||J.blotName!=="list-item"||!F(W,`
`))return W;for(var te=-1,X=G.parentNode;!X.classList.contains("ql-clipboard");)(t.default.query(X)||{}).blotName==="list"&&(te+=1),X=X.parentNode;return te<=0?W:W.compose(new o.default().retain(W.length()-1).retain(1,{indent:te}))}function ie(G,W){return F(W,`
`)||(I(G)||W.length()>0&&G.nextSibling&&I(G.nextSibling))&&W.insert(`
`),W}function se(G,W){if(I(G)&&G.nextElementSibling!=null&&!F(W,`

`)){var J=G.offsetHeight+parseFloat(H(G).marginTop)+parseFloat(H(G).marginBottom);G.nextElementSibling.offsetTop>G.offsetTop+J*1.5&&W.insert(`
`)}return W}function he(G,W){var J={},te=G.style||{};return te.fontStyle&&H(G).fontStyle==="italic"&&(J.italic=!0),te.fontWeight&&(H(G).fontWeight.startsWith("bold")||parseInt(H(G).fontWeight)>=700)&&(J.bold=!0),Object.keys(J).length>0&&(W=B(W,J)),parseFloat(te.textIndent||0)>0&&(W=new o.default().insert("	").concat(W)),W}function ue(G,W){var J=G.data;if(G.parentNode.tagName==="O:P")return W.insert(J.trim());if(J.trim().length===0&&G.parentNode.classList.contains("ql-clipboard"))return W;if(!H(G.parentNode).whiteSpace.startsWith("pre")){var te=function(ae,oe){return oe=oe.replace(/[^\u00a0]/g,""),oe.length<1&&ae?" ":oe};J=J.replace(/\r\n/g," ").replace(/\n/g," "),J=J.replace(/\s\s+/g,te.bind(te,!0)),(G.previousSibling==null&&I(G.parentNode)||G.previousSibling!=null&&I(G.previousSibling))&&(J=J.replace(/^\s+/,te.bind(te,!1))),(G.nextSibling==null&&I(G.parentNode)||G.nextSibling!=null&&I(G.nextSibling))&&(J=J.replace(/\s+$/,te.bind(te,!1)))}return W.insert(J)}u.default=D,u.matchAttributor=K,u.matchBlot=Z,u.matchNewline=ie,u.matchSpacing=se,u.matchText=ue},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=function(){function a(s,l){for(var r=0;r<l.length;r++){var n=l[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(s,n.key,n)}}return function(s,l,r){return l&&a(s.prototype,l),r&&a(s,r),s}}(),w=function a(s,l,r){s===null&&(s=Function.prototype);var n=Object.getOwnPropertyDescriptor(s,l);if(n===void 0){var f=Object.getPrototypeOf(s);return f===null?void 0:a(f,l,r)}else{if("value"in n)return n.value;var i=n.get;return i===void 0?void 0:i.call(r)}},y=c(6),g=m(y);function m(a){return a&&a.__esModule?a:{default:a}}function d(a,s){if(!(a instanceof s))throw new TypeError("Cannot call a class as a function")}function o(a,s){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s&&(typeof s=="object"||typeof s=="function")?s:a}function e(a,s){if(typeof s!="function"&&s!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof s);a.prototype=Object.create(s&&s.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),s&&(Object.setPrototypeOf?Object.setPrototypeOf(a,s):a.__proto__=s)}var t=function(a){e(s,a);function s(){return d(this,s),o(this,(s.__proto__||Object.getPrototypeOf(s)).apply(this,arguments))}return N(s,[{key:"optimize",value:function(r){w(s.prototype.__proto__||Object.getPrototypeOf(s.prototype),"optimize",this).call(this,r),this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}}],[{key:"create",value:function(){return w(s.__proto__||Object.getPrototypeOf(s),"create",this).call(this)}},{key:"formats",value:function(){return!0}}]),s}(g.default);t.blotName="bold",t.tagName=["STRONG","B"],u.default=t},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0}),u.addControls=u.default=void 0;var N=function(){function b(_,S){var L=[],R=!0,$=!1,U=void 0;try{for(var Y=_[Symbol.iterator](),M;!(R=(M=Y.next()).done)&&(L.push(M.value),!(S&&L.length===S));R=!0);}catch(q){$=!0,U=q}finally{try{!R&&Y.return&&Y.return()}finally{if($)throw U}}return L}return function(_,S){if(Array.isArray(_))return _;if(Symbol.iterator in Object(_))return b(_,S);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),w=function(){function b(_,S){for(var L=0;L<S.length;L++){var R=S[L];R.enumerable=R.enumerable||!1,R.configurable=!0,"value"in R&&(R.writable=!0),Object.defineProperty(_,R.key,R)}}return function(_,S,L){return S&&b(_.prototype,S),L&&b(_,L),_}}(),y=c(2),g=r(y),m=c(0),d=r(m),o=c(5),e=r(o),t=c(10),a=r(t),s=c(9),l=r(s);function r(b){return b&&b.__esModule?b:{default:b}}function n(b,_,S){return _ in b?Object.defineProperty(b,_,{value:S,enumerable:!0,configurable:!0,writable:!0}):b[_]=S,b}function f(b,_){if(!(b instanceof _))throw new TypeError("Cannot call a class as a function")}function i(b,_){if(!b)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return _&&(typeof _=="object"||typeof _=="function")?_:b}function h(b,_){if(typeof _!="function"&&_!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof _);b.prototype=Object.create(_&&_.prototype,{constructor:{value:b,enumerable:!1,writable:!0,configurable:!0}}),_&&(Object.setPrototypeOf?Object.setPrototypeOf(b,_):b.__proto__=_)}var T=(0,a.default)("quill:toolbar"),E=function(b){h(_,b);function _(S,L){f(this,_);var R=i(this,(_.__proto__||Object.getPrototypeOf(_)).call(this,S,L));if(Array.isArray(R.options.container)){var $=document.createElement("div");P($,R.options.container),S.container.parentNode.insertBefore($,S.container),R.container=$}else typeof R.options.container=="string"?R.container=document.querySelector(R.options.container):R.container=R.options.container;if(!(R.container instanceof HTMLElement)){var U;return U=T.error("Container required for toolbar",R.options),i(R,U)}return R.container.classList.add("ql-toolbar"),R.controls=[],R.handlers={},Object.keys(R.options.handlers).forEach(function(Y){R.addHandler(Y,R.options.handlers[Y])}),[].forEach.call(R.container.querySelectorAll("button, select"),function(Y){R.attach(Y)}),R.quill.on(e.default.events.EDITOR_CHANGE,function(Y,M){Y===e.default.events.SELECTION_CHANGE&&R.update(M)}),R.quill.on(e.default.events.SCROLL_OPTIMIZE,function(){var Y=R.quill.selection.getRange(),M=N(Y,1),q=M[0];R.update(q)}),R}return w(_,[{key:"addHandler",value:function(L,R){this.handlers[L]=R}},{key:"attach",value:function(L){var R=this,$=[].find.call(L.classList,function(Y){return Y.indexOf("ql-")===0});if($){if($=$.slice(3),L.tagName==="BUTTON"&&L.setAttribute("type","button"),this.handlers[$]==null){if(this.quill.scroll.whitelist!=null&&this.quill.scroll.whitelist[$]==null){T.warn("ignoring attaching to disabled format",$,L);return}if(d.default.query($)==null){T.warn("ignoring attaching to nonexistent format",$,L);return}}var U=L.tagName==="SELECT"?"change":"click";L.addEventListener(U,function(Y){var M=void 0;if(L.tagName==="SELECT"){if(L.selectedIndex<0)return;var q=L.options[L.selectedIndex];q.hasAttribute("selected")?M=!1:M=q.value||!1}else L.classList.contains("ql-active")?M=!1:M=L.value||!L.hasAttribute("value"),Y.preventDefault();R.quill.focus();var j=R.quill.selection.getRange(),D=N(j,1),B=D[0];if(R.handlers[$]!=null)R.handlers[$].call(R,M);else if(d.default.query($).prototype instanceof d.default.Embed){if(M=prompt("Enter "+$),!M)return;R.quill.updateContents(new g.default().retain(B.index).delete(B.length).insert(n({},$,M)),e.default.sources.USER)}else R.quill.format($,M,e.default.sources.USER);R.update(B)}),this.controls.push([$,L])}}},{key:"update",value:function(L){var R=L==null?{}:this.quill.getFormat(L);this.controls.forEach(function($){var U=N($,2),Y=U[0],M=U[1];if(M.tagName==="SELECT"){var q=void 0;if(L==null)q=null;else if(R[Y]==null)q=M.querySelector("option[selected]");else if(!Array.isArray(R[Y])){var j=R[Y];typeof j=="string"&&(j=j.replace(/\"/g,'\\"')),q=M.querySelector('option[value="'+j+'"]')}q==null?(M.value="",M.selectedIndex=-1):q.selected=!0}else if(L==null)M.classList.remove("ql-active");else if(M.hasAttribute("value")){var D=R[Y]===M.getAttribute("value")||R[Y]!=null&&R[Y].toString()===M.getAttribute("value")||R[Y]==null&&!M.getAttribute("value");M.classList.toggle("ql-active",D)}else M.classList.toggle("ql-active",R[Y]!=null)})}}]),_}(l.default);E.DEFAULTS={};function A(b,_,S){var L=document.createElement("button");L.setAttribute("type","button"),L.classList.add("ql-"+_),S!=null&&(L.value=S),b.appendChild(L)}function P(b,_){Array.isArray(_[0])||(_=[_]),_.forEach(function(S){var L=document.createElement("span");L.classList.add("ql-formats"),S.forEach(function(R){if(typeof R=="string")A(L,R);else{var $=Object.keys(R)[0],U=R[$];Array.isArray(U)?O(L,$,U):A(L,$,U)}}),b.appendChild(L)})}function O(b,_,S){var L=document.createElement("select");L.classList.add("ql-"+_),S.forEach(function(R){var $=document.createElement("option");R!==!1?$.setAttribute("value",R):$.setAttribute("selected","selected"),L.appendChild($)}),b.appendChild(L)}E.DEFAULTS={container:null,handlers:{clean:function(){var _=this,S=this.quill.getSelection();if(S!=null)if(S.length==0){var L=this.quill.getFormat();Object.keys(L).forEach(function(R){d.default.query(R,d.default.Scope.INLINE)!=null&&_.quill.format(R,!1)})}else this.quill.removeFormat(S,e.default.sources.USER)},direction:function(_){var S=this.quill.getFormat().align;_==="rtl"&&S==null?this.quill.format("align","right",e.default.sources.USER):!_&&S==="right"&&this.quill.format("align",!1,e.default.sources.USER),this.quill.format("direction",_,e.default.sources.USER)},indent:function(_){var S=this.quill.getSelection(),L=this.quill.getFormat(S),R=parseInt(L.indent||0);if(_==="+1"||_==="-1"){var $=_==="+1"?1:-1;L.direction==="rtl"&&($*=-1),this.quill.format("indent",R+$,e.default.sources.USER)}},link:function(_){_===!0&&(_=prompt("Enter link URL:")),this.quill.format("link",_,e.default.sources.USER)},list:function(_){var S=this.quill.getSelection(),L=this.quill.getFormat(S);_==="check"?L.list==="checked"||L.list==="unchecked"?this.quill.format("list",!1,e.default.sources.USER):this.quill.format("list","unchecked",e.default.sources.USER):this.quill.format("list",_,e.default.sources.USER)}}},u.default=E,u.addControls=P},function(x,u){x.exports='<svg viewbox="0 0 18 18"> <polyline class="ql-even ql-stroke" points="5 7 3 9 5 11"></polyline> <polyline class="ql-even ql-stroke" points="13 7 15 9 13 11"></polyline> <line class=ql-stroke x1=10 x2=8 y1=5 y2=13></line> </svg>'},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=function(){function a(s,l){for(var r=0;r<l.length;r++){var n=l[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(s,n.key,n)}}return function(s,l,r){return l&&a(s.prototype,l),r&&a(s,r),s}}(),w=function a(s,l,r){s===null&&(s=Function.prototype);var n=Object.getOwnPropertyDescriptor(s,l);if(n===void 0){var f=Object.getPrototypeOf(s);return f===null?void 0:a(f,l,r)}else{if("value"in n)return n.value;var i=n.get;return i===void 0?void 0:i.call(r)}},y=c(28),g=m(y);function m(a){return a&&a.__esModule?a:{default:a}}function d(a,s){if(!(a instanceof s))throw new TypeError("Cannot call a class as a function")}function o(a,s){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s&&(typeof s=="object"||typeof s=="function")?s:a}function e(a,s){if(typeof s!="function"&&s!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof s);a.prototype=Object.create(s&&s.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),s&&(Object.setPrototypeOf?Object.setPrototypeOf(a,s):a.__proto__=s)}var t=function(a){e(s,a);function s(l,r){d(this,s);var n=o(this,(s.__proto__||Object.getPrototypeOf(s)).call(this,l));return n.label.innerHTML=r,n.container.classList.add("ql-color-picker"),[].slice.call(n.container.querySelectorAll(".ql-picker-item"),0,7).forEach(function(f){f.classList.add("ql-primary")}),n}return N(s,[{key:"buildItem",value:function(r){var n=w(s.prototype.__proto__||Object.getPrototypeOf(s.prototype),"buildItem",this).call(this,r);return n.style.backgroundColor=r.getAttribute("value")||"",n}},{key:"selectItem",value:function(r,n){w(s.prototype.__proto__||Object.getPrototypeOf(s.prototype),"selectItem",this).call(this,r,n);var f=this.label.querySelector(".ql-color-label"),i=r&&r.getAttribute("data-value")||"";f&&(f.tagName==="line"?f.style.stroke=i:f.style.fill=i)}}]),s}(g.default);u.default=t},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=function(){function a(s,l){for(var r=0;r<l.length;r++){var n=l[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(s,n.key,n)}}return function(s,l,r){return l&&a(s.prototype,l),r&&a(s,r),s}}(),w=function a(s,l,r){s===null&&(s=Function.prototype);var n=Object.getOwnPropertyDescriptor(s,l);if(n===void 0){var f=Object.getPrototypeOf(s);return f===null?void 0:a(f,l,r)}else{if("value"in n)return n.value;var i=n.get;return i===void 0?void 0:i.call(r)}},y=c(28),g=m(y);function m(a){return a&&a.__esModule?a:{default:a}}function d(a,s){if(!(a instanceof s))throw new TypeError("Cannot call a class as a function")}function o(a,s){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s&&(typeof s=="object"||typeof s=="function")?s:a}function e(a,s){if(typeof s!="function"&&s!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof s);a.prototype=Object.create(s&&s.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),s&&(Object.setPrototypeOf?Object.setPrototypeOf(a,s):a.__proto__=s)}var t=function(a){e(s,a);function s(l,r){d(this,s);var n=o(this,(s.__proto__||Object.getPrototypeOf(s)).call(this,l));return n.container.classList.add("ql-icon-picker"),[].forEach.call(n.container.querySelectorAll(".ql-picker-item"),function(f){f.innerHTML=r[f.getAttribute("data-value")||""]}),n.defaultItem=n.container.querySelector(".ql-selected"),n.selectItem(n.defaultItem),n}return N(s,[{key:"selectItem",value:function(r,n){w(s.prototype.__proto__||Object.getPrototypeOf(s.prototype),"selectItem",this).call(this,r,n),r=r||this.defaultItem,this.label.innerHTML=r.innerHTML}}]),s}(g.default);u.default=t},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=function(){function g(m,d){for(var o=0;o<d.length;o++){var e=d[o];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(m,e.key,e)}}return function(m,d,o){return d&&g(m.prototype,d),o&&g(m,o),m}}();function w(g,m){if(!(g instanceof m))throw new TypeError("Cannot call a class as a function")}var y=function(){function g(m,d){var o=this;w(this,g),this.quill=m,this.boundsContainer=d||document.body,this.root=m.addContainer("ql-tooltip"),this.root.innerHTML=this.constructor.TEMPLATE,this.quill.root===this.quill.scrollingContainer&&this.quill.root.addEventListener("scroll",function(){o.root.style.marginTop=-1*o.quill.root.scrollTop+"px"}),this.hide()}return N(g,[{key:"hide",value:function(){this.root.classList.add("ql-hidden")}},{key:"position",value:function(d){var o=d.left+d.width/2-this.root.offsetWidth/2,e=d.bottom+this.quill.root.scrollTop;this.root.style.left=o+"px",this.root.style.top=e+"px",this.root.classList.remove("ql-flip");var t=this.boundsContainer.getBoundingClientRect(),a=this.root.getBoundingClientRect(),s=0;if(a.right>t.right&&(s=t.right-a.right,this.root.style.left=o+s+"px"),a.left<t.left&&(s=t.left-a.left,this.root.style.left=o+s+"px"),a.bottom>t.bottom){var l=a.bottom-a.top,r=d.bottom-d.top+l;this.root.style.top=e-r+"px",this.root.classList.add("ql-flip")}return s}},{key:"show",value:function(){this.root.classList.remove("ql-editing"),this.root.classList.remove("ql-hidden")}}]),g}();u.default=y},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=function(){function O(b,_){var S=[],L=!0,R=!1,$=void 0;try{for(var U=b[Symbol.iterator](),Y;!(L=(Y=U.next()).done)&&(S.push(Y.value),!(_&&S.length===_));L=!0);}catch(M){R=!0,$=M}finally{try{!L&&U.return&&U.return()}finally{if(R)throw $}}return S}return function(b,_){if(Array.isArray(b))return b;if(Symbol.iterator in Object(b))return O(b,_);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),w=function O(b,_,S){b===null&&(b=Function.prototype);var L=Object.getOwnPropertyDescriptor(b,_);if(L===void 0){var R=Object.getPrototypeOf(b);return R===null?void 0:O(R,_,S)}else{if("value"in L)return L.value;var $=L.get;return $===void 0?void 0:$.call(S)}},y=function(){function O(b,_){for(var S=0;S<_.length;S++){var L=_[S];L.enumerable=L.enumerable||!1,L.configurable=!0,"value"in L&&(L.writable=!0),Object.defineProperty(b,L.key,L)}}return function(b,_,S){return _&&O(b.prototype,_),S&&O(b,S),b}}(),g=c(3),m=f(g),d=c(8),o=f(d),e=c(43),t=f(e),a=c(27),s=f(a),l=c(15),r=c(41),n=f(r);function f(O){return O&&O.__esModule?O:{default:O}}function i(O,b){if(!(O instanceof b))throw new TypeError("Cannot call a class as a function")}function h(O,b){if(!O)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return b&&(typeof b=="object"||typeof b=="function")?b:O}function T(O,b){if(typeof b!="function"&&b!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof b);O.prototype=Object.create(b&&b.prototype,{constructor:{value:O,enumerable:!1,writable:!0,configurable:!0}}),b&&(Object.setPrototypeOf?Object.setPrototypeOf(O,b):O.__proto__=b)}var E=[[{header:["1","2","3",!1]}],["bold","italic","underline","link"],[{list:"ordered"},{list:"bullet"}],["clean"]],A=function(O){T(b,O);function b(_,S){i(this,b),S.modules.toolbar!=null&&S.modules.toolbar.container==null&&(S.modules.toolbar.container=E);var L=h(this,(b.__proto__||Object.getPrototypeOf(b)).call(this,_,S));return L.quill.container.classList.add("ql-snow"),L}return y(b,[{key:"extendToolbar",value:function(S){S.container.classList.add("ql-snow"),this.buildButtons([].slice.call(S.container.querySelectorAll("button")),n.default),this.buildPickers([].slice.call(S.container.querySelectorAll("select")),n.default),this.tooltip=new P(this.quill,this.options.bounds),S.container.querySelector(".ql-link")&&this.quill.keyboard.addBinding({key:"K",shortKey:!0},function(L,R){S.handlers.link.call(S,!R.format.link)})}}]),b}(t.default);A.DEFAULTS=(0,m.default)(!0,{},t.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(b){if(b){var _=this.quill.getSelection();if(_==null||_.length==0)return;var S=this.quill.getText(_);/^\S+@\S+\.\S+$/.test(S)&&S.indexOf("mailto:")!==0&&(S="mailto:"+S);var L=this.quill.theme.tooltip;L.edit("link",S)}else this.quill.format("link",!1)}}}}});var P=function(O){T(b,O);function b(_,S){i(this,b);var L=h(this,(b.__proto__||Object.getPrototypeOf(b)).call(this,_,S));return L.preview=L.root.querySelector("a.ql-preview"),L}return y(b,[{key:"listen",value:function(){var S=this;w(b.prototype.__proto__||Object.getPrototypeOf(b.prototype),"listen",this).call(this),this.root.querySelector("a.ql-action").addEventListener("click",function(L){S.root.classList.contains("ql-editing")?S.save():S.edit("link",S.preview.textContent),L.preventDefault()}),this.root.querySelector("a.ql-remove").addEventListener("click",function(L){if(S.linkRange!=null){var R=S.linkRange;S.restoreFocus(),S.quill.formatText(R,"link",!1,o.default.sources.USER),delete S.linkRange}L.preventDefault(),S.hide()}),this.quill.on(o.default.events.SELECTION_CHANGE,function(L,R,$){if(L!=null){if(L.length===0&&$===o.default.sources.USER){var U=S.quill.scroll.descendant(s.default,L.index),Y=N(U,2),M=Y[0],q=Y[1];if(M!=null){S.linkRange=new l.Range(L.index-q,M.length());var j=s.default.formats(M.domNode);S.preview.textContent=j,S.preview.setAttribute("href",j),S.show(),S.position(S.quill.getBounds(S.linkRange));return}}else delete S.linkRange;S.hide()}})}},{key:"show",value:function(){w(b.prototype.__proto__||Object.getPrototypeOf(b.prototype),"show",this).call(this),this.root.removeAttribute("data-mode")}}]),b}(e.BaseTooltip);P.TEMPLATE=['<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a>','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-action"></a>','<a class="ql-remove"></a>'].join(""),u.default=A},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=c(29),w=X(N),y=c(36),g=c(38),m=c(64),d=c(65),o=X(d),e=c(66),t=X(e),a=c(67),s=X(a),l=c(37),r=c(26),n=c(39),f=c(40),i=c(56),h=X(i),T=c(68),E=X(T),A=c(27),P=X(A),O=c(69),b=X(O),_=c(70),S=X(_),L=c(71),R=X(L),$=c(72),U=X($),Y=c(73),M=X(Y),q=c(13),j=X(q),D=c(74),B=X(D),H=c(75),F=X(H),I=c(57),C=X(I),z=c(41),K=X(z),Z=c(28),ee=X(Z),V=c(59),Q=X(V),ie=c(60),se=X(ie),he=c(61),ue=X(he),G=c(108),W=X(G),J=c(62),te=X(J);function X(ae){return ae&&ae.__esModule?ae:{default:ae}}w.default.register({"attributors/attribute/direction":g.DirectionAttribute,"attributors/class/align":y.AlignClass,"attributors/class/background":l.BackgroundClass,"attributors/class/color":r.ColorClass,"attributors/class/direction":g.DirectionClass,"attributors/class/font":n.FontClass,"attributors/class/size":f.SizeClass,"attributors/style/align":y.AlignStyle,"attributors/style/background":l.BackgroundStyle,"attributors/style/color":r.ColorStyle,"attributors/style/direction":g.DirectionStyle,"attributors/style/font":n.FontStyle,"attributors/style/size":f.SizeStyle},!0),w.default.register({"formats/align":y.AlignClass,"formats/direction":g.DirectionClass,"formats/indent":m.IndentClass,"formats/background":l.BackgroundStyle,"formats/color":r.ColorStyle,"formats/font":n.FontClass,"formats/size":f.SizeClass,"formats/blockquote":o.default,"formats/code-block":j.default,"formats/header":t.default,"formats/list":s.default,"formats/bold":h.default,"formats/code":q.Code,"formats/italic":E.default,"formats/link":P.default,"formats/script":b.default,"formats/strike":S.default,"formats/underline":R.default,"formats/image":U.default,"formats/video":M.default,"formats/list/item":a.ListItem,"modules/formula":B.default,"modules/syntax":F.default,"modules/toolbar":C.default,"themes/bubble":W.default,"themes/snow":te.default,"ui/icons":K.default,"ui/picker":ee.default,"ui/icon-picker":se.default,"ui/color-picker":Q.default,"ui/tooltip":ue.default},!0),u.default=w.default},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0}),u.IndentClass=void 0;var N=function(){function s(l,r){for(var n=0;n<r.length;n++){var f=r[n];f.enumerable=f.enumerable||!1,f.configurable=!0,"value"in f&&(f.writable=!0),Object.defineProperty(l,f.key,f)}}return function(l,r,n){return r&&s(l.prototype,r),n&&s(l,n),l}}(),w=function s(l,r,n){l===null&&(l=Function.prototype);var f=Object.getOwnPropertyDescriptor(l,r);if(f===void 0){var i=Object.getPrototypeOf(l);return i===null?void 0:s(i,r,n)}else{if("value"in f)return f.value;var h=f.get;return h===void 0?void 0:h.call(n)}},y=c(0),g=m(y);function m(s){return s&&s.__esModule?s:{default:s}}function d(s,l){if(!(s instanceof l))throw new TypeError("Cannot call a class as a function")}function o(s,l){if(!s)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:s}function e(s,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);s.prototype=Object.create(l&&l.prototype,{constructor:{value:s,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(s,l):s.__proto__=l)}var t=function(s){e(l,s);function l(){return d(this,l),o(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}return N(l,[{key:"add",value:function(n,f){if(f==="+1"||f==="-1"){var i=this.value(n)||0;f=f==="+1"?i+1:i-1}return f===0?(this.remove(n),!0):w(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"add",this).call(this,n,f)}},{key:"canAdd",value:function(n,f){return w(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"canAdd",this).call(this,n,f)||w(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"canAdd",this).call(this,n,parseInt(f))}},{key:"value",value:function(n){return parseInt(w(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"value",this).call(this,n))||void 0}}]),l}(g.default.Attributor.Class),a=new t("indent","ql-indent",{scope:g.default.Scope.BLOCK,whitelist:[1,2,3,4,5,6,7,8]});u.IndentClass=a},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=c(4),w=y(N);function y(e){return e&&e.__esModule?e:{default:e}}function g(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function m(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function d(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var o=function(e){d(t,e);function t(){return g(this,t),m(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return t}(w.default);o.blotName="blockquote",o.tagName="blockquote",u.default=o},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=function(){function t(a,s){for(var l=0;l<s.length;l++){var r=s[l];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(a,r.key,r)}}return function(a,s,l){return s&&t(a.prototype,s),l&&t(a,l),a}}(),w=c(4),y=g(w);function g(t){return t&&t.__esModule?t:{default:t}}function m(t,a){if(!(t instanceof a))throw new TypeError("Cannot call a class as a function")}function d(t,a){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a&&(typeof a=="object"||typeof a=="function")?a:t}function o(t,a){if(typeof a!="function"&&a!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof a);t.prototype=Object.create(a&&a.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),a&&(Object.setPrototypeOf?Object.setPrototypeOf(t,a):t.__proto__=a)}var e=function(t){o(a,t);function a(){return m(this,a),d(this,(a.__proto__||Object.getPrototypeOf(a)).apply(this,arguments))}return N(a,null,[{key:"formats",value:function(l){return this.tagName.indexOf(l.tagName)+1}}]),a}(y.default);e.blotName="header",e.tagName=["H1","H2","H3","H4","H5","H6"],u.default=e},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0}),u.default=u.ListItem=void 0;var N=function(){function i(h,T){for(var E=0;E<T.length;E++){var A=T[E];A.enumerable=A.enumerable||!1,A.configurable=!0,"value"in A&&(A.writable=!0),Object.defineProperty(h,A.key,A)}}return function(h,T,E){return T&&i(h.prototype,T),E&&i(h,E),h}}(),w=function i(h,T,E){h===null&&(h=Function.prototype);var A=Object.getOwnPropertyDescriptor(h,T);if(A===void 0){var P=Object.getPrototypeOf(h);return P===null?void 0:i(P,T,E)}else{if("value"in A)return A.value;var O=A.get;return O===void 0?void 0:O.call(E)}},y=c(0),g=t(y),m=c(4),d=t(m),o=c(25),e=t(o);function t(i){return i&&i.__esModule?i:{default:i}}function a(i,h,T){return h in i?Object.defineProperty(i,h,{value:T,enumerable:!0,configurable:!0,writable:!0}):i[h]=T,i}function s(i,h){if(!(i instanceof h))throw new TypeError("Cannot call a class as a function")}function l(i,h){if(!i)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return h&&(typeof h=="object"||typeof h=="function")?h:i}function r(i,h){if(typeof h!="function"&&h!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof h);i.prototype=Object.create(h&&h.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),h&&(Object.setPrototypeOf?Object.setPrototypeOf(i,h):i.__proto__=h)}var n=function(i){r(h,i);function h(){return s(this,h),l(this,(h.__proto__||Object.getPrototypeOf(h)).apply(this,arguments))}return N(h,[{key:"format",value:function(E,A){E===f.blotName&&!A?this.replaceWith(g.default.create(this.statics.scope)):w(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"format",this).call(this,E,A)}},{key:"remove",value:function(){this.prev==null&&this.next==null?this.parent.remove():w(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"remove",this).call(this)}},{key:"replaceWith",value:function(E,A){return this.parent.isolate(this.offset(this.parent),this.length()),E===this.parent.statics.blotName?(this.parent.replaceWith(E,A),this):(this.parent.unwrap(),w(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"replaceWith",this).call(this,E,A))}}],[{key:"formats",value:function(E){return E.tagName===this.tagName?void 0:w(h.__proto__||Object.getPrototypeOf(h),"formats",this).call(this,E)}}]),h}(d.default);n.blotName="list-item",n.tagName="LI";var f=function(i){r(h,i),N(h,null,[{key:"create",value:function(E){var A=E==="ordered"?"OL":"UL",P=w(h.__proto__||Object.getPrototypeOf(h),"create",this).call(this,A);return(E==="checked"||E==="unchecked")&&P.setAttribute("data-checked",E==="checked"),P}},{key:"formats",value:function(E){if(E.tagName==="OL")return"ordered";if(E.tagName==="UL")return E.hasAttribute("data-checked")?E.getAttribute("data-checked")==="true"?"checked":"unchecked":"bullet"}}]);function h(T){s(this,h);var E=l(this,(h.__proto__||Object.getPrototypeOf(h)).call(this,T)),A=function(O){if(O.target.parentNode===T){var b=E.statics.formats(T),_=g.default.find(O.target);b==="checked"?_.format("list","unchecked"):b==="unchecked"&&_.format("list","checked")}};return T.addEventListener("touchstart",A),T.addEventListener("mousedown",A),E}return N(h,[{key:"format",value:function(E,A){this.children.length>0&&this.children.tail.format(E,A)}},{key:"formats",value:function(){return a({},this.statics.blotName,this.statics.formats(this.domNode))}},{key:"insertBefore",value:function(E,A){if(E instanceof n)w(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"insertBefore",this).call(this,E,A);else{var P=A==null?this.length():A.offset(this),O=this.split(P);O.parent.insertBefore(E,O)}}},{key:"optimize",value:function(E){w(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"optimize",this).call(this,E);var A=this.next;A!=null&&A.prev===this&&A.statics.blotName===this.statics.blotName&&A.domNode.tagName===this.domNode.tagName&&A.domNode.getAttribute("data-checked")===this.domNode.getAttribute("data-checked")&&(A.moveChildren(this),A.remove())}},{key:"replace",value:function(E){if(E.statics.blotName!==this.statics.blotName){var A=g.default.create(this.statics.defaultChild);E.moveChildren(A),this.appendChild(A)}w(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"replace",this).call(this,E)}}]),h}(e.default);f.blotName="list",f.scope=g.default.Scope.BLOCK_BLOT,f.tagName=["OL","UL"],f.defaultChild="list-item",f.allowedChildren=[n],u.ListItem=n,u.default=f},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=c(56),w=y(N);function y(e){return e&&e.__esModule?e:{default:e}}function g(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function m(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function d(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var o=function(e){d(t,e);function t(){return g(this,t),m(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return t}(w.default);o.blotName="italic",o.tagName=["EM","I"],u.default=o},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=function(){function a(s,l){for(var r=0;r<l.length;r++){var n=l[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(s,n.key,n)}}return function(s,l,r){return l&&a(s.prototype,l),r&&a(s,r),s}}(),w=function a(s,l,r){s===null&&(s=Function.prototype);var n=Object.getOwnPropertyDescriptor(s,l);if(n===void 0){var f=Object.getPrototypeOf(s);return f===null?void 0:a(f,l,r)}else{if("value"in n)return n.value;var i=n.get;return i===void 0?void 0:i.call(r)}},y=c(6),g=m(y);function m(a){return a&&a.__esModule?a:{default:a}}function d(a,s){if(!(a instanceof s))throw new TypeError("Cannot call a class as a function")}function o(a,s){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return s&&(typeof s=="object"||typeof s=="function")?s:a}function e(a,s){if(typeof s!="function"&&s!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof s);a.prototype=Object.create(s&&s.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),s&&(Object.setPrototypeOf?Object.setPrototypeOf(a,s):a.__proto__=s)}var t=function(a){e(s,a);function s(){return d(this,s),o(this,(s.__proto__||Object.getPrototypeOf(s)).apply(this,arguments))}return N(s,null,[{key:"create",value:function(r){return r==="super"?document.createElement("sup"):r==="sub"?document.createElement("sub"):w(s.__proto__||Object.getPrototypeOf(s),"create",this).call(this,r)}},{key:"formats",value:function(r){if(r.tagName==="SUB")return"sub";if(r.tagName==="SUP")return"super"}}]),s}(g.default);t.blotName="script",t.tagName=["SUB","SUP"],u.default=t},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=c(6),w=y(N);function y(e){return e&&e.__esModule?e:{default:e}}function g(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function m(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function d(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var o=function(e){d(t,e);function t(){return g(this,t),m(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return t}(w.default);o.blotName="strike",o.tagName="S",u.default=o},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=c(6),w=y(N);function y(e){return e&&e.__esModule?e:{default:e}}function g(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function m(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function d(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var o=function(e){d(t,e);function t(){return g(this,t),m(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return t}(w.default);o.blotName="underline",o.tagName="U",u.default=o},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=function(){function l(r,n){for(var f=0;f<n.length;f++){var i=n[f];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(r,i.key,i)}}return function(r,n,f){return n&&l(r.prototype,n),f&&l(r,f),r}}(),w=function l(r,n,f){r===null&&(r=Function.prototype);var i=Object.getOwnPropertyDescriptor(r,n);if(i===void 0){var h=Object.getPrototypeOf(r);return h===null?void 0:l(h,n,f)}else{if("value"in i)return i.value;var T=i.get;return T===void 0?void 0:T.call(f)}},y=c(0),g=d(y),m=c(27);function d(l){return l&&l.__esModule?l:{default:l}}function o(l,r){if(!(l instanceof r))throw new TypeError("Cannot call a class as a function")}function e(l,r){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r&&(typeof r=="object"||typeof r=="function")?r:l}function t(l,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof r);l.prototype=Object.create(r&&r.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(l,r):l.__proto__=r)}var a=["alt","height","width"],s=function(l){t(r,l);function r(){return o(this,r),e(this,(r.__proto__||Object.getPrototypeOf(r)).apply(this,arguments))}return N(r,[{key:"format",value:function(f,i){a.indexOf(f)>-1?i?this.domNode.setAttribute(f,i):this.domNode.removeAttribute(f):w(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"format",this).call(this,f,i)}}],[{key:"create",value:function(f){var i=w(r.__proto__||Object.getPrototypeOf(r),"create",this).call(this,f);return typeof f=="string"&&i.setAttribute("src",this.sanitize(f)),i}},{key:"formats",value:function(f){return a.reduce(function(i,h){return f.hasAttribute(h)&&(i[h]=f.getAttribute(h)),i},{})}},{key:"match",value:function(f){return/\.(jpe?g|gif|png)$/.test(f)||/^data:image\/.+;base64/.test(f)}},{key:"sanitize",value:function(f){return(0,m.sanitize)(f,["http","https","data"])?f:"//:0"}},{key:"value",value:function(f){return f.getAttribute("src")}}]),r}(g.default.Embed);s.blotName="image",s.tagName="IMG",u.default=s},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0});var N=function(){function l(r,n){for(var f=0;f<n.length;f++){var i=n[f];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(r,i.key,i)}}return function(r,n,f){return n&&l(r.prototype,n),f&&l(r,f),r}}(),w=function l(r,n,f){r===null&&(r=Function.prototype);var i=Object.getOwnPropertyDescriptor(r,n);if(i===void 0){var h=Object.getPrototypeOf(r);return h===null?void 0:l(h,n,f)}else{if("value"in i)return i.value;var T=i.get;return T===void 0?void 0:T.call(f)}},y=c(4),g=c(27),m=d(g);function d(l){return l&&l.__esModule?l:{default:l}}function o(l,r){if(!(l instanceof r))throw new TypeError("Cannot call a class as a function")}function e(l,r){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r&&(typeof r=="object"||typeof r=="function")?r:l}function t(l,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof r);l.prototype=Object.create(r&&r.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(l,r):l.__proto__=r)}var a=["height","width"],s=function(l){t(r,l);function r(){return o(this,r),e(this,(r.__proto__||Object.getPrototypeOf(r)).apply(this,arguments))}return N(r,[{key:"format",value:function(f,i){a.indexOf(f)>-1?i?this.domNode.setAttribute(f,i):this.domNode.removeAttribute(f):w(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"format",this).call(this,f,i)}}],[{key:"create",value:function(f){var i=w(r.__proto__||Object.getPrototypeOf(r),"create",this).call(this,f);return i.setAttribute("frameborder","0"),i.setAttribute("allowfullscreen",!0),i.setAttribute("src",this.sanitize(f)),i}},{key:"formats",value:function(f){return a.reduce(function(i,h){return f.hasAttribute(h)&&(i[h]=f.getAttribute(h)),i},{})}},{key:"sanitize",value:function(f){return m.default.sanitize(f)}},{key:"value",value:function(f){return f.getAttribute("src")}}]),r}(y.BlockEmbed);s.blotName="video",s.className="ql-video",s.tagName="IFRAME",u.default=s},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0}),u.default=u.FormulaBlot=void 0;var N=function(){function f(i,h){for(var T=0;T<h.length;T++){var E=h[T];E.enumerable=E.enumerable||!1,E.configurable=!0,"value"in E&&(E.writable=!0),Object.defineProperty(i,E.key,E)}}return function(i,h,T){return h&&f(i.prototype,h),T&&f(i,T),i}}(),w=function f(i,h,T){i===null&&(i=Function.prototype);var E=Object.getOwnPropertyDescriptor(i,h);if(E===void 0){var A=Object.getPrototypeOf(i);return A===null?void 0:f(A,h,T)}else{if("value"in E)return E.value;var P=E.get;return P===void 0?void 0:P.call(T)}},y=c(35),g=t(y),m=c(5),d=t(m),o=c(9),e=t(o);function t(f){return f&&f.__esModule?f:{default:f}}function a(f,i){if(!(f instanceof i))throw new TypeError("Cannot call a class as a function")}function s(f,i){if(!f)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return i&&(typeof i=="object"||typeof i=="function")?i:f}function l(f,i){if(typeof i!="function"&&i!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof i);f.prototype=Object.create(i&&i.prototype,{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}}),i&&(Object.setPrototypeOf?Object.setPrototypeOf(f,i):f.__proto__=i)}var r=function(f){l(i,f);function i(){return a(this,i),s(this,(i.__proto__||Object.getPrototypeOf(i)).apply(this,arguments))}return N(i,null,[{key:"create",value:function(T){var E=w(i.__proto__||Object.getPrototypeOf(i),"create",this).call(this,T);return typeof T=="string"&&(window.katex.render(T,E,{throwOnError:!1,errorColor:"#f00"}),E.setAttribute("data-value",T)),E}},{key:"value",value:function(T){return T.getAttribute("data-value")}}]),i}(g.default);r.blotName="formula",r.className="ql-formula",r.tagName="SPAN";var n=function(f){l(i,f),N(i,null,[{key:"register",value:function(){d.default.register(r,!0)}}]);function i(){a(this,i);var h=s(this,(i.__proto__||Object.getPrototypeOf(i)).call(this));if(window.katex==null)throw new Error("Formula module requires KaTeX.");return h}return i}(e.default);u.FormulaBlot=r,u.default=n},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0}),u.default=u.CodeToken=u.CodeBlock=void 0;var N=function(){function T(E,A){for(var P=0;P<A.length;P++){var O=A[P];O.enumerable=O.enumerable||!1,O.configurable=!0,"value"in O&&(O.writable=!0),Object.defineProperty(E,O.key,O)}}return function(E,A,P){return A&&T(E.prototype,A),P&&T(E,P),E}}(),w=function T(E,A,P){E===null&&(E=Function.prototype);var O=Object.getOwnPropertyDescriptor(E,A);if(O===void 0){var b=Object.getPrototypeOf(E);return b===null?void 0:T(b,A,P)}else{if("value"in O)return O.value;var _=O.get;return _===void 0?void 0:_.call(P)}},y=c(0),g=s(y),m=c(5),d=s(m),o=c(9),e=s(o),t=c(13),a=s(t);function s(T){return T&&T.__esModule?T:{default:T}}function l(T,E){if(!(T instanceof E))throw new TypeError("Cannot call a class as a function")}function r(T,E){if(!T)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return E&&(typeof E=="object"||typeof E=="function")?E:T}function n(T,E){if(typeof E!="function"&&E!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof E);T.prototype=Object.create(E&&E.prototype,{constructor:{value:T,enumerable:!1,writable:!0,configurable:!0}}),E&&(Object.setPrototypeOf?Object.setPrototypeOf(T,E):T.__proto__=E)}var f=function(T){n(E,T);function E(){return l(this,E),r(this,(E.__proto__||Object.getPrototypeOf(E)).apply(this,arguments))}return N(E,[{key:"replaceWith",value:function(P){this.domNode.textContent=this.domNode.textContent,this.attach(),w(E.prototype.__proto__||Object.getPrototypeOf(E.prototype),"replaceWith",this).call(this,P)}},{key:"highlight",value:function(P){var O=this.domNode.textContent;this.cachedText!==O&&((O.trim().length>0||this.cachedText==null)&&(this.domNode.innerHTML=P(O),this.domNode.normalize(),this.attach()),this.cachedText=O)}}]),E}(a.default);f.className="ql-syntax";var i=new g.default.Attributor.Class("token","hljs",{scope:g.default.Scope.INLINE}),h=function(T){n(E,T),N(E,null,[{key:"register",value:function(){d.default.register(i,!0),d.default.register(f,!0)}}]);function E(A,P){l(this,E);var O=r(this,(E.__proto__||Object.getPrototypeOf(E)).call(this,A,P));if(typeof O.options.highlight!="function")throw new Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");var b=null;return O.quill.on(d.default.events.SCROLL_OPTIMIZE,function(){clearTimeout(b),b=setTimeout(function(){O.highlight(),b=null},O.options.interval)}),O.highlight(),O}return N(E,[{key:"highlight",value:function(){var P=this;if(!this.quill.selection.composing){this.quill.update(d.default.sources.USER);var O=this.quill.getSelection();this.quill.scroll.descendants(f).forEach(function(b){b.highlight(P.options.highlight)}),this.quill.update(d.default.sources.SILENT),O!=null&&this.quill.setSelection(O,d.default.sources.SILENT)}}}]),E}(e.default);h.DEFAULTS={highlight:function(){return window.hljs==null?null:function(T){var E=window.hljs.highlightAuto(T);return E.value}}(),interval:1e3},u.CodeBlock=f,u.CodeToken=i,u.default=h},function(x,u){x.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=13 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=9 y1=4 y2=4></line> </svg>'},function(x,u){x.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=14 x2=4 y1=14 y2=14></line> <line class=ql-stroke x1=12 x2=6 y1=4 y2=4></line> </svg>'},function(x,u){x.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=5 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=9 y1=4 y2=4></line> </svg>'},function(x,u){x.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=3 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=3 y1=4 y2=4></line> </svg>'},function(x,u){x.exports='<svg viewbox="0 0 18 18"> <g class="ql-fill ql-color-label"> <polygon points="6 6.868 6 6 5 6 5 7 5.942 7 6 6.868"></polygon> <rect height=1 width=1 x=4 y=4></rect> <polygon points="6.817 5 6 5 6 6 6.38 6 6.817 5"></polygon> <rect height=1 width=1 x=2 y=6></rect> <rect height=1 width=1 x=3 y=5></rect> <rect height=1 width=1 x=4 y=7></rect> <polygon points="4 11.439 4 11 3 11 3 12 3.755 12 4 11.439"></polygon> <rect height=1 width=1 x=2 y=12></rect> <rect height=1 width=1 x=2 y=9></rect> <rect height=1 width=1 x=2 y=15></rect> <polygon points="4.63 10 4 10 4 11 4.192 11 4.63 10"></polygon> <rect height=1 width=1 x=3 y=8></rect> <path d=M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z></path> <path d=M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z></path> <path d=M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z></path> <rect height=1 width=1 x=12 y=2></rect> <rect height=1 width=1 x=11 y=3></rect> <path d=M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z></path> <rect height=1 width=1 x=2 y=3></rect> <rect height=1 width=1 x=6 y=2></rect> <rect height=1 width=1 x=3 y=2></rect> <rect height=1 width=1 x=5 y=3></rect> <rect height=1 width=1 x=9 y=2></rect> <rect height=1 width=1 x=15 y=14></rect> <polygon points="13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174"></polygon> <rect height=1 width=1 x=13 y=7></rect> <rect height=1 width=1 x=15 y=5></rect> <rect height=1 width=1 x=14 y=6></rect> <rect height=1 width=1 x=15 y=8></rect> <rect height=1 width=1 x=14 y=9></rect> <path d=M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z></path> <rect height=1 width=1 x=14 y=3></rect> <polygon points="12 6.868 12 6 11.62 6 12 6.868"></polygon> <rect height=1 width=1 x=15 y=2></rect> <rect height=1 width=1 x=12 y=5></rect> <rect height=1 width=1 x=13 y=4></rect> <polygon points="12.933 9 13 9 13 8 12.495 8 12.933 9"></polygon> <rect height=1 width=1 x=9 y=14></rect> <rect height=1 width=1 x=8 y=15></rect> <path d=M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z></path> <rect height=1 width=1 x=5 y=15></rect> <path d=M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z></path> <rect height=1 width=1 x=11 y=15></rect> <path d=M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z></path> <rect height=1 width=1 x=14 y=15></rect> <rect height=1 width=1 x=15 y=11></rect> </g> <polyline class=ql-stroke points="5.5 13 9 5 12.5 13"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=11 y2=11></line> </svg>'},function(x,u){x.exports='<svg viewbox="0 0 18 18"> <rect class="ql-fill ql-stroke" height=3 width=3 x=4 y=5></rect> <rect class="ql-fill ql-stroke" height=3 width=3 x=11 y=5></rect> <path class="ql-even ql-fill ql-stroke" d=M7,8c0,4.031-3,5-3,5></path> <path class="ql-even ql-fill ql-stroke" d=M14,8c0,4.031-3,5-3,5></path> </svg>'},function(x,u){x.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z></path> <path class=ql-stroke d=M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z></path> </svg>'},function(x,u){x.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=5 x2=13 y1=3 y2=3></line> <line class=ql-stroke x1=6 x2=9.35 y1=12 y2=3></line> <line class=ql-stroke x1=11 x2=15 y1=11 y2=15></line> <line class=ql-stroke x1=15 x2=11 y1=11 y2=15></line> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=7 x=2 y=14></rect> </svg>'},function(x,u){x.exports='<svg viewbox="0 0 18 18"> <line class="ql-color-label ql-stroke ql-transparent" x1=3 x2=15 y1=15 y2=15></line> <polyline class=ql-stroke points="5.5 11 9 3 12.5 11"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=9 y2=9></line> </svg>'},function(x,u){x.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="3 11 5 9 3 7 3 11"></polygon> <line class="ql-stroke ql-fill" x1=15 x2=11 y1=4 y2=4></line> <path class=ql-fill d=M11,3a3,3,0,0,0,0,6h1V3H11Z></path> <rect class=ql-fill height=11 width=1 x=11 y=4></rect> <rect class=ql-fill height=11 width=1 x=13 y=4></rect> </svg>'},function(x,u){x.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="15 12 13 10 15 8 15 12"></polygon> <line class="ql-stroke ql-fill" x1=9 x2=5 y1=4 y2=4></line> <path class=ql-fill d=M5,3A3,3,0,0,0,5,9H6V3H5Z></path> <rect class=ql-fill height=11 width=1 x=5 y=4></rect> <rect class=ql-fill height=11 width=1 x=7 y=4></rect> </svg>'},function(x,u){x.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M14,16H4a1,1,0,0,1,0-2H14A1,1,0,0,1,14,16Z /> <path class=ql-fill d=M14,4H4A1,1,0,0,1,4,2H14A1,1,0,0,1,14,4Z /> <rect class=ql-fill x=3 y=6 width=12 height=6 rx=1 ry=1 /> </svg>'},function(x,u){x.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M13,16H5a1,1,0,0,1,0-2h8A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H5A1,1,0,0,1,5,2h8A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=2 y=6 width=14 height=6 rx=1 ry=1 /> </svg>'},function(x,u){x.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15,8H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,8Z /> <path class=ql-fill d=M15,12H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,12Z /> <path class=ql-fill d=M15,16H5a1,1,0,0,1,0-2H15A1,1,0,0,1,15,16Z /> <path class=ql-fill d=M15,4H5A1,1,0,0,1,5,2H15A1,1,0,0,1,15,4Z /> <rect class=ql-fill x=2 y=6 width=8 height=6 rx=1 ry=1 /> </svg>'},function(x,u){x.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M5,8H3A1,1,0,0,1,3,6H5A1,1,0,0,1,5,8Z /> <path class=ql-fill d=M5,12H3a1,1,0,0,1,0-2H5A1,1,0,0,1,5,12Z /> <path class=ql-fill d=M13,16H3a1,1,0,0,1,0-2H13A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H3A1,1,0,0,1,3,2H13A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=8 y=6 width=8 height=6 rx=1 ry=1 transform="translate(24 18) rotate(-180)"/> </svg>'},function(x,u){x.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z></path> <rect class=ql-fill height=1.6 rx=0.8 ry=0.8 width=5 x=5.15 y=6.2></rect> <path class=ql-fill d=M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z></path> </svg>'},function(x,u){x.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z /> </svg>'},function(x,u){x.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z /> </svg>'},function(x,u){x.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=13 y1=4 y2=4></line> <line class=ql-stroke x1=5 x2=11 y1=14 y2=14></line> <line class=ql-stroke x1=8 x2=10 y1=14 y2=4></line> </svg>'},function(x,u){x.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=10 width=12 x=3 y=4></rect> <circle class=ql-fill cx=6 cy=7 r=1></circle> <polyline class="ql-even ql-fill" points="5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12"></polyline> </svg>'},function(x,u){x.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class="ql-fill ql-stroke" points="3 7 3 11 5 9 3 7"></polyline> </svg>'},function(x,u){x.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="5 7 5 11 3 9 5 7"></polyline> </svg>'},function(x,u){x.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=11 y1=7 y2=11></line> <path class="ql-even ql-stroke" d=M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z></path> <path class="ql-even ql-stroke" d=M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z></path> </svg>'},function(x,u){x.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=7 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=7 x2=15 y1=14 y2=14></line> <line class="ql-stroke ql-thin" x1=2.5 x2=4.5 y1=5.5 y2=5.5></line> <path class=ql-fill d=M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z></path> <path class="ql-stroke ql-thin" d=M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156></path> <path class="ql-stroke ql-thin" d=M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109></path> </svg>'},function(x,u){x.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=6 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=6 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=6 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=3 y1=4 y2=4></line> <line class=ql-stroke x1=3 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=3 y1=14 y2=14></line> </svg>'},function(x,u){x.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=9 x2=15 y1=4 y2=4></line> <polyline class=ql-stroke points="3 4 4 5 6 3"></polyline> <line class=ql-stroke x1=9 x2=15 y1=14 y2=14></line> <polyline class=ql-stroke points="3 14 4 15 6 13"></polyline> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="3 9 4 10 6 8"></polyline> </svg>'},function(x,u){x.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z /> <path class=ql-fill d=M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z /> </svg>'},function(x,u){x.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z /> <path class=ql-fill d=M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z /> </svg>'},function(x,u){x.exports='<svg viewbox="0 0 18 18"> <line class="ql-stroke ql-thin" x1=15.5 x2=2.5 y1=8.5 y2=9.5></line> <path class=ql-fill d=M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z></path> <path class=ql-fill d=M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z></path> </svg>'},function(x,u){x.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3></path> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=12 x=3 y=15></rect> </svg>'},function(x,u){x.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=12 width=12 x=3 y=3></rect> <rect class=ql-fill height=12 width=1 x=5 y=3></rect> <rect class=ql-fill height=12 width=1 x=12 y=3></rect> <rect class=ql-fill height=2 width=8 x=5 y=8></rect> <rect class=ql-fill height=1 width=3 x=3 y=5></rect> <rect class=ql-fill height=1 width=3 x=3 y=7></rect> <rect class=ql-fill height=1 width=3 x=3 y=10></rect> <rect class=ql-fill height=1 width=3 x=3 y=12></rect> <rect class=ql-fill height=1 width=3 x=12 y=5></rect> <rect class=ql-fill height=1 width=3 x=12 y=7></rect> <rect class=ql-fill height=1 width=3 x=12 y=10></rect> <rect class=ql-fill height=1 width=3 x=12 y=12></rect> </svg>'},function(x,u){x.exports='<svg viewbox="0 0 18 18"> <polygon class=ql-stroke points="7 11 9 13 11 11 7 11"></polygon> <polygon class=ql-stroke points="7 7 9 5 11 7 7 7"></polygon> </svg>'},function(x,u,c){Object.defineProperty(u,"__esModule",{value:!0}),u.default=u.BubbleTooltip=void 0;var N=function E(A,P,O){A===null&&(A=Function.prototype);var b=Object.getOwnPropertyDescriptor(A,P);if(b===void 0){var _=Object.getPrototypeOf(A);return _===null?void 0:E(_,P,O)}else{if("value"in b)return b.value;var S=b.get;return S===void 0?void 0:S.call(O)}},w=function(){function E(A,P){for(var O=0;O<P.length;O++){var b=P[O];b.enumerable=b.enumerable||!1,b.configurable=!0,"value"in b&&(b.writable=!0),Object.defineProperty(A,b.key,b)}}return function(A,P,O){return P&&E(A.prototype,P),O&&E(A,O),A}}(),y=c(3),g=l(y),m=c(8),d=l(m),o=c(43),e=l(o),t=c(15),a=c(41),s=l(a);function l(E){return E&&E.__esModule?E:{default:E}}function r(E,A){if(!(E instanceof A))throw new TypeError("Cannot call a class as a function")}function n(E,A){if(!E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return A&&(typeof A=="object"||typeof A=="function")?A:E}function f(E,A){if(typeof A!="function"&&A!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof A);E.prototype=Object.create(A&&A.prototype,{constructor:{value:E,enumerable:!1,writable:!0,configurable:!0}}),A&&(Object.setPrototypeOf?Object.setPrototypeOf(E,A):E.__proto__=A)}var i=[["bold","italic","link"],[{header:1},{header:2},"blockquote"]],h=function(E){f(A,E);function A(P,O){r(this,A),O.modules.toolbar!=null&&O.modules.toolbar.container==null&&(O.modules.toolbar.container=i);var b=n(this,(A.__proto__||Object.getPrototypeOf(A)).call(this,P,O));return b.quill.container.classList.add("ql-bubble"),b}return w(A,[{key:"extendToolbar",value:function(O){this.tooltip=new T(this.quill,this.options.bounds),this.tooltip.root.appendChild(O.container),this.buildButtons([].slice.call(O.container.querySelectorAll("button")),s.default),this.buildPickers([].slice.call(O.container.querySelectorAll("select")),s.default)}}]),A}(e.default);h.DEFAULTS=(0,g.default)(!0,{},e.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(A){A?this.quill.theme.tooltip.edit():this.quill.format("link",!1)}}}}});var T=function(E){f(A,E);function A(P,O){r(this,A);var b=n(this,(A.__proto__||Object.getPrototypeOf(A)).call(this,P,O));return b.quill.on(d.default.events.EDITOR_CHANGE,function(_,S,L,R){if(_===d.default.events.SELECTION_CHANGE)if(S!=null&&S.length>0&&R===d.default.sources.USER){b.show(),b.root.style.left="0px",b.root.style.width="",b.root.style.width=b.root.offsetWidth+"px";var $=b.quill.getLines(S.index,S.length);if($.length===1)b.position(b.quill.getBounds(S));else{var U=$[$.length-1],Y=b.quill.getIndex(U),M=Math.min(U.length()-1,S.index+S.length-Y),q=b.quill.getBounds(new t.Range(Y,M));b.position(q)}}else document.activeElement!==b.textbox&&b.quill.hasFocus()&&b.hide()}),b}return w(A,[{key:"listen",value:function(){var O=this;N(A.prototype.__proto__||Object.getPrototypeOf(A.prototype),"listen",this).call(this),this.root.querySelector(".ql-close").addEventListener("click",function(){O.root.classList.remove("ql-editing")}),this.quill.on(d.default.events.SCROLL_OPTIMIZE,function(){setTimeout(function(){if(!O.root.classList.contains("ql-hidden")){var b=O.quill.getSelection();b!=null&&O.position(O.quill.getBounds(b))}},1)})}},{key:"cancel",value:function(){this.show()}},{key:"position",value:function(O){var b=N(A.prototype.__proto__||Object.getPrototypeOf(A.prototype),"position",this).call(this,O),_=this.root.querySelector(".ql-tooltip-arrow");if(_.style.marginLeft="",b===0)return b;_.style.marginLeft=-1*b-_.offsetWidth/2+"px"}}]),A}(o.BaseTooltip);T.TEMPLATE=['<span class="ql-tooltip-arrow"></span>','<div class="ql-tooltip-editor">','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-close"></a>',"</div>"].join(""),u.BubbleTooltip=T,u.default=h},function(x,u,c){x.exports=c(63)}]).default})})(Cr);var ac=Cr.exports,oc=ge&&ge.__extends||function(){var p=function(k,x){return p=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(u,c){u.__proto__=c}||function(u,c){for(var N in c)c.hasOwnProperty(N)&&(u[N]=c[N])},p(k,x)};return function(k,x){p(k,x);function u(){this.constructor=k}k.prototype=x===null?Object.create(x):(u.prototype=x.prototype,new u)}}(),Xe=ge&&ge.__assign||function(){return Xe=Object.assign||function(p){for(var k,x=1,u=arguments.length;x<u;x++){k=arguments[x];for(var c in k)Object.prototype.hasOwnProperty.call(k,c)&&(p[c]=k[c])}return p},Xe.apply(this,arguments)},lc=ge&&ge.__spreadArrays||function(){for(var p=0,k=0,x=arguments.length;k<x;k++)p+=arguments[k].length;for(var u=Array(p),c=0,k=0;k<x;k++)for(var N=arguments[k],w=0,y=N.length;w<y;w++,c++)u[c]=N[w];return u},dt=ge&&ge.__importDefault||function(p){return p&&p.__esModule?p:{default:p}},be=dt(ne),sc=dt(Ur),Me=dt(ic),fr=dt(ac),uc=function(p){oc(k,p);function k(x){var u=p.call(this,x)||this;u.dirtyProps=["modules","formats","bounds","theme","children"],u.cleanProps=["id","className","style","placeholder","tabIndex","onChange","onChangeSelection","onFocus","onBlur","onKeyPress","onKeyDown","onKeyUp"],u.state={generation:0},u.selection=null,u.onEditorChange=function(N,w,y,g){var m,d,o,e;N==="text-change"?(d=(m=u).onEditorChangeText)===null||d===void 0||d.call(m,u.editor.root.innerHTML,w,g,u.unprivilegedEditor):N==="selection-change"&&((e=(o=u).onEditorChangeSelection)===null||e===void 0||e.call(o,w,g,u.unprivilegedEditor))};var c=u.isControlled()?x.value:x.defaultValue;return u.value=c??"",u}return k.prototype.validateProps=function(x){var u;if(be.default.Children.count(x.children)>1)throw new Error("The Quill editing area can only be composed of a single React element.");if(be.default.Children.count(x.children)){var c=be.default.Children.only(x.children);if(((u=c)===null||u===void 0?void 0:u.type)==="textarea")throw new Error("Quill does not support editing on a <textarea>. Use a <div> instead.")}if(this.lastDeltaChangeSet&&x.value===this.lastDeltaChangeSet)throw new Error("You are passing the `delta` object from the `onChange` event back as `value`. You most probably want `editor.getContents()` instead. See: https://github.com/zenoamaro/react-quill#using-deltas")},k.prototype.shouldComponentUpdate=function(x,u){var c=this,N;if(this.validateProps(x),!this.editor||this.state.generation!==u.generation)return!0;if("value"in x){var w=this.getEditorContents(),y=(N=x.value,N??"");this.isEqualValue(y,w)||this.setEditorContents(this.editor,y)}return x.readOnly!==this.props.readOnly&&this.setEditorReadOnly(this.editor,x.readOnly),lc(this.cleanProps,this.dirtyProps).some(function(g){return!Me.default(x[g],c.props[g])})},k.prototype.shouldComponentRegenerate=function(x){var u=this;return this.dirtyProps.some(function(c){return!Me.default(x[c],u.props[c])})},k.prototype.componentDidMount=function(){this.instantiateEditor(),this.setEditorContents(this.editor,this.getEditorContents())},k.prototype.componentWillUnmount=function(){this.destroyEditor()},k.prototype.componentDidUpdate=function(x,u){var c=this;if(this.editor&&this.shouldComponentRegenerate(x)){var N=this.editor.getContents(),w=this.editor.getSelection();this.regenerationSnapshot={delta:N,selection:w},this.setState({generation:this.state.generation+1}),this.destroyEditor()}if(this.state.generation!==u.generation){var y=this.regenerationSnapshot,N=y.delta,g=y.selection;delete this.regenerationSnapshot,this.instantiateEditor();var m=this.editor;m.setContents(N),dr(function(){return c.setEditorSelection(m,g)})}},k.prototype.instantiateEditor=function(){this.editor?this.hookEditor(this.editor):this.editor=this.createEditor(this.getEditingArea(),this.getEditorConfig())},k.prototype.destroyEditor=function(){this.editor&&this.unhookEditor(this.editor)},k.prototype.isControlled=function(){return"value"in this.props},k.prototype.getEditorConfig=function(){return{bounds:this.props.bounds,formats:this.props.formats,modules:this.props.modules,placeholder:this.props.placeholder,readOnly:this.props.readOnly,scrollingContainer:this.props.scrollingContainer,tabIndex:this.props.tabIndex,theme:this.props.theme}},k.prototype.getEditor=function(){if(!this.editor)throw new Error("Accessing non-instantiated editor");return this.editor},k.prototype.createEditor=function(x,u){var c=new fr.default(x,u);return u.tabIndex!=null&&this.setEditorTabIndex(c,u.tabIndex),this.hookEditor(c),c},k.prototype.hookEditor=function(x){this.unprivilegedEditor=this.makeUnprivilegedEditor(x),x.on("editor-change",this.onEditorChange)},k.prototype.unhookEditor=function(x){x.off("editor-change",this.onEditorChange)},k.prototype.getEditorContents=function(){return this.value},k.prototype.getEditorSelection=function(){return this.selection},k.prototype.isDelta=function(x){return x&&x.ops},k.prototype.isEqualValue=function(x,u){return this.isDelta(x)&&this.isDelta(u)?Me.default(x.ops,u.ops):Me.default(x,u)},k.prototype.setEditorContents=function(x,u){var c=this;this.value=u;var N=this.getEditorSelection();typeof u=="string"?x.setContents(x.clipboard.convert(u)):x.setContents(u),dr(function(){return c.setEditorSelection(x,N)})},k.prototype.setEditorSelection=function(x,u){if(this.selection=u,u){var c=x.getLength();u.index=Math.max(0,Math.min(u.index,c-1)),u.length=Math.max(0,Math.min(u.length,c-1-u.index)),x.setSelection(u)}},k.prototype.setEditorTabIndex=function(x,u){var c,N;!((N=(c=x)===null||c===void 0?void 0:c.scroll)===null||N===void 0)&&N.domNode&&(x.scroll.domNode.tabIndex=u)},k.prototype.setEditorReadOnly=function(x,u){u?x.disable():x.enable()},k.prototype.makeUnprivilegedEditor=function(x){var u=x;return{getHTML:function(){return u.root.innerHTML},getLength:u.getLength.bind(u),getText:u.getText.bind(u),getContents:u.getContents.bind(u),getSelection:u.getSelection.bind(u),getBounds:u.getBounds.bind(u)}},k.prototype.getEditingArea=function(){if(!this.editingArea)throw new Error("Instantiating on missing editing area");var x=sc.default.findDOMNode(this.editingArea);if(!x)throw new Error("Cannot find element for editing area");if(x.nodeType===3)throw new Error("Editing area cannot be a text node");return x},k.prototype.renderEditingArea=function(){var x=this,u=this.props,c=u.children,N=u.preserveWhitespace,w=this.state.generation,y={key:w,ref:function(g){x.editingArea=g}};return be.default.Children.count(c)?be.default.cloneElement(be.default.Children.only(c),y):N?be.default.createElement("pre",Xe({},y)):be.default.createElement("div",Xe({},y))},k.prototype.render=function(){var x;return be.default.createElement("div",{id:this.props.id,style:this.props.style,key:this.state.generation,className:"quill "+(x=this.props.className,x??""),onKeyPress:this.props.onKeyPress,onKeyDown:this.props.onKeyDown,onKeyUp:this.props.onKeyUp},this.renderEditingArea())},k.prototype.onEditorChangeText=function(x,u,c,N){var w,y;if(this.editor){var g=this.isDelta(this.value)?N.getContents():N.getHTML();g!==this.getEditorContents()&&(this.lastDeltaChangeSet=u,this.value=g,(y=(w=this.props).onChange)===null||y===void 0||y.call(w,x,u,c,N))}},k.prototype.onEditorChangeSelection=function(x,u,c){var N,w,y,g,m,d;if(this.editor){var o=this.getEditorSelection(),e=!o&&x,t=o&&!x;Me.default(x,o)||(this.selection=x,(w=(N=this.props).onChangeSelection)===null||w===void 0||w.call(N,x,u,c),e?(g=(y=this.props).onFocus)===null||g===void 0||g.call(y,x,u,c):t&&((d=(m=this.props).onBlur)===null||d===void 0||d.call(m,o,u,c)))}},k.prototype.focus=function(){this.editor&&this.editor.focus()},k.prototype.blur=function(){this.editor&&(this.selection=null,this.editor.blur())},k.displayName="React Quill",k.Quill=fr.default,k.defaultProps={theme:"snow",modules:{},readOnly:!1},k}(be.default.Component);function dr(p){Promise.resolve().then(p)}var cc=uc;const fc=Hr(cc);function dc(p,k=[]){let x=[];function u(N,w){const y=ne.createContext(w),g=x.length;x=[...x,w];function m(o){const{scope:e,children:t,...a}=o,s=(e==null?void 0:e[p][g])||y,l=ne.useMemo(()=>a,Object.values(a));return v.jsx(s.Provider,{value:l,children:t})}function d(o,e){const t=(e==null?void 0:e[p][g])||y,a=ne.useContext(t);if(a)return a;if(w!==void 0)return w;throw new Error(`\`${o}\` must be used within \`${N}\``)}return m.displayName=N+"Provider",[m,d]}const c=()=>{const N=x.map(w=>ne.createContext(w));return function(y){const g=(y==null?void 0:y[p])||N;return ne.useMemo(()=>({[`__scope${p}`]:{...y,[p]:g}}),[y,g])}};return c.scopeName=p,[u,hc(c,...k)]}function hc(...p){const k=p[0];if(p.length===1)return k;const x=()=>{const u=p.map(c=>({useScope:c(),scopeName:c.scopeName}));return function(N){const w=u.reduce((y,{useScope:g,scopeName:m})=>{const o=g(N)[`__scope${m}`];return{...y,...o}},{});return ne.useMemo(()=>({[`__scope${k.scopeName}`]:w}),[w])}};return x.scopeName=k.scopeName,x}var Rt="Progress",Dt=100,[pc,nf]=dc(Rt),[vc,gc]=pc(Rt),Rr=ne.forwardRef((p,k)=>{const{__scopeProgress:x,value:u=null,max:c,getValueLabel:N=mc,...w}=p;(c||c===0)&&!hr(c)&&console.error(yc(`${c}`,"Progress"));const y=hr(c)?c:Dt;u!==null&&!pr(u,y)&&console.error(bc(`${u}`,"Progress"));const g=pr(u,y)?u:null,m=Qe(g)?N(g,y):void 0;return v.jsx(vc,{scope:x,value:g,max:y,children:v.jsx(vr.div,{"aria-valuemax":y,"aria-valuemin":0,"aria-valuenow":Qe(g)?g:void 0,"aria-valuetext":m,role:"progressbar","data-state":Br(g,y),"data-value":g??void 0,"data-max":y,...w,ref:k})})});Rr.displayName=Rt;var Dr="ProgressIndicator",qr=ne.forwardRef((p,k)=>{const{__scopeProgress:x,...u}=p,c=gc(Dr,x);return v.jsx(vr.div,{"data-state":Br(c.value,c.max),"data-value":c.value??void 0,"data-max":c.max,...u,ref:k})});qr.displayName=Dr;function mc(p,k){return`${Math.round(p/k*100)}%`}function Br(p,k){return p==null?"indeterminate":p===k?"complete":"loading"}function Qe(p){return typeof p=="number"}function hr(p){return Qe(p)&&!isNaN(p)&&p>0}function pr(p,k){return Qe(p)&&!isNaN(p)&&p<=k&&p>=0}function yc(p,k){return`Invalid prop \`max\` of value \`${p}\` supplied to \`${k}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${Dt}\`.`}function bc(p,k){return`Invalid prop \`value\` of value \`${p}\` supplied to \`${k}\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or ${Dt} if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`}var Mr=Rr,xc=qr;const $r=ne.forwardRef(({className:p,value:k,...x},u)=>v.jsx(Mr,{ref:u,className:zr("relative h-4 w-full overflow-hidden rounded-full bg-secondary",p),...x,children:v.jsx(xc,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(k||0)}%)`}})}));$r.displayName=Mr.displayName;const _c=ye.object({title:ye.string().min(1,{message:"Title is required"}).max(200,{message:"Title must be less than 200 characters"}),content:ye.string().min(1,{message:"Content is required"}).min(10,{message:"Content must be at least 10 characters"}),tags:ye.array(ye.string().min(1)).min(0).max(10,{message:"Maximum 10 tags allowed"}),published:ye.boolean(),excerpt:ye.string().max(300,{message:"Excerpt must be less than 300 characters"}).optional().or(ye.literal("")),coverImageUrl:ye.string().url({message:"Must be a valid URL"}).optional().or(ye.literal(""))}),wc={toolbar:[[{header:[1,2,3,!1]}],["bold","italic","underline","strike"],[{list:"ordered"},{list:"bullet"}],["blockquote","code-block"],["link"],["clean"]]},Oc=["header","bold","italic","underline","strike","list","bullet","blockquote","code-block","link"],Nc=({isOpen:p,onClose:k,onSave:x,blog:u,isLoading:c=!1,mode:N})=>{const[w,y]=ne.useState(null),[g,m]=ne.useState(""),[d,o]=ne.useState(0),[e,t]=ne.useState(!1),[a,s]=ne.useState(!1),[l,r]=ne.useState(null),[n,f]=ne.useState(!1),[i,h]=ne.useState(null),[T,E]=ne.useState("richtext"),[A,P]=ne.useState(!1),[O,b]=ne.useState(null),_=ne.useRef(null),S=ne.useRef(null),L=ne.useRef(null),R=Kr({resolver:Vr(_c),defaultValues:{title:"",content:"",tags:[],published:!1,excerpt:"",coverImageUrl:""},mode:"onChange"}),$=ne.useCallback(()=>u!=null&&u.id?`blog-draft-${u.id}`:"blog-draft-new",[u==null?void 0:u.id]),U=ne.useCallback(V=>{try{const Q={...V,editorMode:T,timestamp:new Date().toISOString()};localStorage.setItem($(),JSON.stringify(Q)),b(new Date),P(!0)}catch(Q){console.error("Failed to save to localStorage:",Q),Q instanceof Error&&Q.name==="QuotaExceededError"&&re.error("Local storage is full. Please clear some space.")}},[T,$]),Y=ne.useCallback(()=>{try{const V=localStorage.getItem($());if(V)return JSON.parse(V)}catch(V){console.error("Failed to load from localStorage:",V)}return null},[$]),M=ne.useCallback(()=>{try{localStorage.removeItem($()),P(!1),b(null),re.success("Local draft cleared")}catch(V){console.error("Failed to clear localStorage:",V)}},[$]),q=ne.useCallback(()=>{const V=Y();V&&(P(!0),re.info("Local draft found",{description:"Would you like to restore your unsaved changes?",action:{label:"Restore",onClick:()=>{R.reset({title:V.title||"",content:V.content||"",tags:V.tags||[],published:V.published||!1,excerpt:V.excerpt||"",coverImageUrl:V.coverImageUrl||""}),E(V.editorMode||"richtext"),re.success("Draft restored")}}}))},[Y,R]),j=ne.useCallback(async V=>{var Q,ie,se,he,ue,G,W;if(!(N!=="edit"||!(u!=null&&u.id)||V.published))try{if(f(!0),h(null),!((Q=V.title)!=null&&Q.trim())&&!((ie=V.content)!=null&&ie.trim())){console.log("Skipping autosave - no meaningful content");return}const J={title:((se=V.title)==null?void 0:se.trim())||"",content:((he=V.content)==null?void 0:he.trim())||"",tags:((ue=V.tags)==null?void 0:ue.filter(te=>te.trim().length>0))||[],excerpt:((G=V.excerpt)==null?void 0:G.trim())||void 0,coverImageUrl:((W=V.coverImageUrl)==null?void 0:W.trim())||void 0,published:!1};await Tt(u.id,J),r(new Date),console.log("Autosave successful at",new Date().toLocaleTimeString())}catch(J){console.error("Autosave failed:",J),J instanceof Error?J.message.includes("permission")?h("Permission denied - please check your admin access"):J.message.includes("network")?h("Network error - autosave will retry"):J.message.includes("not found")?h("Blog not found - please refresh and try again"):h(`Autosave failed: ${J.message}`):h("Autosave failed - unknown error")}finally{f(!1)}},[N,u==null?void 0:u.id]);ne.useEffect(()=>{if(!p)return;const V=R.watch(Q=>{_.current&&clearTimeout(_.current),L.current&&clearTimeout(L.current),(Q.title||Q.content)&&(L.current=setTimeout(()=>{U(Q)},4e3),Q.published||(_.current=setTimeout(()=>{j(Q)},1e4)))});return()=>{V.unsubscribe(),_.current&&clearTimeout(_.current),L.current&&clearTimeout(L.current)}},[p,R,j,U]),ne.useEffect(()=>{p&&(N==="edit"&&u?(R.reset({title:u.title,content:u.content,tags:u.tags,published:u.published,excerpt:u.excerpt||"",coverImageUrl:u.coverImageUrl||""}),u.coverImageUrl&&m(u.coverImageUrl),q()):N==="create"&&(R.reset({title:"",content:"",tags:[],published:!1,excerpt:"",coverImageUrl:""}),m(""),y(null),q()),r(null),f(!1),h(null),s(!1),E("richtext"))},[p,u,N,R,q]);const D=ne.useCallback(async V=>{try{t(!0),o(0),console.log("Starting image upload:",{fileName:V.name,fileSize:V.size,fileType:V.type});const Q=await ln(V,u==null?void 0:u.id,ie=>{o(ie)});R.setValue("coverImageUrl",Q),m(Q),y(V),re.success("Cover image uploaded successfully"),console.log("Image upload successful:",Q)}catch(Q){console.error("Error uploading cover image:",Q),Q instanceof Error?Q.message.includes("permission")?re.error("Permission denied: Unable to upload image. Please check your admin access."):Q.message.includes("network")?re.error("Network error: Please check your internet connection and try again."):Q.message.includes("storage")?re.error("Storage error: Unable to save image. Please try again."):Q.message.includes("size")?re.error("File too large: Please select an image smaller than 5MB."):re.error(`Upload failed: ${Q.message}`):re.error("Failed to upload cover image. Please try again.")}finally{t(!1),o(0)}},[R,u==null?void 0:u.id]),B=V=>{var ie;const Q=(ie=V.target.files)==null?void 0:ie[0];if(Q){if(!Q.type.startsWith("image/")){re.error("Please select a valid image file");return}if(Q.size>5*1024*1024){re.error("Image size must be less than 5MB");return}D(Q)}},H=async()=>{const V=R.getValues("coverImageUrl");if(V&&N==="edit")try{await sn(V)}catch(Q){console.warn("Failed to delete previous cover image:",Q)}R.setValue("coverImageUrl",""),m(""),y(null)},F=async V=>{var Q,ie,se,he;try{if(!((Q=V.title)!=null&&Q.trim())){re.error("Title is required");return}if(!((ie=V.content)!=null&&ie.trim())){re.error("Content is required");return}if(V.content.trim().length<10){re.error("Content must be at least 10 characters long");return}const ue={title:V.title.trim(),content:V.content.trim(),tags:V.tags.filter(G=>G.trim().length>0).map(G=>G.trim()),published:!!V.published,excerpt:((se=V.excerpt)==null?void 0:se.trim())||void 0,coverImageUrl:((he=V.coverImageUrl)==null?void 0:he.trim())||void 0};console.log("Submitting blog data:",{title:ue.title,contentLength:ue.content.length,tagsCount:ue.tags.length,published:ue.published,hasExcerpt:!!ue.excerpt,hasCoverImage:!!ue.coverImageUrl}),await x(ue);try{localStorage.removeItem($()),P(!1),b(null)}catch(G){console.warn("Failed to clear localStorage after save:",G)}V.published&&k()}catch(ue){throw console.error("Error saving blog:",ue),ue}},I=V=>{const Q=V.split(",").map(ie=>ie.trim()).filter(ie=>ie.length>0);R.setValue("tags",Q)},C=()=>{_.current&&clearTimeout(_.current),S.current&&clearTimeout(S.current),L.current&&clearTimeout(L.current),R.reset(),k()},z=V=>V.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),K=ne.useCallback(V=>{try{const Q=Xr(V);return Qr.sanitize(Q)}catch(Q){return console.error("Error rendering markdown:",Q),'<p class="text-red-500">Error rendering markdown content</p>'}},[]),Z=V=>{if(R.getValues("content")&&V!==T){const ie={richtext:"Rich Text",markdown:"Markdown"};re.warning(`Switching to ${ie[V]} mode`,{description:"Some formatting may be lost during conversion."})}E(V)},ee=({formData:V})=>v.jsx("div",{className:"bg-white border rounded-lg p-6 h-full overflow-y-auto",children:v.jsxs("div",{className:"prose prose-sm max-w-none",children:[V.coverImageUrl&&v.jsx("div",{className:"mb-6",children:v.jsx("img",{src:V.coverImageUrl,alt:"Cover",className:"w-full h-48 object-cover rounded-lg"})}),v.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:V.title||"Untitled Blog Post"}),V.excerpt&&v.jsx("p",{className:"text-gray-600 italic mb-4 border-l-4 border-burgundy-200 pl-4",children:V.excerpt}),V.tags.length>0&&v.jsx("div",{className:"flex flex-wrap gap-2 mb-6",children:V.tags.map((Q,ie)=>v.jsx(ve,{variant:"secondary",className:"bg-beige-100 text-navy-700",children:Q},ie))}),v.jsx("div",{className:"prose prose-sm max-w-none prose-headings:text-navy-800 prose-a:text-burgundy-600 prose-strong:text-navy-700",dangerouslySetInnerHTML:{__html:V.content?T==="markdown"?K(V.content):V.content:'<p class="text-gray-400">Start writing your blog post content...</p>'}}),v.jsx("div",{className:"mt-8 pt-4 border-t border-gray-200",children:v.jsx(ve,{variant:V.published?"default":"secondary",children:V.published?"Published":"Draft"})})]})});return v.jsx(en,{open:p,onOpenChange:C,children:v.jsxs(tn,{className:`flex flex-col max-h-[90vh] overflow-hidden ${a?"max-w-7xl":"max-w-4xl"}`,children:[v.jsx(rn,{className:"flex-shrink-0",children:v.jsxs("div",{className:"flex items-center justify-between",children:[v.jsxs("div",{children:[v.jsx(nn,{className:"text-navy-800",children:N==="create"?"Create New Blog Post":"Edit Blog Post"}),v.jsx(an,{children:N==="create"?"Create a new blog post for your readers. You can save as draft or publish immediately.":"Edit your blog post. Changes will be saved when you click Save."})]}),v.jsxs("div",{className:"flex items-center gap-2",children:[A&&v.jsx(ve,{variant:"secondary",className:"text-xs",children:"Local draft available"}),N==="edit"&&!R.watch("published")&&v.jsx("div",{className:"flex items-center gap-2 text-sm",children:n?v.jsxs("div",{className:"flex items-center gap-1 text-blue-600",children:[v.jsx(Ge,{size:"sm"}),v.jsx("span",{children:"Saving..."})]}):l?v.jsxs("div",{className:"flex items-center gap-1 text-green-600",children:[v.jsx(br,{className:"h-3 w-3"}),v.jsxs("span",{children:["Saved at ",z(l)]})]}):i?v.jsxs("div",{className:"flex items-center gap-1 text-red-600",children:[v.jsx(qt,{className:"h-3 w-3"}),v.jsx("span",{children:i})]}):null}),A&&v.jsxs(ce,{type:"button",variant:"outline",size:"sm",onClick:M,className:"flex items-center gap-1 text-red-600 hover:text-red-700",children:[v.jsx(Pt,{className:"h-3 w-3"}),"Clear Draft"]}),v.jsxs(ce,{type:"button",variant:"outline",size:"sm",onClick:()=>s(!a),className:"flex items-center gap-1",children:[a?v.jsx(gn,{className:"h-4 w-4"}):v.jsx(_t,{className:"h-4 w-4"}),a?"Hide Preview":"Show Preview"]})]})]})}),v.jsxs("div",{className:"flex-1 flex gap-6 overflow-hidden min-h-0",children:[v.jsx("div",{className:`${a?"w-1/2":"w-full"} flex flex-col overflow-hidden`,children:v.jsx(Gr,{...R,children:v.jsxs("form",{onSubmit:R.handleSubmit(F),className:"flex flex-col h-full",children:[v.jsxs("div",{className:"flex-1 overflow-y-auto space-y-6 pr-2 blog-editor-scroll",children:[v.jsx(Ae,{control:R.control,name:"title",render:({field:V})=>v.jsxs(Oe,{children:[v.jsx(Ne,{children:"Title *"}),v.jsx(je,{children:v.jsx(mt,{placeholder:"Enter blog post title...",...V,disabled:c,className:"text-lg"})}),v.jsx(qe,{})]})}),v.jsx(Ae,{control:R.control,name:"coverImageUrl",render:({field:V})=>v.jsxs(Oe,{children:[v.jsx(Ne,{children:"Cover Image"}),v.jsx(je,{children:v.jsxs("div",{className:"space-y-4",children:[g?v.jsxs("div",{className:"relative",children:[v.jsx("img",{src:g,alt:"Cover preview",className:"w-full h-48 object-cover rounded-lg border"}),v.jsx(ce,{type:"button",variant:"destructive",size:"sm",className:"absolute top-2 right-2",onClick:H,disabled:c||e,children:v.jsx(qt,{className:"h-4 w-4"})})]}):v.jsxs("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center",children:[v.jsx(mn,{className:"mx-auto h-12 w-12 text-gray-400"}),v.jsxs("div",{className:"mt-4",children:[v.jsxs("label",{htmlFor:"cover-image-upload",className:"cursor-pointer",children:[v.jsx("span",{className:"mt-2 block text-sm font-medium text-gray-900",children:"Upload cover image"}),v.jsx("span",{className:"mt-1 block text-sm text-gray-500",children:"PNG, JPG, WebP up to 5MB"})]}),v.jsx("input",{id:"cover-image-upload",type:"file",className:"hidden",accept:"image/*",onChange:B,disabled:c||e})]})]}),e&&v.jsxs("div",{className:"space-y-2",children:[v.jsxs("div",{className:"flex items-center justify-between text-sm",children:[v.jsx("span",{children:"Uploading image..."}),v.jsxs("span",{children:[d,"%"]})]}),v.jsx($r,{value:d,className:"w-full"})]})]})}),v.jsx(Te,{children:"Upload a cover image for your blog post. This will be displayed as the main image."}),v.jsx(qe,{})]})}),v.jsx(Ae,{control:R.control,name:"excerpt",render:({field:V})=>v.jsxs(Oe,{children:[v.jsx(Ne,{children:"Excerpt"}),v.jsx(je,{children:v.jsx(bt,{placeholder:"Brief description of the blog post (optional)...",...V,disabled:c,rows:2})}),v.jsx(Te,{children:"A short summary that will be displayed in blog previews. If left empty, it will be auto-generated from content."}),v.jsx(qe,{})]})}),v.jsxs(Oe,{children:[v.jsx(Ne,{children:"Editor Mode"}),v.jsxs("div",{className:"flex items-center space-x-4 p-3 border rounded-lg bg-gray-50",children:[v.jsxs("div",{className:"flex items-center space-x-2",children:[v.jsx("input",{type:"radio",id:"richtext-mode",name:"editor-mode",checked:T==="richtext",onChange:()=>Z("richtext"),className:"w-4 h-4 text-burgundy-600 border-gray-300 focus:ring-burgundy-500"}),v.jsxs("label",{htmlFor:"richtext-mode",className:"flex items-center gap-2 text-sm font-medium text-gray-700 cursor-pointer",children:[v.jsx(yt,{className:"h-4 w-4"}),"Rich Text"]})]}),v.jsxs("div",{className:"flex items-center space-x-2",children:[v.jsx("input",{type:"radio",id:"markdown-mode",name:"editor-mode",checked:T==="markdown",onChange:()=>Z("markdown"),className:"w-4 h-4 text-burgundy-600 border-gray-300 focus:ring-burgundy-500"}),v.jsxs("label",{htmlFor:"markdown-mode",className:"flex items-center gap-2 text-sm font-medium text-gray-700 cursor-pointer",children:[v.jsx(Tn,{className:"h-4 w-4"}),"Markdown"]})]})]}),v.jsx(Te,{children:"Choose between rich text editing with formatting tools or Markdown for more control over content structure."})]}),v.jsx(Ae,{control:R.control,name:"content",render:({field:V})=>v.jsxs(Oe,{children:[v.jsx(Ne,{children:"Content *"}),v.jsx(je,{children:T==="richtext"?v.jsx("div",{className:"min-h-[300px]",children:v.jsx(fc,{theme:"snow",value:V.value,onChange:V.onChange,modules:wc,formats:Oc,placeholder:"Write your blog post content here...",className:"bg-white",style:{height:"250px",marginBottom:"50px"},readOnly:c})}):v.jsx(bt,{...V,placeholder:`Write your blog post content in Markdown...

# Heading 1
## Heading 2

**Bold text** and *italic text*

- List item 1
- List item 2

[Link text](https://example.com)`,className:"min-h-[400px] font-mono text-sm resize-none bg-gray-50 border-2 focus:border-burgundy-300 focus:ring-burgundy-200",disabled:c})}),v.jsx(Te,{children:T==="richtext"?"Write your blog post content using the rich text editor. You can format text, add headers, lists, and links.":"Write your blog post content in Markdown. Use the preview to see how it will appear to readers."}),v.jsx(qe,{})]})}),v.jsx(Ae,{control:R.control,name:"tags",render:({field:V})=>v.jsxs(Oe,{children:[v.jsx(Ne,{children:"Tags"}),v.jsx(je,{children:v.jsx(mt,{placeholder:"Enter tags separated by commas (e.g., books, reading, reviews)",value:V.value.join(", "),onChange:Q=>I(Q.target.value),disabled:c})}),v.jsx(Te,{children:"Add tags to help categorize your blog post. Separate multiple tags with commas."}),v.jsx(qe,{})]})}),v.jsx(Ae,{control:R.control,name:"published",render:({field:V})=>v.jsxs(Oe,{className:"flex flex-row items-center justify-between rounded-lg border p-4",children:[v.jsxs("div",{className:"space-y-0.5",children:[v.jsx(Ne,{className:"text-base",children:"Publish immediately"}),v.jsx(Te,{children:"Toggle this to publish the blog post immediately. If disabled, it will be saved as a draft."})]}),v.jsx(je,{children:v.jsx(Jr,{checked:V.value,onCheckedChange:V.onChange,disabled:c})})]})})]}),v.jsxs(on,{className:"flex-shrink-0 gap-2 pt-4 border-t bg-white",children:[v.jsx(ce,{type:"button",variant:"outline",onClick:C,disabled:c,children:"Cancel"}),v.jsx(ce,{type:"submit",disabled:c,className:"bg-burgundy-600 hover:bg-burgundy-700",children:c?v.jsxs(v.Fragment,{children:[v.jsx(Ge,{size:"sm",className:"mr-2"}),N==="create"?"Creating...":"Saving..."]}):v.jsx(v.Fragment,{children:R.watch("published")?N==="create"?"Create & Publish":"Save & Publish":N==="create"?"Save as Draft":"Save Changes"})})]})]})})}),a&&v.jsxs("div",{className:"w-1/2 border-l border-gray-200 pl-6 flex flex-col overflow-hidden",children:[v.jsxs("div",{className:"flex-shrink-0 pb-4 mb-4 border-b border-gray-200",children:[v.jsxs("h3",{className:"text-lg font-semibold text-navy-800 flex items-center gap-2",children:[v.jsx(_t,{className:"h-5 w-5"}),"Live Preview"]}),v.jsx("p",{className:"text-sm text-gray-600",children:"See how your blog post will appear to readers"})]}),v.jsx("div",{className:"flex-1 overflow-y-auto pr-2 blog-editor-scroll",children:v.jsx(ee,{formData:R.watch()})})]})]})]})})},Ec=()=>{const{currentUser:p,isAdmin:k}=ke(),[x,u]=ne.useState(!1),[c,N]=ne.useState([]),w=(d,o,e,t)=>{N(a=>{const s=a.find(l=>l.name===d);return s?(s.status=o,s.message=e,s.data=t,[...a]):[...a,{name:d,status:o,message:e,data:t}]})},y=async()=>{var o,e;if(!p||!k){re.error("You must be logged in as an admin to run these tests");return}u(!0),N([]),["Firebase Connection","Create Draft Blog","Autosave Draft","Publish Blog Post","Fetch Blogs","Fetch Published Posts"].forEach(t=>w(t,"pending"));try{w("Firebase Connection","running");const t=await St();if(t.success)w("Firebase Connection","success","All Firebase services accessible");else{w("Firebase Connection","error",t.message);return}w("Create Draft Blog","running");const a={title:`Test Draft Blog ${new Date().toISOString()}`,content:"This is a test draft blog post created by the workflow tester. It contains enough content to pass validation requirements.",tags:["test","workflow","draft"],published:!1,authorId:p.uid,authorName:p.displayName||((o=p.email)==null?void 0:o.split("@")[0])||"Test User",authorEmail:p.email||"<EMAIL>",excerpt:"Test draft blog excerpt for workflow validation"},s=await kt(a);w("Create Draft Blog","success",`Draft created with ID: ${s}`,{id:s}),w("Autosave Draft","running");const l={title:`${a.title} - Updated`,content:`${a.content} - This content has been updated via autosave.`,tags:[...a.tags,"autosaved"],excerpt:"Updated excerpt via autosave"};await Tt(s,l),w("Autosave Draft","success","Draft autosaved successfully"),w("Publish Blog Post","running");const r={title:`Test Published Blog ${new Date().toISOString()}`,content:"This is a test published blog post created by the workflow tester. It demonstrates the complete publishing workflow from the admin panel.",excerpt:"Test published blog excerpt for workflow validation",author:p.displayName||((e=p.email)==null?void 0:e.split("@")[0])||"Test User",tags:["test","workflow","published"],coverImageUrl:null},n=await xt(r);w("Publish Blog Post","success",`Blog published with ID: ${n}`,{id:n}),w("Fetch Blogs","running");const f=await gr();w("Fetch Blogs","success",`Retrieved ${f.length} blogs from drafts collection`),w("Fetch Published Posts","running");const i=await un();w("Fetch Published Posts","success",`Retrieved ${i.length} published posts`),re.success("All workflow tests completed successfully!")}catch(t){console.error("Workflow test failed:",t);const a=t instanceof Error?t.message:"Unknown error",s=c.find(l=>l.status==="running");s&&w(s.name,"error",a),re.error(`Workflow test failed: ${a}`)}finally{u(!1)}},g=d=>{switch(d){case"pending":return v.jsx(He,{className:"h-4 w-4 text-gray-400"});case"running":return v.jsx(He,{className:"h-4 w-4 text-blue-500 animate-spin"});case"success":return v.jsx(Ue,{className:"h-4 w-4 text-green-500"});case"error":return v.jsx(et,{className:"h-4 w-4 text-red-500"})}},m=d=>{switch(d){case"pending":return"bg-gray-100 text-gray-600";case"running":return"bg-blue-100 text-blue-600";case"success":return"bg-green-100 text-green-600";case"error":return"bg-red-100 text-red-600"}};return v.jsxs(tt,{className:"w-full max-w-4xl mx-auto",children:[v.jsxs(rt,{children:[v.jsxs(nt,{className:"flex items-center gap-2",children:[v.jsx(Fe,{className:"h-5 w-5 text-orange-500"}),"Blog Workflow Tester"]}),v.jsx(it,{children:"Test the complete blog management workflow including draft creation, autosave, and publishing. This will create test data in your Firebase collections."})]}),v.jsxs(at,{className:"space-y-4",children:[v.jsxs("div",{className:"flex items-center gap-4 p-3 bg-gray-50 rounded-lg",children:[v.jsxs("div",{children:[v.jsx("span",{className:"text-sm font-medium",children:"User:"}),v.jsx("span",{className:"ml-2 text-sm",children:(p==null?void 0:p.email)||"Not logged in"})]}),v.jsxs("div",{children:[v.jsx("span",{className:"text-sm font-medium",children:"Admin:"}),v.jsx(ve,{variant:k?"default":"destructive",className:"ml-2",children:k?"Yes":"No"})]})]}),v.jsxs(ce,{onClick:y,disabled:x||!k,className:"w-full",children:[v.jsx(kn,{className:"h-4 w-4 mr-2"}),x?"Running Tests...":"Run Workflow Tests"]}),c.length>0&&v.jsxs("div",{className:"space-y-2",children:[v.jsx("h3",{className:"text-lg font-semibold",children:"Test Results"}),c.map((d,o)=>v.jsxs("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[v.jsxs("div",{className:"flex items-center gap-3",children:[g(d.status),v.jsx("span",{className:"font-medium",children:d.name})]}),v.jsxs("div",{className:"flex items-center gap-2",children:[d.message&&v.jsx("span",{className:"text-sm text-gray-600",children:d.message}),v.jsx(ve,{variant:"outline",className:m(d.status),children:d.status})]})]},o))]}),c.length>0&&!x&&v.jsxs("div",{className:"mt-4 p-3 bg-blue-50 rounded-lg",children:[v.jsx("h4",{className:"font-medium text-blue-800",children:"Test Summary"}),v.jsxs("div",{className:"text-sm text-blue-600 mt-1",children:["Passed: ",c.filter(d=>d.status==="success").length," | Failed: ",c.filter(d=>d.status==="error").length," | Total: ",c.length]})]})]})]})},Ac=()=>{const{currentUser:p,isAdmin:k}=ke(),[x,u]=ne.useState(null),[c,N]=ne.useState(""),[w,y]=ne.useState(""),[g,m]=ne.useState({localStorage:"idle",firestore:"idle"});ne.useEffect(()=>{if(!c&&!w)return;const l=setTimeout(()=>{try{m(n=>({...n,localStorage:"saving"}));const r={title:c,content:w,timestamp:new Date().toISOString(),testMode:!0};localStorage.setItem("autosave-test-draft",JSON.stringify(r)),m(n=>({...n,localStorage:"success",lastLocalSave:new Date})),console.log("LocalStorage autosave successful")}catch(r){console.error("LocalStorage autosave failed:",r),m(n=>({...n,localStorage:"error"}))}},3e3);return()=>clearTimeout(l)},[c,w]),ne.useEffect(()=>{if(!x||!c&&!w)return;const l=setTimeout(async()=>{try{m(r=>({...r,firestore:"saving"})),await Tt(x,{title:c||"Autosave Test Blog",content:w||"Testing autosave functionality...",tags:["autosave","test"],excerpt:"Autosave test excerpt"}),m(r=>({...r,firestore:"success",lastFirestoreSave:new Date})),console.log("Firestore autosave successful")}catch(r){console.error("Firestore autosave failed:",r),m(n=>({...n,firestore:"error"}))}},8e3);return()=>clearTimeout(l)},[x,c,w]);const d=async()=>{var l;if(!p||!k){re.error("You must be logged in as an admin");return}try{const r={title:"Autosave Test Blog",content:"This is a test blog for autosave functionality.",tags:["autosave","test"],published:!1,authorId:p.uid,authorName:p.displayName||((l=p.email)==null?void 0:l.split("@")[0])||"Test User",authorEmail:p.email||"<EMAIL>",excerpt:"Test blog for autosave functionality"},n=await kt(r);u(n),re.success(`Test blog created with ID: ${n}`)}catch(r){console.error("Failed to create test blog:",r),re.error("Failed to create test blog")}},o=()=>{try{const l=localStorage.getItem("autosave-test-draft");if(l){const r=JSON.parse(l);N(r.title||""),y(r.content||""),re.success("Draft loaded from localStorage")}else re.info("No localStorage draft found")}catch(l){console.error("Failed to load from localStorage:",l),re.error("Failed to load from localStorage")}},e=()=>{try{localStorage.removeItem("autosave-test-draft"),re.success("localStorage draft cleared"),m(l=>({...l,localStorage:"idle",lastLocalSave:void 0}))}catch(l){console.error("Failed to clear localStorage:",l),re.error("Failed to clear localStorage")}},t=l=>{switch(l){case"idle":return v.jsx(He,{className:"h-4 w-4 text-gray-400"});case"saving":return v.jsx(He,{className:"h-4 w-4 text-blue-500 animate-spin"});case"success":return v.jsx(Ue,{className:"h-4 w-4 text-green-500"});case"error":return v.jsx(et,{className:"h-4 w-4 text-red-500"})}},a=l=>{switch(l){case"idle":return"bg-gray-100 text-gray-600";case"saving":return"bg-blue-100 text-blue-600";case"success":return"bg-green-100 text-green-600";case"error":return"bg-red-100 text-red-600"}},s=l=>l?l.toLocaleTimeString():"Never";return v.jsxs(tt,{className:"w-full max-w-4xl mx-auto",children:[v.jsxs(rt,{children:[v.jsxs(nt,{className:"flex items-center gap-2",children:[v.jsx(br,{className:"h-5 w-5 text-blue-500"}),"Autosave Functionality Tester"]}),v.jsx(it,{children:"Test both localStorage and Firestore autosave mechanisms. LocalStorage saves every 3 seconds, Firestore saves every 8 seconds."})]}),v.jsxs(at,{className:"space-y-6",children:[v.jsx("div",{className:"space-y-4",children:v.jsxs("div",{className:"flex items-center gap-4",children:[v.jsx(ce,{onClick:d,disabled:!!x||!k,variant:"outline",children:"Create Test Blog"}),x&&v.jsxs(ve,{variant:"secondary",children:["Test Blog ID: ",x]})]})}),v.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[v.jsxs("div",{className:"p-4 border rounded-lg",children:[v.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[v.jsx(Sn,{className:"h-4 w-4"}),v.jsx("span",{className:"font-medium",children:"LocalStorage Autosave"}),t(g.localStorage)]}),v.jsxs("div",{className:"space-y-1 text-sm",children:[v.jsxs("div",{className:"flex justify-between",children:[v.jsx("span",{children:"Status:"}),v.jsx(ve,{variant:"outline",className:a(g.localStorage),children:g.localStorage})]}),v.jsxs("div",{className:"flex justify-between",children:[v.jsx("span",{children:"Last Save:"}),v.jsx("span",{children:s(g.lastLocalSave)})]})]}),v.jsxs("div",{className:"flex gap-2 mt-3",children:[v.jsx(ce,{size:"sm",variant:"outline",onClick:o,children:"Load Draft"}),v.jsx(ce,{size:"sm",variant:"outline",onClick:e,children:"Clear"})]})]}),v.jsxs("div",{className:"p-4 border rounded-lg",children:[v.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[v.jsx(ze,{className:"h-4 w-4"}),v.jsx("span",{className:"font-medium",children:"Firestore Autosave"}),t(g.firestore)]}),v.jsxs("div",{className:"space-y-1 text-sm",children:[v.jsxs("div",{className:"flex justify-between",children:[v.jsx("span",{children:"Status:"}),v.jsx(ve,{variant:"outline",className:a(g.firestore),children:g.firestore})]}),v.jsxs("div",{className:"flex justify-between",children:[v.jsx("span",{children:"Last Save:"}),v.jsx("span",{children:s(g.lastFirestoreSave)})]}),v.jsxs("div",{className:"flex justify-between",children:[v.jsx("span",{children:"Requires:"}),v.jsx("span",{children:"Test Blog ID"})]})]})]})]}),v.jsxs("div",{className:"space-y-4",children:[v.jsxs("div",{children:[v.jsx("label",{className:"block text-sm font-medium mb-2",children:"Title"}),v.jsx(mt,{value:c,onChange:l=>N(l.target.value),placeholder:"Start typing to trigger autosave..."})]}),v.jsxs("div",{children:[v.jsx("label",{className:"block text-sm font-medium mb-2",children:"Content"}),v.jsx(bt,{value:w,onChange:l=>y(l.target.value),placeholder:"Start typing to trigger autosave...",rows:6})]})]}),v.jsxs("div",{className:"p-4 bg-blue-50 rounded-lg",children:[v.jsx("h4",{className:"font-medium text-blue-800 mb-2",children:"How to Test"}),v.jsxs("ol",{className:"text-sm text-blue-600 space-y-1",children:[v.jsx("li",{children:"1. Create a test blog first (required for Firestore autosave)"}),v.jsx("li",{children:"2. Start typing in the title or content fields"}),v.jsx("li",{children:"3. Watch the autosave status indicators"}),v.jsx("li",{children:"4. LocalStorage saves every 3 seconds, Firestore every 8 seconds"}),v.jsx("li",{children:"5. Test loading from localStorage and clearing drafts"})]})]})]})]})},jc=()=>{const{currentUser:p,isAdmin:k}=ke(),[x,u]=ne.useState(!1),[c,N]=ne.useState(!1),[w,y]=ne.useState(!1),[g,m]=ne.useState([]),[d,o]=ne.useState(null),e=async()=>{if(!p||!k){re.error("You must be logged in as an admin to initialize collections");return}u(!0);try{console.log("🔧 Starting blog collections initialization...");const r=await mr(p.uid,p.email||void 0);r.success?(re.success("Blog collections initialized successfully!"),console.log("✅ Collections initialization result:",r),await t()):(re.error(`Failed to initialize collections: ${r.message}`),console.error("❌ Collections initialization failed:",r))}catch(r){console.error("❌ Error during initialization:",r),re.error(`Initialization error: ${r instanceof Error?r.message:"Unknown error"}`)}finally{u(!1)}},t=async()=>{var r,n,f,i;if(!p||!k){re.error("You must be logged in as an admin to verify collections");return}N(!0);try{console.log("🔍 Verifying blog collections access...");const h=await yr(),T=[{name:Ke.BLOGS,initialized:!0,accessible:((r=h.details)==null?void 0:r.blogsReadAccess)||!1,error:(n=h.details)!=null&&n.blogsReadAccess?void 0:"Read access failed"},{name:Ke.BLOG_POSTS,initialized:!0,accessible:((f=h.details)==null?void 0:f.blogPostsReadAccess)||!1,error:(i=h.details)!=null&&i.blogPostsReadAccess?void 0:"Read access failed"}];m(T),o(new Date),h.success?re.success("All collections are accessible!"):re.warning(`Some collections have access issues: ${h.message}`),console.log("🔍 Verification result:",h)}catch(h){console.error("❌ Error during verification:",h),re.error(`Verification error: ${h instanceof Error?h.message:"Unknown error"}`)}finally{N(!1)}},a=async()=>{if(!p||!k){re.error("You must be logged in as an admin to cleanup placeholders");return}y(!0);try{console.log("🧹 Cleaning up placeholder documents...");const r=await cn();r.success?(re.success("Placeholder documents cleaned up successfully!"),console.log("✅ Cleanup result:",r),await t()):(re.error(`Cleanup failed: ${r.message}`),console.error("❌ Cleanup failed:",r))}catch(r){console.error("❌ Error during cleanup:",r),re.error(`Cleanup error: ${r instanceof Error?r.message:"Unknown error"}`)}finally{y(!1)}},s=r=>r.accessible?v.jsx(Ue,{className:"h-4 w-4 text-green-500"}):v.jsx(et,{className:"h-4 w-4 text-red-500"}),l=r=>r.accessible?v.jsx(ve,{variant:"default",className:"bg-green-100 text-green-700",children:"Accessible"}):v.jsx(ve,{variant:"destructive",children:"Not Accessible"});return v.jsxs(tt,{className:"w-full max-w-4xl mx-auto",children:[v.jsxs(rt,{children:[v.jsxs(nt,{className:"flex items-center gap-2",children:[v.jsx(ze,{className:"h-5 w-5 text-blue-500"}),"Blog Collections Initializer"]}),v.jsx(it,{children:"Initialize and manage Firestore collections for the blog system. This creates the necessary collections with proper structure and security rules."})]}),v.jsxs(at,{className:"space-y-6",children:[v.jsxs("div",{className:"flex items-center gap-4 p-3 bg-gray-50 rounded-lg",children:[v.jsxs("div",{children:[v.jsx("span",{className:"text-sm font-medium",children:"User:"}),v.jsx("span",{className:"ml-2 text-sm",children:(p==null?void 0:p.email)||"Not logged in"})]}),v.jsxs("div",{children:[v.jsx("span",{className:"text-sm font-medium",children:"Admin:"}),v.jsx(ve,{variant:k?"default":"destructive",className:"ml-2",children:k?"Yes":"No"})]})]}),v.jsxs("div",{className:"space-y-4",children:[v.jsx("h3",{className:"text-lg font-semibold",children:"Collections Overview"}),v.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[v.jsxs("div",{className:"p-4 border rounded-lg",children:[v.jsxs("h4",{className:"font-medium text-blue-800 mb-2",children:["📝 ",Ke.BLOGS]}),v.jsx("p",{className:"text-sm text-gray-600 mb-2",children:"Stores draft blog posts and admin content. Used for editing and autosave functionality."}),v.jsxs("div",{className:"text-xs text-gray-500",children:["• Admin-only read/write access",v.jsx("br",{}),"• Supports autosave and drafts",v.jsx("br",{}),"• Contains author information"]})]}),v.jsxs("div",{className:"p-4 border rounded-lg",children:[v.jsxs("h4",{className:"font-medium text-green-800 mb-2",children:["🌐 ",Ke.BLOG_POSTS]}),v.jsx("p",{className:"text-sm text-gray-600 mb-2",children:"Stores published, public-facing blog posts. Optimized for public consumption."}),v.jsxs("div",{className:"text-xs text-gray-500",children:["• Public read access",v.jsx("br",{}),"• Admin-only write access",v.jsx("br",{}),"• SEO-optimized structure"]})]})]})]}),v.jsxs("div",{className:"flex flex-wrap gap-3",children:[v.jsx(ce,{onClick:e,disabled:x||!k,className:"bg-blue-600 hover:bg-blue-700",children:x?v.jsxs(v.Fragment,{children:[v.jsx($e,{className:"h-4 w-4 mr-2 animate-spin"}),"Initializing..."]}):v.jsxs(v.Fragment,{children:[v.jsx(ze,{className:"h-4 w-4 mr-2"}),"Initialize Collections"]})}),v.jsx(ce,{onClick:t,disabled:c||!k,variant:"outline",children:c?v.jsxs(v.Fragment,{children:[v.jsx($e,{className:"h-4 w-4 mr-2 animate-spin"}),"Verifying..."]}):v.jsxs(v.Fragment,{children:[v.jsx(Ue,{className:"h-4 w-4 mr-2"}),"Verify Access"]})}),v.jsx(ce,{onClick:a,disabled:w||!k,variant:"outline",className:"border-orange-300 text-orange-600 hover:bg-orange-50",children:w?v.jsxs(v.Fragment,{children:[v.jsx($e,{className:"h-4 w-4 mr-2 animate-spin"}),"Cleaning..."]}):v.jsxs(v.Fragment,{children:[v.jsx(Pt,{className:"h-4 w-4 mr-2"}),"Cleanup Placeholders"]})})]}),g.length>0&&v.jsxs("div",{className:"space-y-3",children:[v.jsxs("div",{className:"flex items-center justify-between",children:[v.jsx("h3",{className:"text-lg font-semibold",children:"Collection Status"}),d&&v.jsxs("span",{className:"text-sm text-gray-500",children:["Last checked: ",d.toLocaleTimeString()]})]}),g.map((r,n)=>v.jsxs("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[v.jsxs("div",{className:"flex items-center gap-3",children:[s(r),v.jsx("span",{className:"font-medium",children:r.name}),r.error&&v.jsxs("span",{className:"text-sm text-red-600",children:["(",r.error,")"]})]}),l(r)]},n))]}),v.jsxs("div",{className:"p-4 bg-blue-50 rounded-lg",children:[v.jsxs("h4",{className:"font-medium text-blue-800 mb-2 flex items-center gap-2",children:[v.jsx(Fe,{className:"h-4 w-4"}),"Setup Instructions"]}),v.jsxs("ol",{className:"text-sm text-blue-600 space-y-1",children:[v.jsxs("li",{children:["1. ",v.jsx("strong",{children:"Initialize Collections:"})," Creates the necessary Firestore collections if they don't exist"]}),v.jsxs("li",{children:["2. ",v.jsx("strong",{children:"Verify Access:"})," Tests read/write permissions to ensure proper setup"]}),v.jsxs("li",{children:["3. ",v.jsx("strong",{children:"Cleanup Placeholders:"})," Removes temporary documents used for initialization (optional)"]}),v.jsxs("li",{children:["4. ",v.jsx("strong",{children:"Security Rules:"})," Ensure your Firestore security rules are deployed and up-to-date"]})]})]})]})]})},Tc=()=>{var t;const{currentUser:p,isAdmin:k,checkAdminStatus:x}=ke(),[u,c]=ne.useState(!1),[N,w]=ne.useState([]),y=(a,s,l,r)=>{w(n=>[...n,{step:a,status:s,message:l,data:r}])},g=async()=>{c(!0),w([]);try{if(y("Authentication","success","Checking current user authentication..."),!p){y("Authentication","error","No user is currently authenticated");return}y("Authentication","success",`User authenticated: ${p.email}`,{uid:p.uid,email:p.email,emailVerified:p.emailVerified,displayName:p.displayName}),y("Admin Status",k?"success":"warning",`Admin status from context: ${k}`);try{await Zr();const{getAuth:a}=await Se(async()=>{const{getAuth:r}=await import("./index.esm-CI7pLi7O.js");return{getAuth:r}},__vite__mapDeps([0,1,2,3])),l=a().currentUser;if(l){const r=await l.getIdToken(!0),n=await l.getIdTokenResult();y("Firebase Token","success","Firebase token retrieved successfully",{email:n.claims.email,emailVerified:n.claims.email_verified,authTime:new Date(n.authTime).toLocaleString(),issuedAt:new Date(n.issuedAtTime).toLocaleString(),expirationTime:new Date(n.expirationTime).toLocaleString()})}else y("Firebase Token","error","No Firebase user found")}catch(a){y("Firebase Token","error",`Token error: ${a instanceof Error?a.message:"Unknown error"}`)}try{const{doc:a,getDoc:s}=await Se(async()=>{const{doc:n,getDoc:f}=await import("./index.esm-ehpEbksy.js");return{doc:n,getDoc:f}},__vite__mapDeps([4,1])),l=a(ht,"admins","admins"),r=await s(l);if(r.exists()){const n=r.data();if(y("Admins Collection","success","Admins collection found",n),n.uids&&Array.isArray(n.uids)){const f=n.uids.includes(p.uid);y("Admin UID Check",f?"success":"warning",`User UID ${f?"found":"not found"} in admins list`,{userUID:p.uid,adminUIDs:n.uids})}else y("Admin UID Check","warning","Admins collection exists but no uids array found")}else y("Admins Collection","warning","Admins collection does not exist")}catch(a){y("Admins Collection","error",`Error accessing admins collection: ${a instanceof Error?a.message:"Unknown error"}`)}try{const{collection:a,getDocs:s,query:l,limit:r}=await Se(async()=>{const{collection:i,getDocs:h,query:T,limit:E}=await import("./index.esm-ehpEbksy.js");return{collection:i,getDocs:h,query:T,limit:E}},__vite__mapDeps([4,1])),n=a(ht,"blogs"),f=l(n,r(1));await s(f),y("Blogs Access","success","Successfully accessed blogs collection")}catch(a){y("Blogs Access","error",`Cannot access blogs collection: ${a instanceof Error?a.message:"Unknown error"}`)}try{const{collection:a,getDocs:s,query:l,limit:r}=await Se(async()=>{const{collection:i,getDocs:h,query:T,limit:E}=await import("./index.esm-ehpEbksy.js");return{collection:i,getDocs:h,query:T,limit:E}},__vite__mapDeps([4,1])),n=a(ht,"blogPosts"),f=l(n,r(1));await s(f),y("BlogPosts Access","success","Successfully accessed blogPosts collection")}catch(a){y("BlogPosts Access","error",`Cannot access blogPosts collection: ${a instanceof Error?a.message:"Unknown error"}`)}try{const a=await x();y("Admin Refresh","success",`Admin status after refresh: ${a}`)}catch(a){y("Admin Refresh","error",`Error refreshing admin status: ${a instanceof Error?a.message:"Unknown error"}`)}}catch(a){y("Debug Error","error",`Debug process failed: ${a instanceof Error?a.message:"Unknown error"}`)}finally{c(!1)}},m=async()=>{if(!p||!p.email){re.error("No user authenticated or email missing");return}try{const a=await fn(p.uid,p.email);a.success?(re.success("Admins collection setup successfully"),y("Admin Collection Setup","success",a.message,a.details)):(re.error(`Failed to setup admins collection: ${a.message}`),y("Admin Collection Setup","error",a.message,a.details))}catch(a){re.error(`Failed to setup admins collection: ${a instanceof Error?a.message:"Unknown error"}`),y("Admin Collection Setup","error",`Failed to setup admins collection: ${a instanceof Error?a.message:"Unknown error"}`)}},d=async()=>{if(!p){re.error("No user authenticated");return}try{const a=await dn(p.uid);a.success?(re.success("User has admin access"),y("Admin Access Verification","success",a.message,a.details)):(re.warning(`Admin access verification: ${a.message}`),y("Admin Access Verification","warning",a.message,a.details))}catch(a){re.error(`Failed to verify admin access: ${a instanceof Error?a.message:"Unknown error"}`),y("Admin Access Verification","error",`Failed to verify admin access: ${a instanceof Error?a.message:"Unknown error"}`)}},o=a=>{switch(a){case"success":return v.jsx(Ue,{className:"h-4 w-4 text-green-500"});case"error":return v.jsx(et,{className:"h-4 w-4 text-red-500"});case"warning":return v.jsx(Fe,{className:"h-4 w-4 text-yellow-500"})}},e=a=>{switch(a){case"success":return"bg-green-100 text-green-700";case"error":return"bg-red-100 text-red-700";case"warning":return"bg-yellow-100 text-yellow-700"}};return v.jsxs(tt,{className:"w-full max-w-4xl mx-auto",children:[v.jsxs(rt,{children:[v.jsxs(nt,{className:"flex items-center gap-2",children:[v.jsx(wt,{className:"h-5 w-5 text-blue-500"}),"Authentication & Permissions Debugger"]}),v.jsx(it,{children:"Debug authentication flow and Firebase permissions for blog collections access."})]}),v.jsxs(at,{className:"space-y-6",children:[v.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg",children:[v.jsxs("div",{className:"flex items-center gap-2",children:[v.jsx(Wr,{className:"h-4 w-4"}),v.jsx("span",{className:"text-sm font-medium",children:"User:"}),v.jsx("span",{className:"text-sm",children:(p==null?void 0:p.email)||"Not logged in"})]}),v.jsxs("div",{className:"flex items-center gap-2",children:[v.jsx(wt,{className:"h-4 w-4"}),v.jsx("span",{className:"text-sm font-medium",children:"Admin:"}),v.jsx(ve,{variant:k?"default":"destructive",children:k?"Yes":"No"})]}),v.jsxs("div",{className:"flex items-center gap-2",children:[v.jsx(ze,{className:"h-4 w-4"}),v.jsx("span",{className:"text-sm font-medium",children:"UID:"}),v.jsxs("span",{className:"text-xs font-mono",children:[((t=p==null?void 0:p.uid)==null?void 0:t.substring(0,8))||"N/A","..."]})]})]}),v.jsxs("div",{className:"flex flex-wrap gap-3",children:[v.jsx(ce,{onClick:g,disabled:u||!p,className:"bg-blue-600 hover:bg-blue-700",children:u?"Debugging...":"Run Auth Debug"}),v.jsx(ce,{onClick:m,disabled:!p,variant:"outline",className:"border-green-300 text-green-600 hover:bg-green-50",children:"Setup Admins Collection"}),v.jsx(ce,{onClick:d,disabled:!p,variant:"outline",className:"border-blue-300 text-blue-600 hover:bg-blue-50",children:"Verify Admin Access"})]}),N.length>0&&v.jsxs("div",{className:"space-y-3",children:[v.jsx("h3",{className:"text-lg font-semibold",children:"Debug Results"}),N.map((a,s)=>v.jsxs("div",{className:"border rounded-lg p-3",children:[v.jsxs("div",{className:"flex items-center justify-between mb-2",children:[v.jsxs("div",{className:"flex items-center gap-2",children:[o(a.status),v.jsx("span",{className:"font-medium",children:a.step})]}),v.jsx(ve,{variant:"outline",className:e(a.status),children:a.status})]}),v.jsx("p",{className:"text-sm text-gray-600 mb-2",children:a.message}),a.data&&v.jsxs("details",{className:"text-xs",children:[v.jsx("summary",{className:"cursor-pointer text-blue-600 hover:text-blue-800",children:"View Details"}),v.jsx("pre",{className:"mt-2 p-2 bg-gray-100 rounded overflow-x-auto",children:JSON.stringify(a.data,null,2)})]})]},s))]}),v.jsxs("div",{className:"p-4 bg-blue-50 rounded-lg",children:[v.jsx("h4",{className:"font-medium text-blue-800 mb-2",children:"Troubleshooting Steps"}),v.jsxs("ol",{className:"text-sm text-blue-600 space-y-1",children:[v.jsxs("li",{children:["1. ",v.jsx("strong",{children:"Run Auth Debug:"})," Check authentication status and permissions"]}),v.jsxs("li",{children:["2. ",v.jsx("strong",{children:"Create Admins Collection:"})," If missing, create the /admins collection"]}),v.jsxs("li",{children:["3. ",v.jsx("strong",{children:"Check Security Rules:"})," Ensure Firestore rules allow admin access"]}),v.jsxs("li",{children:["4. ",v.jsx("strong",{children:"Verify Email:"})," Ensure <EMAIL> is the authenticated email"]}),v.jsxs("li",{children:["5. ",v.jsx("strong",{children:"Check Token:"})," Verify Firebase authentication token is valid"]})]})]})]})]})},Sc=async()=>{console.log("🔍 Starting blog service debug tests...");const p={firebaseConnection:null,authStatus:null,permissions:null,errors:[]};try{console.log("🔧 Initializing blog collections...");const k=await mr();k.success?console.log("✅ Blog collections initialized successfully"):(console.error("❌ Blog collections initialization failed:",k.message),p.errors.push(`Collection initialization: ${k.message}`)),console.log("🔍 Verifying collection access...");const x=await yr();x.success?console.log("✅ Collection access verified successfully"):(console.error("❌ Collection access verification failed:",x.message),p.errors.push(`Collection access: ${x.message}`)),console.log("📡 Testing Firebase connection..."),p.firebaseConnection=await St(),p.firebaseConnection.success?console.log("✅ Firebase connection test passed"):(console.error("❌ Firebase connection test failed:",p.firebaseConnection.message),p.errors.push(`Firebase connection: ${p.firebaseConnection.message}`))}catch(k){const x=k instanceof Error?k.message:"Unknown error";console.error("❌ Debug test failed:",x),p.errors.push(`Debug test error: ${x}`)}return console.log("🔍 Debug test summary:",p),p},kc=async p=>{var u;if(!p)throw new Error("No authenticated user for blog creation test");const{createBlog:k}=await Se(async()=>{const{createBlog:c}=await import("./blogService-CUQdar90.js").then(N=>N.o);return{createBlog:c}},__vite__mapDeps([5,2,3,6,7,1])),x={title:`Test Blog ${new Date().toISOString()}`,content:"This is a test blog post created for debugging purposes.",tags:["test","debug"],published:!1,authorId:p.uid,authorName:p.displayName||((u=p.email)==null?void 0:u.split("@")[0])||"Test User",authorEmail:p.email||"<EMAIL>",excerpt:"Test blog excerpt"};console.log("🧪 Testing blog creation with data:",x);try{const c=await k(x);return console.log("✅ Test blog created successfully with ID:",c),{success:!0,blogId:c,data:x}}catch(c){throw console.error("❌ Test blog creation failed:",c),c}},Pc=async p=>{var u;if(!p)throw new Error("No authenticated user for blog publishing test");const{publishBlogPost:k}=await Se(async()=>{const{publishBlogPost:c}=await import("./blogService-CUQdar90.js").then(N=>N.o);return{publishBlogPost:c}},__vite__mapDeps([5,2,3,6,7,1])),x={title:`Published Test Blog ${new Date().toISOString()}`,content:"This is a test published blog post created for debugging purposes.",excerpt:"Test published blog excerpt",author:p.displayName||((u=p.email)==null?void 0:u.split("@")[0])||"Test User",tags:["test","debug","published"],coverImageUrl:null};console.log("🚀 Testing blog publishing with data:",x);try{const c=await k(x);return console.log("✅ Test blog published successfully with ID:",c),{success:!0,publishedId:c,data:x}}catch(c){throw console.error("❌ Test blog publishing failed:",c),c}},Lc=()=>{const{currentUser:p,isAdmin:k}=ke();return{runAllTests:async()=>{console.log("🔧 Running comprehensive blog debug tests..."),console.log("👤 Current user:",p==null?void 0:p.email),console.log("🔐 Is admin:",k);const u={connectionTest:null,creationTest:null,publishingTest:null,errors:[]};try{if(u.connectionTest=await Sc(),k&&p){try{u.creationTest=await kc(p)}catch(c){const N=c instanceof Error?c.message:"Unknown error";u.errors.push(`Creation test: ${N}`)}try{u.publishingTest=await Pc(p)}catch(c){const N=c instanceof Error?c.message:"Unknown error";u.errors.push(`Publishing test: ${N}`)}}else u.errors.push("User is not admin or not authenticated - skipping creation/publishing tests")}catch(c){const N=c instanceof Error?c.message:"Unknown error";u.errors.push(`Overall test error: ${N}`)}return console.log("🔧 All debug tests completed:",u),u},currentUser:p,isAdmin:k}},Ic=(p,k)=>{console.log("🔐 Authentication State:"),console.log("  - User:",(p==null?void 0:p.email)||"Not authenticated"),console.log("  - UID:",(p==null?void 0:p.uid)||"N/A"),console.log("  - Email verified:",(p==null?void 0:p.emailVerified)||!1),console.log("  - Is admin:",k),console.log("  - Display name:",(p==null?void 0:p.displayName)||"N/A")},af=()=>{const{currentUser:p,isAdmin:k}=ke(),{runAllTests:x}=Lc(),[u,c]=ne.useState([]),[N,w]=ne.useState(!0),[y,g]=ne.useState(null),[m,d]=ne.useState(!1),[o,e]=ne.useState(null),[t,a]=ne.useState("create"),[s,l]=ne.useState(!1),[r,n]=ne.useState(!1),[f,i]=ne.useState(null),[h,T]=ne.useState(!1),[E,A]=ne.useState(!1),[P,O]=ne.useState(!1),[b,_]=ne.useState(!1),[S,L]=ne.useState(!1),[R,$]=ne.useState(!1);ne.useEffect(()=>{U()},[]);const U=async()=>{try{w(!0),g(null);const C=await gr();c(C)}catch(C){console.error("Error fetching blogs:",C),C instanceof Error?g(`Failed to load blogs: ${C.message}`):g("Failed to load blogs. Please try again.")}finally{w(!1)}},Y=async()=>{try{w(!0),g(null);const C=await St();C.success?(re.success("Firebase connection test passed successfully!"),console.log("Connection test details:",C.details)):(re.error(`Connection test failed: ${C.message}`),console.error("Connection test details:",C.details),g(`Connection test failed: ${C.message}`))}catch(C){console.error("Error testing connection:",C);const z=C instanceof Error?C.message:"Unknown error";re.error(`Connection test error: ${z}`),g(`Connection test error: ${z}`)}finally{w(!1)}},M=async()=>{try{w(!0),g(null),Ic(p,k);const C=await x();C.errors.length===0?re.success("All debug tests passed successfully!"):(re.error(`Debug tests completed with ${C.errors.length} errors`),g(`Debug errors: ${C.errors.join(", ")}`)),console.log("Debug test results:",C)}catch(C){console.error("Error running debug tests:",C);const z=C instanceof Error?C.message:"Unknown error";re.error(`Debug test error: ${z}`),g(`Debug test error: ${z}`)}finally{w(!1)}},q=()=>{e(null),a("create"),d(!0)},j=C=>{e(C),a("edit"),d(!0)},D=async C=>{var z,K;if(!p){re.error("You must be logged in to save blogs");return}try{if(l(!0),t==="create")if(C.published)await xt({title:C.title,content:C.content,excerpt:C.excerpt,author:p.displayName||((z=p.email)==null?void 0:z.split("@")[0])||"Admin",tags:C.tags,coverImageUrl:C.coverImageUrl}),re.success("Blog post published successfully!"),d(!1);else{const Z={...C,authorId:p.uid,authorName:p.displayName||((K=p.email)==null?void 0:K.split("@")[0])||"Admin",authorEmail:p.email||""};await kt(Z),re.success("Draft saved successfully!")}else o&&(C.published&&!o.published?(await xt({title:C.title,content:C.content,excerpt:C.excerpt,author:o.authorName||"Admin",tags:C.tags,coverImageUrl:C.coverImageUrl}),re.success("Blog post published successfully!"),d(!1)):(await hn(o.id,C),C.published?(re.success("Published blog post updated successfully!"),d(!1)):re.success("Draft saved successfully!")));await U()}catch(Z){console.error("Error saving blog:",Z);let ee="An unknown error occurred";Z instanceof Error&&(ee=Z.message),C.published?re.error(`Failed to publish blog post: ${ee}`):re.error(`Failed to save draft: ${ee}`),console.error("Blog save error context:",{mode:t,published:C.published,hasTitle:!!C.title,hasContent:!!C.content,hasAuthor:!!p,userEmail:p==null?void 0:p.email})}finally{l(!1)}},B=async C=>{try{const z=await pn(C.id);re.success(`Blog post ${z?"published":"unpublished"} successfully`),await U()}catch(z){console.error("Error toggling publish status:",z),re.error("Failed to update publish status. Please try again.")}},H=C=>{i(C),n(!0)},F=async()=>{if(f)try{$(!0),await vn(f.id),re.success("Blog post deleted successfully"),await U(),n(!1)}catch(C){console.error("Error deleting blog:",C),re.error("Failed to delete blog post. Please try again.")}finally{$(!1)}},I=C=>new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(C);return v.jsxs(Yr,{title:"Blog Manager",description:"Create and manage blog posts and content",children:[v.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[v.jsxs("div",{children:[v.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Blog Manager"}),v.jsx("p",{className:"text-gray-600",children:"Create and manage blog posts and content"})]}),v.jsxs("div",{className:"flex gap-2 mt-4 md:mt-0",children:[v.jsxs(ce,{variant:"default",onClick:q,className:"flex items-center bg-burgundy-600 hover:bg-burgundy-700",children:[v.jsx(Bt,{className:"h-4 w-4 mr-2"}),"Create New Post"]}),v.jsx(ce,{variant:"outline",onClick:U,disabled:N,children:N?v.jsxs(v.Fragment,{children:[v.jsx($e,{className:"h-4 w-4 mr-2 animate-spin"}),"Loading..."]}):v.jsxs(v.Fragment,{children:[v.jsx($e,{className:"h-4 w-4 mr-2"}),"Refresh"]})}),v.jsxs(ce,{variant:"outline",onClick:Y,disabled:N,className:"border-blue-300 text-blue-600 hover:bg-blue-50",children:[v.jsx(Fe,{className:"h-4 w-4 mr-2"}),"Test Connection"]}),v.jsxs(ce,{variant:"outline",onClick:M,disabled:N,className:"border-purple-300 text-purple-600 hover:bg-purple-50",children:[v.jsx(jn,{className:"h-4 w-4 mr-2"}),"Run Debug Tests"]}),v.jsxs(ce,{variant:"outline",onClick:()=>A(!E),className:"border-green-300 text-green-600 hover:bg-green-50",children:[v.jsx(yt,{className:"h-4 w-4 mr-2"}),E?"Hide":"Show"," Workflow Tester"]}),v.jsxs(ce,{variant:"outline",onClick:()=>O(!P),className:"border-orange-300 text-orange-600 hover:bg-orange-50",children:[v.jsx(He,{className:"h-4 w-4 mr-2"}),P?"Hide":"Show"," Autosave Tester"]}),v.jsxs(ce,{variant:"outline",onClick:()=>_(!b),className:"border-indigo-300 text-indigo-600 hover:bg-indigo-50",children:[v.jsx(ze,{className:"h-4 w-4 mr-2"}),b?"Hide":"Show"," Collection Setup"]}),v.jsxs(ce,{variant:"outline",onClick:()=>L(!S),className:"border-red-300 text-red-600 hover:bg-red-50",children:[v.jsx(wt,{className:"h-4 w-4 mr-2"}),S?"Hide":"Show"," Auth Debug"]})]})]}),y&&v.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:v.jsx("p",{children:y})}),v.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6",children:[v.jsxs("div",{className:"flex items-center justify-between mb-3",children:[v.jsx("h3",{className:"text-sm font-medium text-gray-700",children:"Debug Information"}),v.jsxs(ce,{variant:"ghost",size:"sm",onClick:()=>T(!h),className:"text-xs",children:[h?"Hide":"Show"," Details"]})]}),v.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-xs",children:[v.jsxs("div",{children:[v.jsx("span",{className:"font-medium text-gray-600",children:"User:"}),v.jsx("p",{className:"text-gray-800",children:(p==null?void 0:p.email)||"Not logged in"})]}),v.jsxs("div",{children:[v.jsx("span",{className:"font-medium text-gray-600",children:"Admin:"}),v.jsx("p",{className:`${k?"text-green-600":"text-red-600"}`,children:k?"Yes":"No"})]}),v.jsxs("div",{children:[v.jsx("span",{className:"font-medium text-gray-600",children:"Email Verified:"}),v.jsx("p",{className:`${p!=null&&p.emailVerified?"text-green-600":"text-red-600"}`,children:p!=null&&p.emailVerified?"Yes":"No"})]}),v.jsxs("div",{children:[v.jsx("span",{className:"font-medium text-gray-600",children:"Blogs Count:"}),v.jsx("p",{className:"text-gray-800",children:u.length})]})]}),h&&v.jsx("div",{className:"mt-4 pt-4 border-t border-gray-200",children:v.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-xs",children:[v.jsxs("div",{children:[v.jsx("span",{className:"font-medium text-gray-600",children:"User ID:"}),v.jsx("p",{className:"text-gray-800 font-mono break-all",children:(p==null?void 0:p.uid)||"N/A"})]}),v.jsxs("div",{children:[v.jsx("span",{className:"font-medium text-gray-600",children:"Display Name:"}),v.jsx("p",{className:"text-gray-800",children:(p==null?void 0:p.displayName)||"N/A"})]})]})})]}),E&&v.jsx("div",{className:"mb-6",children:v.jsx(Ec,{})}),P&&v.jsx("div",{className:"mb-6",children:v.jsx(Ac,{})}),b&&v.jsx("div",{className:"mb-6",children:v.jsx(jc,{})}),S&&v.jsx("div",{className:"mb-6",children:v.jsx(Tc,{})}),N?v.jsxs("div",{className:"flex justify-center items-center py-12",children:[v.jsx(Ge,{size:"lg"}),v.jsx("span",{className:"ml-2 text-gray-600",children:"Loading blogs..."})]}):u.length>0?v.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:v.jsx("div",{className:"overflow-x-auto",children:v.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[v.jsx("thead",{className:"bg-gray-50",children:v.jsxs("tr",{children:[v.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Title"}),v.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tags"}),v.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),v.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),v.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),v.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:u.map(C=>v.jsxs("tr",{className:"hover:bg-gray-50",children:[v.jsx("td",{className:"px-6 py-4",children:v.jsxs("div",{className:"max-w-xs",children:[v.jsx("div",{className:"text-sm font-medium text-gray-900 truncate",children:C.title}),C.excerpt&&v.jsx("div",{className:"text-sm text-gray-500 truncate",children:C.excerpt})]})}),v.jsx("td",{className:"px-6 py-4",children:v.jsxs("div",{className:"flex flex-wrap gap-1",children:[C.tags.slice(0,3).map((z,K)=>v.jsx(ve,{variant:"secondary",className:"text-xs",children:z},K)),C.tags.length>3&&v.jsxs(ve,{variant:"outline",className:"text-xs",children:["+",C.tags.length-3]})]})}),v.jsx("td",{className:"px-6 py-4",children:v.jsx(ve,{variant:C.published?"default":"secondary",className:C.published?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800",children:C.published?"Published":"Draft"})}),v.jsx("td",{className:"px-6 py-4 text-sm text-gray-500",children:I(C.createdAt)}),v.jsx("td",{className:"px-6 py-4",children:v.jsxs("div",{className:"flex gap-2",children:[v.jsxs(ce,{variant:"outline",size:"sm",onClick:()=>j(C),className:"flex items-center gap-1",children:[v.jsx(An,{className:"h-4 w-4"}),"Edit"]}),v.jsxs(ce,{variant:"outline",size:"sm",onClick:()=>B(C),className:`flex items-center gap-1 ${C.published?"text-orange-600 hover:text-orange-800 hover:bg-orange-50":"text-green-600 hover:text-green-800 hover:bg-green-50"}`,children:[v.jsx(_t,{className:"h-4 w-4"}),C.published?"Unpublish":"Publish"]}),v.jsxs(ce,{variant:"outline",size:"sm",onClick:()=>H(C),className:"flex items-center gap-1 text-red-600 hover:text-red-800 hover:bg-red-50",children:[v.jsx(Pt,{className:"h-4 w-4"}),"Delete"]})]})})]},C.id))})]})})}):v.jsxs("div",{className:"bg-gray-50 rounded-lg p-8 text-center",children:[v.jsx("div",{className:"mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-burgundy-100 mb-4",children:v.jsx(yt,{className:"h-8 w-8 text-burgundy-600"})}),v.jsx("h2",{className:"text-xl font-medium text-gray-700 mb-2",children:"No blog posts found"}),v.jsx("p",{className:"text-gray-500 mb-4",children:"Get started by creating your first blog post."}),v.jsxs(ce,{onClick:q,className:"bg-burgundy-600 hover:bg-burgundy-700",children:[v.jsx(Bt,{className:"h-4 w-4 mr-2"}),"Create Your First Post"]})]}),v.jsx(Nc,{isOpen:m,onClose:()=>d(!1),onSave:D,blog:o,isLoading:s,mode:t}),v.jsx(yn,{open:r,onOpenChange:n,children:v.jsxs(bn,{children:[v.jsxs(xn,{children:[v.jsxs(_n,{className:"text-red-600 flex items-center gap-2",children:[v.jsx(Fe,{className:"h-5 w-5"}),"Delete Blog Post"]}),v.jsxs(wn,{children:["Are you sure you want to delete the blog post ",v.jsxs("strong",{children:['"',f==null?void 0:f.title,'"']}),"?",v.jsx("br",{}),v.jsx("br",{}),"This action is permanent and cannot be undone."]})]}),v.jsxs(On,{children:[v.jsx(Nn,{disabled:R,children:"Cancel"}),v.jsx(En,{onClick:F,disabled:R,className:"bg-red-600 hover:bg-red-700 text-white",children:R?v.jsxs(v.Fragment,{children:[v.jsx(Ge,{size:"sm",className:"mr-2"}),"Deleting..."]}):"Delete Post"})]})]})})]})};export{af as default};
