import{ref as $,uploadBytes as v,getDownloadURL as x,deleteObject as z}from"./index.esm-3FY_JRiK.js";import{aG as I}from"./index-Bm_kDzMk.js";const g={maxWidth:1200,maxHeight:1200,webpQuality:.85,jpegQuality:.85,maxFileSize:5*1024*1024,targetFileSize:200*1024,supportedFormats:["image/jpeg","image/jpg","image/png","image/webp"],outputFormat:"webp",fallbackFormat:"jpeg"};function S(){return new Promise(e=>{const o=new Image;o.onload=o.onerror=()=>{e(o.height===2)},o.src="data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA"})}function b(e){return g.supportedFormats.includes(e.type)?e.size>g.maxFileSize?{valid:!1,error:`File size too large. Maximum size: ${g.maxFileSize/1024/1024}MB`}:{valid:!0}:{valid:!1,error:`Unsupported file format. Supported formats: ${g.supportedFormats.join(", ")}`}}function A(e,o){const t=document.createElement("canvas");return t.width=e,t.height=o,t}function E(e,o,t=g.maxWidth,r=g.maxHeight){let{width:s,height:l}={width:e,height:o};const i=t/s,c=r/l,a=Math.min(i,c,1);return s=Math.round(s*a),l=Math.round(l*a),{width:s,height:l}}function U(e){return new Promise((o,t)=>{const r=new Image;r.onload=()=>o(r),r.onerror=()=>t(new Error("Failed to load image")),r.src=URL.createObjectURL(e)})}function y(e,o="webp",t=.85){return new Promise((r,s)=>{e.toBlob(l=>{l?r(l):s(new Error("Failed to convert canvas to blob"))},`image/${o}`,t)})}async function R(e,o={}){const t=b(e);if(!t.valid)throw new Error(t.error);const{maxWidth:r=g.maxWidth,maxHeight:s=g.maxHeight,quality:l=g.webpQuality,format:i=await S()?"webp":g.fallbackFormat,targetSize:c=g.targetFileSize}=o;try{const a=await U(e),{width:n,height:m}=E(a.naturalWidth,a.naturalHeight,r,s),p=A(n,m),u=p.getContext("2d");if(!u)throw new Error("Failed to get canvas context");u.imageSmoothingEnabled=!0,u.imageSmoothingQuality="high",u.drawImage(a,0,0,n,m),URL.revokeObjectURL(a.src);let f=await y(p,i,l),h=l;for(;f.size>c&&h>.3;)h-=.1,f=await y(p,i,h);const w=new File([f],`${e.name.split(".")[0]}.${i}`,{type:`image/${i}`});return{file:w,originalSize:e.size,processedSize:w.size,format:i}}catch(a){throw console.error("Image processing failed:",a),new Error(`Image processing failed: ${a instanceof Error?a.message:"Unknown error"}`)}}const d="https://via.placeholder.com/150?text=No+Image",k=async(e,o,t,r={})=>{try{if(console.log(`Uploading image to path: ${o}`),console.log(`Image file details: name=${e.name}, size=${e.size}, type=${e.type}`),!e)return console.error("No image file provided"),d;if(!o)return console.error("No path provided"),d;const s=b(e);if(!s.valid)return console.error("Image validation failed:",s.error),d;let l=e,i=o;if(!r.skipProcessing)try{console.log("Processing image for optimization..."),t&&t(10);const n=await R(e,{maxWidth:r.maxWidth,maxHeight:r.maxHeight,quality:r.quality});l=n.file,n.format!==e.type.split("/")[1]&&(i=o.replace(/\.[^.]+$/,`.${n.format}`)),console.log(`Image processed: ${e.size} bytes -> ${n.file.size} bytes (${n.format})`),t&&t(30)}catch(n){console.warn("Image processing failed, uploading original:",n)}const c=$(I,i);console.log(`Storage reference created: ${c.fullPath}`);const a={contentType:l.type,customMetadata:{originalName:e.name,originalSize:e.size.toString(),processedSize:l.size.toString(),uploadedAt:new Date().toISOString()}};console.log("Metadata:",a),console.log("Starting file upload..."),t&&t(50);try{const n=await v(c,l,a);console.log("Image uploaded successfully"),console.log("Upload snapshot:",{bytesTransferred:n.bytesTransferred,totalBytes:n.totalBytes,state:n.state}),t&&t(90),console.log("Getting download URL...");const m=await x(n.ref);return console.log("Image download URL:",m),t&&(console.log("Calling progress callback with 100%"),t(100)),m}catch(n){throw console.error("Error in uploadBytes:",n),n instanceof Error&&console.error("Upload error details:",{message:n.message,stack:n.stack}),n}}catch(s){return console.error("Error uploading image:",s),s instanceof Error&&console.error("Error details:",{message:s.message,stack:s.stack}),d}},F=async(e,o,t)=>{try{if(console.log(`Uploading ${e.length} images`),console.log("Image files:",e.map(a=>({name:a.name,size:a.size,type:a.type}))),console.log("User ID:",o),!e||e.length===0)return console.error("No image files provided"),[d];if(!o)return console.error("No user ID provided"),[d];const r=[],s=e.length;let l=0;for(const a of e){console.log(`Processing file: ${a.name} (${a.size} bytes)`);const n=M(o,a.name);console.log(`Generated path: ${n}`);const m=k(a,n,p=>{if(console.log(`File ${a.name} progress: ${p}%`),p===100&&(l++,t)){const u=Math.round(l/s*100);console.log(`Overall progress: ${u}%`),t(u)}});r.push(m)}console.log(`Created ${r.length} upload promises`);const i=await Promise.all(r);console.log("All uploads completed. Results:",i);const c=i.filter(a=>a!==d);return console.log(`Successful uploads: ${c.length}/${i.length}`),c.length===0&&i.length>0?(console.log("All uploads failed, returning default URL"),[d]):(console.log("Final URLs:",c),c)}catch(r){return console.error("Error uploading multiple images:",r),r instanceof Error&&console.error("Error details:",{message:r.message,stack:r.stack}),[d]}},W=async e=>{try{if(e===d)return console.log("Skipping deletion of default image"),!0;const o=L(e);if(!o)return console.error("Could not extract path from URL:",e),!1;console.log(`Deleting image at path: ${o}`);const t=$(I,o);return await z(t),console.log("Image deleted successfully"),!0}catch(o){return console.error("Error deleting image:",o),!1}},L=e=>{try{const o=/\/o\/([^?]+)/,t=e.match(o);return t&&t[1]?decodeURIComponent(t[1]):null}catch(o){return console.error("Error extracting path from URL:",o),null}},M=(e,o)=>{const t=new Date().getTime(),r=Math.random().toString(36).substring(2,6);return`book-images/${e}/${t}-${r}.webp`};export{k as a,W as d,F as u};
