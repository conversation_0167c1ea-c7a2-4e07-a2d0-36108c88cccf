/**
 * Blog Collections Configuration and Initialization
 * This file manages the Firestore collections for the blog system
 */

import { initializeFirebase, db } from './firebase';

// Collection names as constants
export const BLOG_COLLECTIONS = {
  BLOGS: 'blogs',           // Draft blog posts and admin content
  BLOG_POSTS: 'blogPosts'   // Published, public-facing blog posts
} as const;

// Document structure interfaces for validation
export interface BlogDocument {
  title: string;
  content: string;
  tags: string[];
  published: boolean;
  authorId: string;
  authorName: string;
  authorEmail: string;
  slug?: string;
  excerpt?: string;
  readTime?: number;
  views?: number;
  coverImageUrl?: string;
  createdAt: any; // Firestore timestamp
  updatedAt: any; // Firestore timestamp
}

export interface PublishedBlogDocument {
  title: string;
  content: string;
  excerpt: string;
  author: string;
  tags: string[];
  coverImageUrl?: string;
  published: boolean; // Always true for blogPosts collection
  slug: string;
  readTime: number;
  createdAt: any; // Firestore timestamp
  updatedAt: any; // Firestore timestamp
}

/**
 * Initialize blog collections and verify they exist
 * @param userUID - Optional user UID to set up as admin
 * @param userEmail - Optional user email to set up as admin
 * @returns Promise<{success: boolean, message: string, details?: any}>
 */
export const initializeBlogCollections = async (userUID?: string, userEmail?: string): Promise<{success: boolean, message: string, details?: any}> => {
  try {
    console.log('🔧 Initializing blog collections...');
    
    // Initialize Firebase
    await initializeFirebase();
    
    // Dynamically import Firestore functions
    const { collection, doc, setDoc, getDoc, getDocs, query, limit } = await import('firebase/firestore');
    
    const results = {
      blogsCollection: false,
      blogPostsCollection: false,
      blogsAccessible: false,
      blogPostsAccessible: false,
      adminsCollection: false,
      errors: [] as string[]
    };

    // Test 1: Check if blogs collection exists and is accessible
    try {
      const blogsRef = collection(db, BLOG_COLLECTIONS.BLOGS);
      const blogsQuery = query(blogsRef, limit(1));
      await getDocs(blogsQuery);
      results.blogsCollection = true;
      results.blogsAccessible = true;
      console.log('✅ Blogs collection is accessible');
    } catch (error) {
      console.log('⚠️ Blogs collection needs initialization');
      results.errors.push(`Blogs collection error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Test 2: Check if blogPosts collection exists and is accessible
    try {
      const blogPostsRef = collection(db, BLOG_COLLECTIONS.BLOG_POSTS);
      const blogPostsQuery = query(blogPostsRef, limit(1));
      await getDocs(blogPostsQuery);
      results.blogPostsCollection = true;
      results.blogPostsAccessible = true;
      console.log('✅ BlogPosts collection is accessible');
    } catch (error) {
      console.log('⚠️ BlogPosts collection needs initialization');
      results.errors.push(`BlogPosts collection error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Initialize collections if they don't exist by creating placeholder documents
    if (!results.blogsCollection) {
      try {
        const blogsRef = collection(db, BLOG_COLLECTIONS.BLOGS);
        const placeholderDoc = doc(blogsRef, '_placeholder');
        await setDoc(placeholderDoc, {
          _placeholder: true,
          _description: 'This is a placeholder document to initialize the blogs collection',
          _createdAt: new Date().toISOString()
        });
        results.blogsCollection = true;
        console.log('✅ Blogs collection initialized with placeholder');
      } catch (error) {
        results.errors.push(`Failed to initialize blogs collection: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    if (!results.blogPostsCollection) {
      try {
        const blogPostsRef = collection(db, BLOG_COLLECTIONS.BLOG_POSTS);
        const placeholderDoc = doc(blogPostsRef, '_placeholder');
        await setDoc(placeholderDoc, {
          _placeholder: true,
          _description: 'This is a placeholder document to initialize the blogPosts collection',
          _createdAt: new Date().toISOString()
        });
        results.blogPostsCollection = true;
        console.log('✅ BlogPosts collection initialized with placeholder');
      } catch (error) {
        results.errors.push(`Failed to initialize blogPosts collection: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    // Initialize admins collection if user credentials provided
    if (userUID && userEmail) {
      try {
        const adminsResult = await setupAdminsCollection(userUID, userEmail);
        results.adminsCollection = adminsResult.success;
        if (!adminsResult.success) {
          results.errors.push(`Admins collection setup failed: ${adminsResult.message}`);
        } else {
          console.log('✅ Admins collection setup completed');
        }
      } catch (error) {
        results.errors.push(`Admins collection setup error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    // Final verification
    const allInitialized = results.blogsCollection && results.blogPostsCollection;
    const allAccessible = results.blogsAccessible && results.blogPostsAccessible;

    if (allInitialized && allAccessible) {
      return {
        success: true,
        message: 'All blog collections initialized and accessible successfully',
        details: results
      };
    } else if (allInitialized) {
      return {
        success: true,
        message: 'Blog collections initialized but some access issues detected',
        details: results
      };
    } else {
      return {
        success: false,
        message: `Failed to initialize blog collections: ${results.errors.join(', ')}`,
        details: results
      };
    }

  } catch (error) {
    console.error('❌ Failed to initialize blog collections:', error);
    return {
      success: false,
      message: `Blog collections initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      details: { error: error instanceof Error ? error.message : String(error) }
    };
  }
};

/**
 * Verify collection access and permissions
 * @returns Promise<{success: boolean, message: string, details?: any}>
 */
export const verifyBlogCollectionAccess = async (): Promise<{success: boolean, message: string, details?: any}> => {
  try {
    await initializeFirebase();
    
    const { collection, getDocs, query, limit } = await import('firebase/firestore');
    
    const results = {
      blogsReadAccess: false,
      blogPostsReadAccess: false,
      errors: [] as string[]
    };

    // Test read access to blogs collection
    try {
      const blogsRef = collection(db, BLOG_COLLECTIONS.BLOGS);
      const blogsQuery = query(blogsRef, limit(1));
      const blogsSnapshot = await getDocs(blogsQuery);
      results.blogsReadAccess = true;
      console.log(`✅ Blogs collection read access verified (${blogsSnapshot.size} documents)`);
    } catch (error) {
      results.errors.push(`Blogs read access failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Test read access to blogPosts collection
    try {
      const blogPostsRef = collection(db, BLOG_COLLECTIONS.BLOG_POSTS);
      const blogPostsQuery = query(blogPostsRef, limit(1));
      const blogPostsSnapshot = await getDocs(blogPostsQuery);
      results.blogPostsReadAccess = true;
      console.log(`✅ BlogPosts collection read access verified (${blogPostsSnapshot.size} documents)`);
    } catch (error) {
      results.errors.push(`BlogPosts read access failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    const allAccessible = results.blogsReadAccess && results.blogPostsReadAccess;

    return {
      success: allAccessible,
      message: allAccessible 
        ? 'All blog collections are accessible' 
        : `Some collections are not accessible: ${results.errors.join(', ')}`,
      details: results
    };

  } catch (error) {
    return {
      success: false,
      message: `Collection access verification failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      details: { error: error instanceof Error ? error.message : String(error) }
    };
  }
};

/**
 * Set up the admins collection with the current user
 * @param userUID - The UID of the user to add as admin
 * @param userEmail - The email of the user to add as admin
 * @returns Promise<{success: boolean, message: string, details?: any}>
 */
export const setupAdminsCollection = async (userUID: string, userEmail: string): Promise<{success: boolean, message: string, details?: any}> => {
  try {
    console.log('🔧 Setting up admins collection...');

    await initializeFirebase();

    const { doc, setDoc, getDoc } = await import('firebase/firestore');

    const adminsRef = doc(db, 'admins', 'admins');

    // Check if admins collection already exists
    const adminsSnap = await getDoc(adminsRef);

    if (adminsSnap.exists()) {
      const adminsData = adminsSnap.data();
      console.log('📋 Existing admins collection found:', adminsData);

      // Check if user is already in the list
      if (adminsData.uids && adminsData.uids.includes(userUID)) {
        return {
          success: true,
          message: 'User is already in the admins list',
          details: adminsData
        };
      }

      // Add user to existing list
      const updatedUIDs = [...(adminsData.uids || []), userUID];
      const updatedEmails = [...(adminsData.emails || []), userEmail];

      await setDoc(adminsRef, {
        ...adminsData,
        uids: updatedUIDs,
        emails: updatedEmails,
        updatedAt: new Date().toISOString()
      });

      console.log('✅ User added to existing admins collection');
      return {
        success: true,
        message: 'User added to existing admins collection',
        details: { uids: updatedUIDs, emails: updatedEmails }
      };
    } else {
      // Create new admins collection
      const adminsData = {
        uids: [userUID],
        emails: [userEmail],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        description: 'Admin users for blog management system'
      };

      await setDoc(adminsRef, adminsData);

      console.log('✅ New admins collection created');
      return {
        success: true,
        message: 'New admins collection created successfully',
        details: adminsData
      };
    }

  } catch (error) {
    console.error('❌ Failed to setup admins collection:', error);
    return {
      success: false,
      message: `Failed to setup admins collection: ${error instanceof Error ? error.message : 'Unknown error'}`,
      details: { error: error instanceof Error ? error.message : String(error) }
    };
  }
};

/**
 * Verify admins collection and user access
 * @param userUID - The UID of the user to check
 * @returns Promise<{success: boolean, message: string, details?: any}>
 */
export const verifyAdminAccess = async (userUID: string): Promise<{success: boolean, message: string, details?: any}> => {
  try {
    await initializeFirebase();

    const { doc, getDoc } = await import('firebase/firestore');

    const adminsRef = doc(db, 'admins', 'admins');
    const adminsSnap = await getDoc(adminsRef);

    if (!adminsSnap.exists()) {
      return {
        success: false,
        message: 'Admins collection does not exist',
        details: { collectionExists: false }
      };
    }

    const adminsData = adminsSnap.data();
    const isAdmin = adminsData.uids && adminsData.uids.includes(userUID);

    return {
      success: isAdmin,
      message: isAdmin ? 'User has admin access' : 'User does not have admin access',
      details: {
        collectionExists: true,
        userUID,
        adminUIDs: adminsData.uids || [],
        adminEmails: adminsData.emails || [],
        isAdmin
      }
    };

  } catch (error) {
    return {
      success: false,
      message: `Failed to verify admin access: ${error instanceof Error ? error.message : 'Unknown error'}`,
      details: { error: error instanceof Error ? error.message : String(error) }
    };
  }
};

/**
 * Clean up placeholder documents (optional maintenance function)
 */
export const cleanupPlaceholders = async (): Promise<{success: boolean, message: string}> => {
  try {
    await initializeFirebase();

    const { doc, deleteDoc } = await import('firebase/firestore');

    const placeholders = [
      doc(db, BLOG_COLLECTIONS.BLOGS, '_placeholder'),
      doc(db, BLOG_COLLECTIONS.BLOG_POSTS, '_placeholder')
    ];

    for (const placeholder of placeholders) {
      try {
        await deleteDoc(placeholder);
        console.log(`🧹 Cleaned up placeholder: ${placeholder.path}`);
      } catch (error) {
        console.log(`⚠️ Placeholder not found or already deleted: ${placeholder.path}`);
      }
    }

    return {
      success: true,
      message: 'Placeholder cleanup completed'
    };

  } catch (error) {
    return {
      success: false,
      message: `Placeholder cleanup failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
};
