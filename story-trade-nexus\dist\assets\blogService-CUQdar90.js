const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.esm-ehpEbksy.js","assets/index.esm2017-H7c5Bkvh.js"])))=>i.map(i=>d[i]);
import{w as f,_ as b,V as p,aF as S}from"./index-Bm_kDzMk.js";import{a as T,d as $}from"./storageService-eCr3LQAS.js";const w={BLOGS:"blogs",BLOG_POSTS:"blogPosts"},O=async(e,t)=>{try{console.log("🔧 Initializing blog collections..."),await f();const{collection:n,doc:c,setDoc:i,getDoc:d,getDocs:r,query:l,limit:s}=await b(async()=>{const{collection:a,doc:u,setDoc:h,getDoc:A,getDocs:y,query:v,limit:E}=await import("./index.esm-ehpEbksy.js");return{collection:a,doc:u,setDoc:h,getDoc:A,getDocs:y,query:v,limit:E}},__vite__mapDeps([0,1])),o={blogsCollection:!1,blogPostsCollection:!1,blogsAccessible:!1,blogPostsAccessible:!1,adminsCollection:!1,errors:[]};try{const a=n(p,w.BLOGS),u=l(a,s(1));await r(u),o.blogsCollection=!0,o.blogsAccessible=!0,console.log("✅ Blogs collection is accessible")}catch(a){console.log("⚠️ Blogs collection needs initialization"),o.errors.push(`Blogs collection error: ${a instanceof Error?a.message:"Unknown error"}`)}try{const a=n(p,w.BLOG_POSTS),u=l(a,s(1));await r(u),o.blogPostsCollection=!0,o.blogPostsAccessible=!0,console.log("✅ BlogPosts collection is accessible")}catch(a){console.log("⚠️ BlogPosts collection needs initialization"),o.errors.push(`BlogPosts collection error: ${a instanceof Error?a.message:"Unknown error"}`)}if(!o.blogsCollection)try{const a=n(p,w.BLOGS),u=c(a,"_placeholder");await i(u,{_placeholder:!0,_description:"This is a placeholder document to initialize the blogs collection",_createdAt:new Date().toISOString()}),o.blogsCollection=!0,console.log("✅ Blogs collection initialized with placeholder")}catch(a){o.errors.push(`Failed to initialize blogs collection: ${a instanceof Error?a.message:"Unknown error"}`)}if(!o.blogPostsCollection)try{const a=n(p,w.BLOG_POSTS),u=c(a,"_placeholder");await i(u,{_placeholder:!0,_description:"This is a placeholder document to initialize the blogPosts collection",_createdAt:new Date().toISOString()}),o.blogPostsCollection=!0,console.log("✅ BlogPosts collection initialized with placeholder")}catch(a){o.errors.push(`Failed to initialize blogPosts collection: ${a instanceof Error?a.message:"Unknown error"}`)}if(e&&t)try{const a=await R(e,t);o.adminsCollection=a.success,a.success?console.log("✅ Admins collection setup completed"):o.errors.push(`Admins collection setup failed: ${a.message}`)}catch(a){o.errors.push(`Admins collection setup error: ${a instanceof Error?a.message:"Unknown error"}`)}const m=o.blogsCollection&&o.blogPostsCollection,g=o.blogsAccessible&&o.blogPostsAccessible;return m&&g?{success:!0,message:"All blog collections initialized and accessible successfully",details:o}:m?{success:!0,message:"Blog collections initialized but some access issues detected",details:o}:{success:!1,message:`Failed to initialize blog collections: ${o.errors.join(", ")}`,details:o}}catch(n){return console.error("❌ Failed to initialize blog collections:",n),{success:!1,message:`Blog collections initialization failed: ${n instanceof Error?n.message:"Unknown error"}`,details:{error:n instanceof Error?n.message:String(n)}}}},I=async()=>{try{await f();const{collection:e,getDocs:t,query:n,limit:c}=await b(async()=>{const{collection:r,getDocs:l,query:s,limit:o}=await import("./index.esm-ehpEbksy.js");return{collection:r,getDocs:l,query:s,limit:o}},__vite__mapDeps([0,1])),i={blogsReadAccess:!1,blogPostsReadAccess:!1,errors:[]};try{const r=e(p,w.BLOGS),l=n(r,c(1)),s=await t(l);i.blogsReadAccess=!0,console.log(`✅ Blogs collection read access verified (${s.size} documents)`)}catch(r){i.errors.push(`Blogs read access failed: ${r instanceof Error?r.message:"Unknown error"}`)}try{const r=e(p,w.BLOG_POSTS),l=n(r,c(1)),s=await t(l);i.blogPostsReadAccess=!0,console.log(`✅ BlogPosts collection read access verified (${s.size} documents)`)}catch(r){i.errors.push(`BlogPosts read access failed: ${r instanceof Error?r.message:"Unknown error"}`)}const d=i.blogsReadAccess&&i.blogPostsReadAccess;return{success:d,message:d?"All blog collections are accessible":`Some collections are not accessible: ${i.errors.join(", ")}`,details:i}}catch(e){return{success:!1,message:`Collection access verification failed: ${e instanceof Error?e.message:"Unknown error"}`,details:{error:e instanceof Error?e.message:String(e)}}}},R=async(e,t)=>{try{console.log("🔧 Setting up admins collection..."),await f();const{doc:n,setDoc:c,getDoc:i}=await b(async()=>{const{doc:l,setDoc:s,getDoc:o}=await import("./index.esm-ehpEbksy.js");return{doc:l,setDoc:s,getDoc:o}},__vite__mapDeps([0,1])),d=n(p,"admins","admins"),r=await i(d);if(r.exists()){const l=r.data();if(console.log("📋 Existing admins collection found:",l),l.uids&&l.uids.includes(e))return{success:!0,message:"User is already in the admins list",details:l};const s=[...l.uids||[],e],o=[...l.emails||[],t];return await c(d,{...l,uids:s,emails:o,updatedAt:new Date().toISOString()}),console.log("✅ User added to existing admins collection"),{success:!0,message:"User added to existing admins collection",details:{uids:s,emails:o}}}else{const l={uids:[e],emails:[t],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),description:"Admin users for blog management system"};return await c(d,l),console.log("✅ New admins collection created"),{success:!0,message:"New admins collection created successfully",details:l}}}catch(n){return console.error("❌ Failed to setup admins collection:",n),{success:!1,message:`Failed to setup admins collection: ${n instanceof Error?n.message:"Unknown error"}`,details:{error:n instanceof Error?n.message:String(n)}}}},H=async e=>{try{await f();const{doc:t,getDoc:n}=await b(async()=>{const{doc:l,getDoc:s}=await import("./index.esm-ehpEbksy.js");return{doc:l,getDoc:s}},__vite__mapDeps([0,1])),c=t(p,"admins","admins"),i=await n(c);if(!i.exists())return{success:!1,message:"Admins collection does not exist",details:{collectionExists:!1}};const d=i.data(),r=d.uids&&d.uids.includes(e);return{success:r,message:r?"User has admin access":"User does not have admin access",details:{collectionExists:!0,userUID:e,adminUIDs:d.uids||[],adminEmails:d.emails||[],isAdmin:r}}}catch(t){return{success:!1,message:`Failed to verify admin access: ${t instanceof Error?t.message:"Unknown error"}`,details:{error:t instanceof Error?t.message:String(t)}}}},W=async()=>{try{await f();const{doc:e,deleteDoc:t}=await b(async()=>{const{doc:c,deleteDoc:i}=await import("./index.esm-ehpEbksy.js");return{doc:c,deleteDoc:i}},__vite__mapDeps([0,1])),n=[e(p,w.BLOGS,"_placeholder"),e(p,w.BLOG_POSTS,"_placeholder")];for(const c of n)try{await t(c),console.log(`🧹 Cleaned up placeholder: ${c.path}`)}catch{console.log(`⚠️ Placeholder not found or already deleted: ${c.path}`)}return{success:!0,message:"Placeholder cleanup completed"}}catch(e){return{success:!1,message:`Placeholder cleanup failed: ${e instanceof Error?e.message:"Unknown error"}`}}},x=async()=>{var e,t,n,c;try{console.log("🔍 Testing Firebase connection for blog management..."),await f();const i={firebaseInitialized:!1,collectionsInitialized:!1,collectionsAccessible:!1,blogsCollectionAccess:!1,blogPostsCollectionAccess:!1,readPermissions:!1,initializationDetails:null,accessDetails:null};if(p)i.firebaseInitialized=!0,console.log("✅ Firebase initialized successfully");else throw new Error("Firebase database not initialized");console.log("🔧 Initializing blog collections...");const d=await O();i.collectionsInitialized=d.success,i.initializationDetails=d,d.success?console.log("✅ Blog collections initialized successfully"):console.error("❌ Blog collections initialization failed:",d.message),console.log("🔍 Verifying collection access...");const r=await I();return i.collectionsAccessible=r.success,i.accessDetails=r,r.success?(i.blogsCollectionAccess=((e=r.details)==null?void 0:e.blogsReadAccess)||!1,i.blogPostsCollectionAccess=((t=r.details)==null?void 0:t.blogPostsReadAccess)||!1,i.readPermissions=((n=r.details)==null?void 0:n.blogsReadAccess)&&((c=r.details)==null?void 0:c.blogPostsReadAccess),console.log("✅ Collection access verified successfully")):console.error("❌ Collection access verification failed:",r.message),i.firebaseInitialized&&i.collectionsInitialized&&i.collectionsAccessible&&i.readPermissions?{success:!0,message:"All Firebase connection and collection tests passed successfully",details:i}:{success:!1,message:"Some Firebase connection or collection tests failed",details:i}}catch(i){return console.error("❌ Firebase connection test failed:",i),{success:!1,message:`Firebase connection test failed: ${i instanceof Error?i.message:"Unknown error"}`,details:{error:i instanceof Error?i.message:String(i)}}}},L=async()=>{try{await f();const{collection:e,query:t,getDocs:n,orderBy:c}=await b(async()=>{const{collection:s,query:o,getDocs:m,orderBy:g}=await import("./index.esm-ehpEbksy.js");return{collection:s,query:o,getDocs:m,orderBy:g}},__vite__mapDeps([0,1]));console.log("Fetching all blogs from Firestore");const i=e(p,w.BLOGS),d=t(i,c("createdAt","desc")),r=await n(d),l=[];return r.forEach(s=>{var u,h;const o=s.data(),m=(u=o.createdAt)!=null&&u.toDate?o.createdAt.toDate():new Date,g=(h=o.updatedAt)!=null&&h.toDate?o.updatedAt.toDate():new Date,a={id:s.id,title:o.title||"",content:o.content||"",tags:Array.isArray(o.tags)?o.tags:[],published:o.published||!1,status:o.published?S.Published:S.Draft,createdAt:m,updatedAt:g,authorId:o.authorId||"",authorName:o.authorName||"",authorEmail:o.authorEmail||"",slug:o.slug,excerpt:o.excerpt,readTime:o.readTime,views:o.views||0,coverImageUrl:o.coverImageUrl};l.push(a)}),console.log(`Found ${l.length} blogs in Firestore`),l}catch(e){throw console.error("Error getting blogs from Firestore:",e),e}},U=async e=>{var t,n,c,i,d,r,l;try{if(!((t=e.title)!=null&&t.trim()))throw new Error("Blog title is required and cannot be empty");if(!((n=e.content)!=null&&n.trim()))throw new Error("Blog content is required and cannot be empty");if(!((c=e.authorId)!=null&&c.trim()))throw new Error("Author ID is required");if(!((i=e.authorEmail)!=null&&i.trim()))throw new Error("Author email is required");await f();const{collection:s,addDoc:o,serverTimestamp:m}=await b(async()=>{const{collection:y,addDoc:v,serverTimestamp:E}=await import("./index.esm-ehpEbksy.js");return{collection:y,addDoc:v,serverTimestamp:E}},__vite__mapDeps([0,1]));console.log("Creating new blog in Firestore:",e.title);const g=_(e.title),a=B(e.content),u=s(p,w.BLOGS),h={title:e.title.trim(),content:e.content.trim(),tags:Array.isArray(e.tags)?e.tags.filter(y=>y.trim()):[],published:!!e.published,authorId:e.authorId.trim(),authorName:((d=e.authorName)==null?void 0:d.trim())||"Unknown Author",authorEmail:e.authorEmail.trim(),slug:g,excerpt:((r=e.excerpt)==null?void 0:r.trim())||P(e.content),readTime:a,views:0,coverImageUrl:((l=e.coverImageUrl)==null?void 0:l.trim())||null,createdAt:m(),updatedAt:m()};console.log("Blog document to be created:",{title:h.title,authorId:h.authorId,authorEmail:h.authorEmail,published:h.published,tagsCount:h.tags.length});const A=await o(u,h);return console.log(`Blog created successfully with ID: ${A.id}`),A.id}catch(s){throw console.error("Error creating blog:",s),s instanceof Error?s.message.includes("permission-denied")?new Error("Permission denied: You do not have permission to create blogs. Please ensure you are logged in as an admin."):s.message.includes("unavailable")?new Error("Firebase service is currently unavailable. Please try again later."):s.message.includes("network")?new Error("Network error: Please check your internet connection and try again."):s.message.includes("required")?s:new Error(`Failed to create blog: ${s.message}`):new Error("An unknown error occurred while creating the blog. Please try again.")}},C=async(e,t)=>{try{await f();const{doc:n,getDoc:c,updateDoc:i,serverTimestamp:d}=await b(async()=>{const{doc:o,getDoc:m,updateDoc:g,serverTimestamp:a}=await import("./index.esm-ehpEbksy.js");return{doc:o,getDoc:m,updateDoc:g,serverTimestamp:a}},__vite__mapDeps([0,1]));console.log(`Updating blog with ID: ${e}`);const r=n(p,w.BLOGS,e);if(!(await c(r)).exists())throw new Error(`Blog with ID ${e} not found`);const s={updatedAt:d()};t.title!==void 0&&(s.title=t.title,s.slug=_(t.title)),t.content!==void 0&&(s.content=t.content,s.readTime=B(t.content),t.excerpt||(s.excerpt=P(t.content))),t.tags!==void 0&&(s.tags=t.tags),t.published!==void 0&&(s.published=t.published),t.excerpt!==void 0&&(s.excerpt=t.excerpt),t.coverImageUrl!==void 0&&(s.coverImageUrl=t.coverImageUrl),await i(r,s),console.log(`Blog ${e} updated successfully`)}catch(n){throw console.error("Error updating blog:",n),n}},F=async e=>{try{await f();const{doc:t,getDoc:n,deleteDoc:c}=await b(async()=>{const{doc:r,getDoc:l,deleteDoc:s}=await import("./index.esm-ehpEbksy.js");return{doc:r,getDoc:l,deleteDoc:s}},__vite__mapDeps([0,1]));console.log(`Deleting blog with ID: ${e}`);const i=t(p,w.BLOGS,e);if(!(await n(i)).exists())throw new Error(`Blog with ID ${e} not found`);await c(i),console.log(`Blog ${e} deleted successfully`)}catch(t){throw console.error("Error deleting blog:",t),t}},z=async e=>{try{await f();const{doc:t,getDoc:n,updateDoc:c,serverTimestamp:i}=await b(async()=>{const{doc:o,getDoc:m,updateDoc:g,serverTimestamp:a}=await import("./index.esm-ehpEbksy.js");return{doc:o,getDoc:m,updateDoc:g,serverTimestamp:a}},__vite__mapDeps([0,1]));console.log(`Toggling publish status for blog with ID: ${e}`);const d=t(p,w.BLOGS,e),r=await n(d);if(!r.exists())throw new Error(`Blog with ID ${e} not found`);const s=!r.data().published;return await c(d,{published:s,updatedAt:i()}),console.log(`Blog ${e} publish status toggled to: ${s}`),s}catch(t){throw console.error("Error toggling blog publish status:",t),t}},k=async(e,t)=>{var n,c,i,d;try{if(!(e!=null&&e.trim()))throw new Error("Blog ID is required for autosave");await f();const{doc:r,getDoc:l,updateDoc:s,serverTimestamp:o}=await b(async()=>{const{doc:h,getDoc:A,updateDoc:y,serverTimestamp:v}=await import("./index.esm-ehpEbksy.js");return{doc:h,getDoc:A,updateDoc:y,serverTimestamp:v}},__vite__mapDeps([0,1]));console.log(`Autosaving blog draft with ID: ${e}`);const m=r(p,w.BLOGS,e),g=await l(m);if(!g.exists())throw new Error(`Blog with ID ${e} not found for autosave`);if(g.data().published){console.log(`Skipping autosave for published blog ${e}`);return}const u={updatedAt:o(),published:!1};t.title!==void 0&&(u.title=((n=t.title)==null?void 0:n.trim())||"",u.title&&(u.slug=_(u.title))),t.content!==void 0&&(u.content=((c=t.content)==null?void 0:c.trim())||"",u.content&&(u.readTime=B(u.content),t.excerpt||(u.excerpt=P(u.content)))),t.tags!==void 0&&(u.tags=Array.isArray(t.tags)?t.tags.filter(h=>h.trim()):[]),t.excerpt!==void 0&&(u.excerpt=((i=t.excerpt)==null?void 0:i.trim())||""),t.coverImageUrl!==void 0&&(u.coverImageUrl=((d=t.coverImageUrl)==null?void 0:d.trim())||null),console.log(`Autosaving blog ${e} with fields:`,Object.keys(u).filter(h=>h!=="updatedAt")),await s(m,u),console.log(`Blog draft ${e} autosaved successfully`)}catch(r){throw console.error("Error autosaving blog draft:",r),r instanceof Error?r.message.includes("permission-denied")?new Error("Permission denied: Unable to autosave blog draft. Please ensure you are logged in as an admin."):r.message.includes("unavailable")?(console.warn("Firebase unavailable during autosave, will retry later"),new Error("Service temporarily unavailable during autosave")):r.message.includes("network")?(console.warn("Network error during autosave, will retry later"),new Error("Network error during autosave")):r.message.includes("not found")?r:new Error(`Autosave failed: ${r.message}`):new Error("Unknown error occurred during autosave")}},_=e=>e.toLowerCase().trim().replace(/[^\w\s-]/g,"").replace(/[\s_-]+/g,"-").replace(/^-+|-+$/g,""),P=e=>{const t=e.replace(/<[^>]*>/g,"");return t.length>150?t.substring(0,150)+"...":t},B=e=>{const c=e.replace(/<[^>]*>/g,"").split(/\s+/).length;return Math.ceil(c/200)},q=async(e,t,n)=>{try{console.log("Uploading blog cover image:",e.name);const c=new Date().getTime(),i=Math.random().toString(36).substring(2,6),d="webp",r=t?`blog-covers/${t}/${c}-${i}.${d}`:`blog-covers/temp/${c}-${i}.${d}`,l=await T(e,r,n,{maxWidth:1200,maxHeight:800,quality:.85});return console.log("Blog cover image uploaded successfully:",l),l}catch(c){throw console.error("Error uploading blog cover image:",c),c}},G=async e=>{try{console.log("Deleting blog cover image:",e);const t=await $(e);return console.log("Blog cover image deletion result:",t),t}catch(t){return console.error("Error deleting blog cover image:",t),!1}},V=async e=>{var t,n,c,i,d;try{if(!((t=e.title)!=null&&t.trim()))throw new Error("Blog title is required for publishing");if(!((n=e.content)!=null&&n.trim()))throw new Error("Blog content is required for publishing");if(!((c=e.author)!=null&&c.trim()))throw new Error("Author name is required for publishing");await f();const{collection:r,addDoc:l,serverTimestamp:s}=await b(async()=>{const{collection:h,addDoc:A,serverTimestamp:y}=await import("./index.esm-ehpEbksy.js");return{collection:h,addDoc:A,serverTimestamp:y}},__vite__mapDeps([0,1]));console.log("Publishing blog post to blogPosts collection:",e.title);const o=_(e.title),m=B(e.content),g=r(p,w.BLOG_POSTS),a={title:e.title.trim(),content:e.content.trim(),excerpt:((i=e.excerpt)==null?void 0:i.trim())||P(e.content),author:e.author.trim(),tags:Array.isArray(e.tags)?e.tags.filter(h=>h.trim()):[],coverImageUrl:((d=e.coverImageUrl)==null?void 0:d.trim())||null,published:!0,slug:o,readTime:m,createdAt:s(),updatedAt:s()};console.log("Publishing blog post document:",{title:a.title,author:a.author,slug:a.slug,tagsCount:a.tags.length,hasContent:!!a.content,hasExcerpt:!!a.excerpt});const u=await l(g,a);return console.log(`Blog post published successfully with ID: ${u.id}`),u.id}catch(r){throw console.error("Error publishing blog post:",r),r instanceof Error?r.message.includes("permission-denied")?new Error("Permission denied: You do not have permission to publish blog posts. Please ensure you are logged in as an admin."):r.message.includes("unavailable")?new Error("Firebase service is currently unavailable. Please try again later."):r.message.includes("network")?new Error("Network error: Please check your internet connection and try again."):r.message.includes("required")?r:new Error(`Failed to publish blog post: ${r.message}`):new Error("An unknown error occurred while publishing the blog post. Please try again.")}},D=async()=>{try{await f();const{collection:e,query:t,getDocs:n,orderBy:c}=await b(async()=>{const{collection:s,query:o,getDocs:m,orderBy:g}=await import("./index.esm-ehpEbksy.js");return{collection:s,query:o,getDocs:m,orderBy:g}},__vite__mapDeps([0,1]));console.log("Fetching all published blog posts from Firestore");const i=e(p,w.BLOG_POSTS),d=t(i,c("createdAt","desc")),r=await n(d),l=[];return r.forEach(s=>{var u,h;const o=s.data(),m=(u=o.createdAt)!=null&&u.toDate?o.createdAt.toDate():new Date,g=(h=o.updatedAt)!=null&&h.toDate?o.updatedAt.toDate():new Date,a={id:s.id,title:o.title||"",content:o.content||"",excerpt:o.excerpt||"",author:o.author||"Admin",tags:Array.isArray(o.tags)?o.tags:[],coverImageUrl:o.coverImageUrl,published:!0,createdAt:m,updatedAt:g,slug:o.slug||"",readTime:o.readTime||1};l.push(a)}),console.log(`Found ${l.length} published blog posts in Firestore`),l}catch(e){throw console.error("Error getting published blog posts from Firestore:",e),e}},N=async e=>{var t,n;try{await f();const{collection:c,query:i,where:d,getDocs:r}=await b(async()=>{const{collection:A,query:y,where:v,getDocs:E}=await import("./index.esm-ehpEbksy.js");return{collection:A,query:y,where:v,getDocs:E}},__vite__mapDeps([0,1]));console.log(`Fetching published blog post with slug: ${e}`);const l=c(p,w.BLOG_POSTS),s=i(l,d("slug","==",e)),o=await r(s);if(o.empty)return console.log(`No published blog post found with slug: ${e}`),null;const m=o.docs[0],g=m.data(),a=(t=g.createdAt)!=null&&t.toDate?g.createdAt.toDate():new Date,u=(n=g.updatedAt)!=null&&n.toDate?g.updatedAt.toDate():new Date,h={id:m.id,title:g.title||"",content:g.content||"",excerpt:g.excerpt||"",author:g.author||"Admin",tags:Array.isArray(g.tags)?g.tags:[],coverImageUrl:g.coverImageUrl,published:!0,createdAt:a,updatedAt:u,slug:g.slug||"",readTime:g.readTime||1};return console.log(`Found published blog post: ${h.title}`),h}catch(c){throw console.error("Error getting published blog post by slug:",c),c}},j=async e=>{try{const t=await D(),n=t.findIndex(d=>d.slug===e);if(n===-1)return{previous:null,next:null};const c=n>0?t[n-1]:null,i=n<t.length-1?t[n+1]:null;return{previous:c,next:i}}catch(t){return console.error("Error getting adjacent blog posts:",t),{previous:null,next:null}}},Q=async e=>{var t,n;try{await f();const{collection:c,getDocs:i}=await b(async()=>{const{collection:l,getDocs:s}=await import("./index.esm-ehpEbksy.js");return{collection:l,getDocs:s}},__vite__mapDeps([0,1]));console.log(`Searching for blog post with title: ${e}`);const d=c(p,w.BLOG_POSTS),r=await i(d);for(const l of r.docs){const s=l.data();if(s.title&&s.title.toLowerCase().trim()===e.toLowerCase().trim()){const o=(t=s.createdAt)!=null&&t.toDate?s.createdAt.toDate():new Date,m=(n=s.updatedAt)!=null&&n.toDate?s.updatedAt.toDate():new Date,g={id:l.id,title:s.title||"",content:s.content||"",excerpt:s.excerpt||"",author:s.author||"Admin",tags:Array.isArray(s.tags)?s.tags:[],coverImageUrl:s.coverImageUrl,published:!0,createdAt:o,updatedAt:m,slug:s.slug||"",readTime:s.readTime||1};return console.log(`Found blog post by title: ${g.title}`),g}}return console.log(`No blog post found with title: ${e}`),null}catch(c){throw console.error("Error searching blog post by title:",c),c}},J=Object.freeze(Object.defineProperty({__proto__:null,autosaveBlogDraft:k,createBlog:U,deleteBlog:F,deleteBlogCoverImage:G,getAdjacentBlogPosts:j,getAllBlogs:L,getAllPublishedBlogPosts:D,getPublishedBlogPostBySlug:N,publishBlogPost:V,searchBlogPostByTitle:Q,testFirebaseConnection:x,togglePublishStatus:z,updateBlog:C,uploadBlogCoverImage:q},Symbol.toStringTag,{value:"Module"}));export{w as B,N as a,j as b,k as c,G as d,U as e,L as f,D as g,W as h,O as i,R as j,H as k,C as l,z as m,F as n,J as o,V as p,Q as s,x as t,q as u,I as v};
