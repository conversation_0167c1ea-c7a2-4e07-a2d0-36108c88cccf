{"version": 3, "file": "syntax.js", "names": ["Delta", "ClassAttributor", "<PERSON><PERSON>", "Inline", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "blockDelta", "BreakBlot", "CursorBlot", "TextBlot", "escapeText", "CodeBlock", "CodeBlockContainer", "traverse", "TokenAttributor", "scope", "INLINE", "CodeToken", "formats", "node", "scroll", "domNode", "classList", "contains", "className", "parentNode", "undefined", "constructor", "value", "add", "format", "blotName", "remove", "statics", "optimize", "arguments", "unwrap", "SyntaxCodeBlock", "create", "setAttribute", "getAttribute", "register", "name", "replaceWith", "formatAt", "length", "SyntaxCodeBlockContainer", "attach", "forceNext", "emitMount", "children", "for<PERSON>ach", "child", "index", "highlight", "forced", "head", "nodes", "Array", "from", "childNodes", "filter", "uiNode", "text", "map", "textContent", "join", "language", "cachedText", "trim", "<PERSON><PERSON><PERSON><PERSON>", "reduce", "delta", "concat", "diff", "_ref", "retain", "attributes", "Object", "keys", "includes", "html", "codeBlock", "find", "code", "context", "parent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requiredC<PERSON><PERSON>", "lib", "versionString", "majorVersion", "split", "parseInt", "Syntax", "quill", "options", "hljs", "Error", "languages", "memo", "_ref2", "key", "highlightBlot", "bind", "initListener", "initTimer", "on", "events", "SCROLL_BLOT_MOUNT", "blot", "select", "root", "ownerDocument", "createElement", "_ref3", "label", "option", "append<PERSON><PERSON><PERSON>", "addEventListener", "focus", "attachUI", "timer", "SCROLL_OPTIMIZE", "clearTimeout", "setTimeout", "interval", "force", "selection", "composing", "update", "sources", "USER", "range", "getSelection", "blots", "descendants", "container", "SILENT", "setSelection", "line", "i", "insert", "innerHTML", "compose", "data", "nodeText", "WeakMap", "DEFAULTS", "window", "default"], "sources": ["../../src/modules/syntax.ts"], "sourcesContent": ["import Delta from 'quill-delta';\nimport { ClassAttributor, Scope } from 'parchment';\nimport type { Blot, ScrollBlot } from 'parchment';\nimport Inline from '../blots/inline.js';\nimport Quill from '../core/quill.js';\nimport Module from '../core/module.js';\nimport { blockD<PERSON><PERSON> } from '../blots/block.js';\nimport BreakBlot from '../blots/break.js';\nimport CursorBlot from '../blots/cursor.js';\nimport TextBlot, { escapeText } from '../blots/text.js';\nimport CodeBlock, { CodeBlockContainer } from '../formats/code.js';\nimport { traverse } from './clipboard.js';\n\nconst TokenAttributor = new ClassAttributor('code-token', 'hljs', {\n  scope: Scope.INLINE,\n});\nclass CodeToken extends Inline {\n  static formats(node: Element, scroll: ScrollBlot) {\n    while (node != null && node !== scroll.domNode) {\n      if (node.classList && node.classList.contains(CodeBlock.className)) {\n        // @ts-expect-error\n        return super.formats(node, scroll);\n      }\n      // @ts-expect-error\n      node = node.parentNode;\n    }\n    return undefined;\n  }\n\n  constructor(scroll: ScrollBlot, domNode: Node, value: unknown) {\n    // @ts-expect-error\n    super(scroll, domNode, value);\n    TokenAttributor.add(this.domNode, value);\n  }\n\n  format(format: string, value: unknown) {\n    if (format !== CodeToken.blotName) {\n      super.format(format, value);\n    } else if (value) {\n      TokenAttributor.add(this.domNode, value);\n    } else {\n      TokenAttributor.remove(this.domNode);\n      this.domNode.classList.remove(this.statics.className);\n    }\n  }\n\n  optimize(...args: unknown[]) {\n    // @ts-expect-error\n    super.optimize(...args);\n    if (!TokenAttributor.value(this.domNode)) {\n      this.unwrap();\n    }\n  }\n}\nCodeToken.blotName = 'code-token';\nCodeToken.className = 'ql-token';\n\nclass SyntaxCodeBlock extends CodeBlock {\n  static create(value: unknown) {\n    const domNode = super.create(value);\n    if (typeof value === 'string') {\n      domNode.setAttribute('data-language', value);\n    }\n    return domNode;\n  }\n\n  static formats(domNode: Node) {\n    // @ts-expect-error\n    return domNode.getAttribute('data-language') || 'plain';\n  }\n\n  static register() {} // Syntax module will register\n\n  format(name: string, value: unknown) {\n    if (name === this.statics.blotName && value) {\n      // @ts-expect-error\n      this.domNode.setAttribute('data-language', value);\n    } else {\n      super.format(name, value);\n    }\n  }\n\n  replaceWith(name: string | Blot, value?: any) {\n    this.formatAt(0, this.length(), CodeToken.blotName, false);\n    return super.replaceWith(name, value);\n  }\n}\n\nclass SyntaxCodeBlockContainer extends CodeBlockContainer {\n  forceNext?: boolean;\n  cachedText?: string | null;\n\n  attach() {\n    super.attach();\n    this.forceNext = false;\n    // @ts-expect-error\n    this.scroll.emitMount(this);\n  }\n\n  format(name: string, value: unknown) {\n    if (name === SyntaxCodeBlock.blotName) {\n      this.forceNext = true;\n      this.children.forEach((child) => {\n        // @ts-expect-error\n        child.format(name, value);\n      });\n    }\n  }\n\n  formatAt(index: number, length: number, name: string, value: unknown) {\n    if (name === SyntaxCodeBlock.blotName) {\n      this.forceNext = true;\n    }\n    super.formatAt(index, length, name, value);\n  }\n\n  highlight(\n    highlight: (text: string, language: string) => Delta,\n    forced = false,\n  ) {\n    if (this.children.head == null) return;\n    const nodes = Array.from(this.domNode.childNodes).filter(\n      (node) => node !== this.uiNode,\n    );\n    const text = `${nodes.map((node) => node.textContent).join('\\n')}\\n`;\n    const language = SyntaxCodeBlock.formats(this.children.head.domNode);\n    if (forced || this.forceNext || this.cachedText !== text) {\n      if (text.trim().length > 0 || this.cachedText == null) {\n        const oldDelta = this.children.reduce((delta, child) => {\n          // @ts-expect-error\n          return delta.concat(blockDelta(child, false));\n        }, new Delta());\n        const delta = highlight(text, language);\n        oldDelta.diff(delta).reduce((index, { retain, attributes }) => {\n          // Should be all retains\n          if (!retain) return index;\n          if (attributes) {\n            Object.keys(attributes).forEach((format) => {\n              if (\n                [SyntaxCodeBlock.blotName, CodeToken.blotName].includes(format)\n              ) {\n                // @ts-expect-error\n                this.formatAt(index, retain, format, attributes[format]);\n              }\n            });\n          }\n          // @ts-expect-error\n          return index + retain;\n        }, 0);\n      }\n      this.cachedText = text;\n      this.forceNext = false;\n    }\n  }\n\n  html(index: number, length: number) {\n    const [codeBlock] = this.children.find(index);\n    const language = codeBlock\n      ? SyntaxCodeBlock.formats(codeBlock.domNode)\n      : 'plain';\n\n    return `<pre data-language=\"${language}\">\\n${escapeText(\n      this.code(index, length),\n    )}\\n</pre>`;\n  }\n\n  optimize(context: Record<string, any>) {\n    super.optimize(context);\n    if (\n      this.parent != null &&\n      this.children.head != null &&\n      this.uiNode != null\n    ) {\n      const language = SyntaxCodeBlock.formats(this.children.head.domNode);\n      // @ts-expect-error\n      if (language !== this.uiNode.value) {\n        // @ts-expect-error\n        this.uiNode.value = language;\n      }\n    }\n  }\n}\n\nSyntaxCodeBlockContainer.allowedChildren = [SyntaxCodeBlock];\nSyntaxCodeBlock.requiredContainer = SyntaxCodeBlockContainer;\nSyntaxCodeBlock.allowedChildren = [CodeToken, CursorBlot, TextBlot, BreakBlot];\n\ninterface SyntaxOptions {\n  interval: number;\n  languages: { key: string; label: string }[];\n  hljs: any;\n}\n\nconst highlight = (lib: any, language: string, text: string) => {\n  if (typeof lib.versionString === 'string') {\n    const majorVersion = lib.versionString.split('.')[0];\n    if (parseInt(majorVersion, 10) >= 11) {\n      return lib.highlight(text, { language }).value;\n    }\n  }\n  return lib.highlight(language, text).value;\n};\n\nclass Syntax extends Module<SyntaxOptions> {\n  static DEFAULTS: SyntaxOptions & { hljs: any };\n\n  static register() {\n    Quill.register(CodeToken, true);\n    Quill.register(SyntaxCodeBlock, true);\n    Quill.register(SyntaxCodeBlockContainer, true);\n  }\n\n  languages: Record<string, true>;\n\n  constructor(quill: Quill, options: Partial<SyntaxOptions>) {\n    super(quill, options);\n    if (this.options.hljs == null) {\n      throw new Error(\n        'Syntax module requires highlight.js. Please include the library on the page before Quill.',\n      );\n    }\n    // @ts-expect-error Fix me later\n    this.languages = this.options.languages.reduce(\n      (memo: Record<string, unknown>, { key }) => {\n        memo[key] = true;\n        return memo;\n      },\n      {},\n    );\n    this.highlightBlot = this.highlightBlot.bind(this);\n    this.initListener();\n    this.initTimer();\n  }\n\n  initListener() {\n    this.quill.on(Quill.events.SCROLL_BLOT_MOUNT, (blot: Blot) => {\n      if (!(blot instanceof SyntaxCodeBlockContainer)) return;\n      const select = this.quill.root.ownerDocument.createElement('select');\n      // @ts-expect-error Fix me later\n      this.options.languages.forEach(({ key, label }) => {\n        const option = select.ownerDocument.createElement('option');\n        option.textContent = label;\n        option.setAttribute('value', key);\n        select.appendChild(option);\n      });\n      select.addEventListener('change', () => {\n        blot.format(SyntaxCodeBlock.blotName, select.value);\n        this.quill.root.focus(); // Prevent scrolling\n        this.highlight(blot, true);\n      });\n      if (blot.uiNode == null) {\n        blot.attachUI(select);\n        if (blot.children.head) {\n          select.value = SyntaxCodeBlock.formats(blot.children.head.domNode);\n        }\n      }\n    });\n  }\n\n  initTimer() {\n    let timer: ReturnType<typeof setTimeout> | null = null;\n    this.quill.on(Quill.events.SCROLL_OPTIMIZE, () => {\n      if (timer) {\n        clearTimeout(timer);\n      }\n      timer = setTimeout(() => {\n        this.highlight();\n        timer = null;\n      }, this.options.interval);\n    });\n  }\n\n  highlight(blot: SyntaxCodeBlockContainer | null = null, force = false) {\n    if (this.quill.selection.composing) return;\n    this.quill.update(Quill.sources.USER);\n    const range = this.quill.getSelection();\n    const blots =\n      blot == null\n        ? this.quill.scroll.descendants(SyntaxCodeBlockContainer)\n        : [blot];\n    blots.forEach((container) => {\n      container.highlight(this.highlightBlot, force);\n    });\n    this.quill.update(Quill.sources.SILENT);\n    if (range != null) {\n      this.quill.setSelection(range, Quill.sources.SILENT);\n    }\n  }\n\n  highlightBlot(text: string, language = 'plain') {\n    language = this.languages[language] ? language : 'plain';\n    if (language === 'plain') {\n      return escapeText(text)\n        .split('\\n')\n        .reduce((delta, line, i) => {\n          if (i !== 0) {\n            delta.insert('\\n', { [CodeBlock.blotName]: language });\n          }\n          return delta.insert(line);\n        }, new Delta());\n    }\n    const container = this.quill.root.ownerDocument.createElement('div');\n    container.classList.add(CodeBlock.className);\n    container.innerHTML = highlight(this.options.hljs, language, text);\n    return traverse(\n      this.quill.scroll,\n      container,\n      [\n        (node, delta) => {\n          // @ts-expect-error\n          const value = TokenAttributor.value(node);\n          if (value) {\n            return delta.compose(\n              new Delta().retain(delta.length(), {\n                [CodeToken.blotName]: value,\n              }),\n            );\n          }\n          return delta;\n        },\n      ],\n      [\n        (node, delta) => {\n          // @ts-expect-error\n          return node.data.split('\\n').reduce((memo, nodeText, i) => {\n            if (i !== 0) memo.insert('\\n', { [CodeBlock.blotName]: language });\n            return memo.insert(nodeText);\n          }, delta);\n        },\n      ],\n      new WeakMap(),\n    );\n  }\n}\nSyntax.DEFAULTS = {\n  hljs: (() => {\n    return window.hljs;\n  })(),\n  interval: 1000,\n  languages: [\n    { key: 'plain', label: 'Plain' },\n    { key: 'bash', label: 'Bash' },\n    { key: 'cpp', label: 'C++' },\n    { key: 'cs', label: 'C#' },\n    { key: 'css', label: 'CSS' },\n    { key: 'diff', label: 'Diff' },\n    { key: 'xml', label: 'HTML/XML' },\n    { key: 'java', label: 'Java' },\n    { key: 'javascript', label: 'JavaScript' },\n    { key: 'markdown', label: 'Markdown' },\n    { key: 'php', label: 'PHP' },\n    { key: 'python', label: 'Python' },\n    { key: 'ruby', label: 'Ruby' },\n    { key: 'sql', label: 'SQL' },\n  ],\n};\n\nexport { SyntaxCodeBlock as CodeBlock, CodeToken, Syntax as default };\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,SAASC,eAAe,EAAEC,KAAK,QAAQ,WAAW;AAElD,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,QAAQ,IAAIC,UAAU,QAAQ,kBAAkB;AACvD,OAAOC,SAAS,IAAIC,kBAAkB,QAAQ,oBAAoB;AAClE,SAASC,QAAQ,QAAQ,gBAAgB;AAEzC,MAAMC,eAAe,GAAG,IAAIb,eAAe,CAAC,YAAY,EAAE,MAAM,EAAE;EAChEc,KAAK,EAAEb,KAAK,CAACc;AACf,CAAC,CAAC;AACF,MAAMC,SAAS,SAASd,MAAM,CAAC;EAC7B,OAAOe,OAAOA,CAACC,IAAa,EAAEC,MAAkB,EAAE;IAChD,OAAOD,IAAI,IAAI,IAAI,IAAIA,IAAI,KAAKC,MAAM,CAACC,OAAO,EAAE;MAC9C,IAAIF,IAAI,CAACG,SAAS,IAAIH,IAAI,CAACG,SAAS,CAACC,QAAQ,CAACZ,SAAS,CAACa,SAAS,CAAC,EAAE;QAClE;QACA,OAAO,KAAK,CAACN,OAAO,CAACC,IAAI,EAAEC,MAAM,CAAC;MACpC;MACA;MACAD,IAAI,GAAGA,IAAI,CAACM,UAAU;IACxB;IACA,OAAOC,SAAS;EAClB;EAEAC,WAAWA,CAACP,MAAkB,EAAEC,OAAa,EAAEO,KAAc,EAAE;IAC7D;IACA,KAAK,CAACR,MAAM,EAAEC,OAAO,EAAEO,KAAK,CAAC;IAC7Bd,eAAe,CAACe,GAAG,CAAC,IAAI,CAACR,OAAO,EAAEO,KAAK,CAAC;EAC1C;EAEAE,MAAMA,CAACA,MAAc,EAAEF,KAAc,EAAE;IACrC,IAAIE,MAAM,KAAKb,SAAS,CAACc,QAAQ,EAAE;MACjC,KAAK,CAACD,MAAM,CAACA,MAAM,EAAEF,KAAK,CAAC;IAC7B,CAAC,MAAM,IAAIA,KAAK,EAAE;MAChBd,eAAe,CAACe,GAAG,CAAC,IAAI,CAACR,OAAO,EAAEO,KAAK,CAAC;IAC1C,CAAC,MAAM;MACLd,eAAe,CAACkB,MAAM,CAAC,IAAI,CAACX,OAAO,CAAC;MACpC,IAAI,CAACA,OAAO,CAACC,SAAS,CAACU,MAAM,CAAC,IAAI,CAACC,OAAO,CAACT,SAAS,CAAC;IACvD;EACF;EAEAU,QAAQA,CAAA,EAAqB;IAC3B;IACA,KAAK,CAACA,QAAQ,CAAC,GAAAC,SAAO,CAAC;IACvB,IAAI,CAACrB,eAAe,CAACc,KAAK,CAAC,IAAI,CAACP,OAAO,CAAC,EAAE;MACxC,IAAI,CAACe,MAAM,CAAC,CAAC;IACf;EACF;AACF;AACAnB,SAAS,CAACc,QAAQ,GAAG,YAAY;AACjCd,SAAS,CAACO,SAAS,GAAG,UAAU;AAEhC,MAAMa,eAAe,SAAS1B,SAAS,CAAC;EACtC,OAAO2B,MAAMA,CAACV,KAAc,EAAE;IAC5B,MAAMP,OAAO,GAAG,KAAK,CAACiB,MAAM,CAACV,KAAK,CAAC;IACnC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7BP,OAAO,CAACkB,YAAY,CAAC,eAAe,EAAEX,KAAK,CAAC;IAC9C;IACA,OAAOP,OAAO;EAChB;EAEA,OAAOH,OAAOA,CAACG,OAAa,EAAE;IAC5B;IACA,OAAOA,OAAO,CAACmB,YAAY,CAAC,eAAe,CAAC,IAAI,OAAO;EACzD;EAEA,OAAOC,QAAQA,CAAA,EAAG,CAAC,CAAC,CAAC;;EAErBX,MAAMA,CAACY,IAAY,EAAEd,KAAc,EAAE;IACnC,IAAIc,IAAI,KAAK,IAAI,CAACT,OAAO,CAACF,QAAQ,IAAIH,KAAK,EAAE;MAC3C;MACA,IAAI,CAACP,OAAO,CAACkB,YAAY,CAAC,eAAe,EAAEX,KAAK,CAAC;IACnD,CAAC,MAAM;MACL,KAAK,CAACE,MAAM,CAACY,IAAI,EAAEd,KAAK,CAAC;IAC3B;EACF;EAEAe,WAAWA,CAACD,IAAmB,EAAEd,KAAW,EAAE;IAC5C,IAAI,CAACgB,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE5B,SAAS,CAACc,QAAQ,EAAE,KAAK,CAAC;IAC1D,OAAO,KAAK,CAACY,WAAW,CAACD,IAAI,EAAEd,KAAK,CAAC;EACvC;AACF;AAEA,MAAMkB,wBAAwB,SAASlC,kBAAkB,CAAC;EAIxDmC,MAAMA,CAAA,EAAG;IACP,KAAK,CAACA,MAAM,CAAC,CAAC;IACd,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAAC5B,MAAM,CAAC6B,SAAS,CAAC,IAAI,CAAC;EAC7B;EAEAnB,MAAMA,CAACY,IAAY,EAAEd,KAAc,EAAE;IACnC,IAAIc,IAAI,KAAKL,eAAe,CAACN,QAAQ,EAAE;MACrC,IAAI,CAACiB,SAAS,GAAG,IAAI;MACrB,IAAI,CAACE,QAAQ,CAACC,OAAO,CAAEC,KAAK,IAAK;QAC/B;QACAA,KAAK,CAACtB,MAAM,CAACY,IAAI,EAAEd,KAAK,CAAC;MAC3B,CAAC,CAAC;IACJ;EACF;EAEAgB,QAAQA,CAACS,KAAa,EAAER,MAAc,EAAEH,IAAY,EAAEd,KAAc,EAAE;IACpE,IAAIc,IAAI,KAAKL,eAAe,CAACN,QAAQ,EAAE;MACrC,IAAI,CAACiB,SAAS,GAAG,IAAI;IACvB;IACA,KAAK,CAACJ,QAAQ,CAACS,KAAK,EAAER,MAAM,EAAEH,IAAI,EAAEd,KAAK,CAAC;EAC5C;EAEA0B,SAASA,CACPA,SAAoD,EAEpD;IAAA,IADAC,MAAM,GAAApB,SAAA,CAAAU,MAAA,QAAAV,SAAA,QAAAT,SAAA,GAAAS,SAAA,MAAG,KAAK;IAEd,IAAI,IAAI,CAACe,QAAQ,CAACM,IAAI,IAAI,IAAI,EAAE;IAChC,MAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACtC,OAAO,CAACuC,UAAU,CAAC,CAACC,MAAM,CACrD1C,IAAI,IAAKA,IAAI,KAAK,IAAI,CAAC2C,MAC1B,CAAC;IACD,MAAMC,IAAI,GAAI,GAAEN,KAAK,CAACO,GAAG,CAAE7C,IAAI,IAAKA,IAAI,CAAC8C,WAAW,CAAC,CAACC,IAAI,CAAC,IAAI,CAAE,IAAG;IACpE,MAAMC,QAAQ,GAAG9B,eAAe,CAACnB,OAAO,CAAC,IAAI,CAACgC,QAAQ,CAACM,IAAI,CAACnC,OAAO,CAAC;IACpE,IAAIkC,MAAM,IAAI,IAAI,CAACP,SAAS,IAAI,IAAI,CAACoB,UAAU,KAAKL,IAAI,EAAE;MACxD,IAAIA,IAAI,CAACM,IAAI,CAAC,CAAC,CAACxB,MAAM,GAAG,CAAC,IAAI,IAAI,CAACuB,UAAU,IAAI,IAAI,EAAE;QACrD,MAAME,QAAQ,GAAG,IAAI,CAACpB,QAAQ,CAACqB,MAAM,CAAC,CAACC,KAAK,EAAEpB,KAAK,KAAK;UACtD;UACA,OAAOoB,KAAK,CAACC,MAAM,CAACnE,UAAU,CAAC8C,KAAK,EAAE,KAAK,CAAC,CAAC;QAC/C,CAAC,EAAE,IAAIpD,KAAK,CAAC,CAAC,CAAC;QACf,MAAMwE,KAAK,GAAGlB,SAAS,CAACS,IAAI,EAAEI,QAAQ,CAAC;QACvCG,QAAQ,CAACI,IAAI,CAACF,KAAK,CAAC,CAACD,MAAM,CAAC,CAAClB,KAAK,EAAAsB,IAAA,KAA6B;UAAA,IAA3B;YAAEC,MAAM;YAAEC;UAAW,CAAC,GAAAF,IAAA;UACxD;UACA,IAAI,CAACC,MAAM,EAAE,OAAOvB,KAAK;UACzB,IAAIwB,UAAU,EAAE;YACdC,MAAM,CAACC,IAAI,CAACF,UAAU,CAAC,CAAC1B,OAAO,CAAErB,MAAM,IAAK;cAC1C,IACE,CAACO,eAAe,CAACN,QAAQ,EAAEd,SAAS,CAACc,QAAQ,CAAC,CAACiD,QAAQ,CAAClD,MAAM,CAAC,EAC/D;gBACA;gBACA,IAAI,CAACc,QAAQ,CAACS,KAAK,EAAEuB,MAAM,EAAE9C,MAAM,EAAE+C,UAAU,CAAC/C,MAAM,CAAC,CAAC;cAC1D;YACF,CAAC,CAAC;UACJ;UACA;UACA,OAAOuB,KAAK,GAAGuB,MAAM;QACvB,CAAC,EAAE,CAAC,CAAC;MACP;MACA,IAAI,CAACR,UAAU,GAAGL,IAAI;MACtB,IAAI,CAACf,SAAS,GAAG,KAAK;IACxB;EACF;EAEAiC,IAAIA,CAAC5B,KAAa,EAAER,MAAc,EAAE;IAClC,MAAM,CAACqC,SAAS,CAAC,GAAG,IAAI,CAAChC,QAAQ,CAACiC,IAAI,CAAC9B,KAAK,CAAC;IAC7C,MAAMc,QAAQ,GAAGe,SAAS,GACtB7C,eAAe,CAACnB,OAAO,CAACgE,SAAS,CAAC7D,OAAO,CAAC,GAC1C,OAAO;IAEX,OAAQ,uBAAsB8C,QAAS,OAAMzD,UAAU,CACrD,IAAI,CAAC0E,IAAI,CAAC/B,KAAK,EAAER,MAAM,CACzB,CAAE,UAAS;EACb;EAEAX,QAAQA,CAACmD,OAA4B,EAAE;IACrC,KAAK,CAACnD,QAAQ,CAACmD,OAAO,CAAC;IACvB,IACE,IAAI,CAACC,MAAM,IAAI,IAAI,IACnB,IAAI,CAACpC,QAAQ,CAACM,IAAI,IAAI,IAAI,IAC1B,IAAI,CAACM,MAAM,IAAI,IAAI,EACnB;MACA,MAAMK,QAAQ,GAAG9B,eAAe,CAACnB,OAAO,CAAC,IAAI,CAACgC,QAAQ,CAACM,IAAI,CAACnC,OAAO,CAAC;MACpE;MACA,IAAI8C,QAAQ,KAAK,IAAI,CAACL,MAAM,CAAClC,KAAK,EAAE;QAClC;QACA,IAAI,CAACkC,MAAM,CAAClC,KAAK,GAAGuC,QAAQ;MAC9B;IACF;EACF;AACF;AAEArB,wBAAwB,CAACyC,eAAe,GAAG,CAAClD,eAAe,CAAC;AAC5DA,eAAe,CAACmD,iBAAiB,GAAG1C,wBAAwB;AAC5DT,eAAe,CAACkD,eAAe,GAAG,CAACtE,SAAS,EAAET,UAAU,EAAEC,QAAQ,EAAEF,SAAS,CAAC;AAQ9E,MAAM+C,SAAS,GAAGA,CAACmC,GAAQ,EAAEtB,QAAgB,EAAEJ,IAAY,KAAK;EAC9D,IAAI,OAAO0B,GAAG,CAACC,aAAa,KAAK,QAAQ,EAAE;IACzC,MAAMC,YAAY,GAAGF,GAAG,CAACC,aAAa,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACpD,IAAIC,QAAQ,CAACF,YAAY,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE;MACpC,OAAOF,GAAG,CAACnC,SAAS,CAACS,IAAI,EAAE;QAAEI;MAAS,CAAC,CAAC,CAACvC,KAAK;IAChD;EACF;EACA,OAAO6D,GAAG,CAACnC,SAAS,CAACa,QAAQ,EAAEJ,IAAI,CAAC,CAACnC,KAAK;AAC5C,CAAC;AAED,MAAMkE,MAAM,SAASzF,MAAM,CAAgB;EAGzC,OAAOoC,QAAQA,CAAA,EAAG;IAChBrC,KAAK,CAACqC,QAAQ,CAACxB,SAAS,EAAE,IAAI,CAAC;IAC/Bb,KAAK,CAACqC,QAAQ,CAACJ,eAAe,EAAE,IAAI,CAAC;IACrCjC,KAAK,CAACqC,QAAQ,CAACK,wBAAwB,EAAE,IAAI,CAAC;EAChD;EAIAnB,WAAWA,CAACoE,KAAY,EAAEC,OAA+B,EAAE;IACzD,KAAK,CAACD,KAAK,EAAEC,OAAO,CAAC;IACrB,IAAI,IAAI,CAACA,OAAO,CAACC,IAAI,IAAI,IAAI,EAAE;MAC7B,MAAM,IAAIC,KAAK,CACb,2FACF,CAAC;IACH;IACA;IACA,IAAI,CAACC,SAAS,GAAG,IAAI,CAACH,OAAO,CAACG,SAAS,CAAC5B,MAAM,CAC5C,CAAC6B,IAA6B,EAAAC,KAAA,KAAc;MAAA,IAAZ;QAAEC;MAAI,CAAC,GAAAD,KAAA;MACrCD,IAAI,CAACE,GAAG,CAAC,GAAG,IAAI;MAChB,OAAOF,IAAI;IACb,CAAC,EACD,CAAC,CACH,CAAC;IACD,IAAI,CAACG,aAAa,GAAG,IAAI,CAACA,aAAa,CAACC,IAAI,CAAC,IAAI,CAAC;IAClD,IAAI,CAACC,YAAY,CAAC,CAAC;IACnB,IAAI,CAACC,SAAS,CAAC,CAAC;EAClB;EAEAD,YAAYA,CAAA,EAAG;IACb,IAAI,CAACV,KAAK,CAACY,EAAE,CAACvG,KAAK,CAACwG,MAAM,CAACC,iBAAiB,EAAGC,IAAU,IAAK;MAC5D,IAAI,EAAEA,IAAI,YAAYhE,wBAAwB,CAAC,EAAE;MACjD,MAAMiE,MAAM,GAAG,IAAI,CAAChB,KAAK,CAACiB,IAAI,CAACC,aAAa,CAACC,aAAa,CAAC,QAAQ,CAAC;MACpE;MACA,IAAI,CAAClB,OAAO,CAACG,SAAS,CAAChD,OAAO,CAACgE,KAAA,IAAoB;QAAA,IAAnB;UAAEb,GAAG;UAAEc;QAAM,CAAC,GAAAD,KAAA;QAC5C,MAAME,MAAM,GAAGN,MAAM,CAACE,aAAa,CAACC,aAAa,CAAC,QAAQ,CAAC;QAC3DG,MAAM,CAACpD,WAAW,GAAGmD,KAAK;QAC1BC,MAAM,CAAC9E,YAAY,CAAC,OAAO,EAAE+D,GAAG,CAAC;QACjCS,MAAM,CAACO,WAAW,CAACD,MAAM,CAAC;MAC5B,CAAC,CAAC;MACFN,MAAM,CAACQ,gBAAgB,CAAC,QAAQ,EAAE,MAAM;QACtCT,IAAI,CAAChF,MAAM,CAACO,eAAe,CAACN,QAAQ,EAAEgF,MAAM,CAACnF,KAAK,CAAC;QACnD,IAAI,CAACmE,KAAK,CAACiB,IAAI,CAACQ,KAAK,CAAC,CAAC,CAAC,CAAC;QACzB,IAAI,CAAClE,SAAS,CAACwD,IAAI,EAAE,IAAI,CAAC;MAC5B,CAAC,CAAC;MACF,IAAIA,IAAI,CAAChD,MAAM,IAAI,IAAI,EAAE;QACvBgD,IAAI,CAACW,QAAQ,CAACV,MAAM,CAAC;QACrB,IAAID,IAAI,CAAC5D,QAAQ,CAACM,IAAI,EAAE;UACtBuD,MAAM,CAACnF,KAAK,GAAGS,eAAe,CAACnB,OAAO,CAAC4F,IAAI,CAAC5D,QAAQ,CAACM,IAAI,CAACnC,OAAO,CAAC;QACpE;MACF;IACF,CAAC,CAAC;EACJ;EAEAqF,SAASA,CAAA,EAAG;IACV,IAAIgB,KAA2C,GAAG,IAAI;IACtD,IAAI,CAAC3B,KAAK,CAACY,EAAE,CAACvG,KAAK,CAACwG,MAAM,CAACe,eAAe,EAAE,MAAM;MAChD,IAAID,KAAK,EAAE;QACTE,YAAY,CAACF,KAAK,CAAC;MACrB;MACAA,KAAK,GAAGG,UAAU,CAAC,MAAM;QACvB,IAAI,CAACvE,SAAS,CAAC,CAAC;QAChBoE,KAAK,GAAG,IAAI;MACd,CAAC,EAAE,IAAI,CAAC1B,OAAO,CAAC8B,QAAQ,CAAC;IAC3B,CAAC,CAAC;EACJ;EAEAxE,SAASA,CAAA,EAA8D;IAAA,IAA7DwD,IAAqC,GAAA3E,SAAA,CAAAU,MAAA,QAAAV,SAAA,QAAAT,SAAA,GAAAS,SAAA,MAAG,IAAI;IAAA,IAAE4F,KAAK,GAAA5F,SAAA,CAAAU,MAAA,QAAAV,SAAA,QAAAT,SAAA,GAAAS,SAAA,MAAG,KAAK;IACnE,IAAI,IAAI,CAAC4D,KAAK,CAACiC,SAAS,CAACC,SAAS,EAAE;IACpC,IAAI,CAAClC,KAAK,CAACmC,MAAM,CAAC9H,KAAK,CAAC+H,OAAO,CAACC,IAAI,CAAC;IACrC,MAAMC,KAAK,GAAG,IAAI,CAACtC,KAAK,CAACuC,YAAY,CAAC,CAAC;IACvC,MAAMC,KAAK,GACTzB,IAAI,IAAI,IAAI,GACR,IAAI,CAACf,KAAK,CAAC3E,MAAM,CAACoH,WAAW,CAAC1F,wBAAwB,CAAC,GACvD,CAACgE,IAAI,CAAC;IACZyB,KAAK,CAACpF,OAAO,CAAEsF,SAAS,IAAK;MAC3BA,SAAS,CAACnF,SAAS,CAAC,IAAI,CAACiD,aAAa,EAAEwB,KAAK,CAAC;IAChD,CAAC,CAAC;IACF,IAAI,CAAChC,KAAK,CAACmC,MAAM,CAAC9H,KAAK,CAAC+H,OAAO,CAACO,MAAM,CAAC;IACvC,IAAIL,KAAK,IAAI,IAAI,EAAE;MACjB,IAAI,CAACtC,KAAK,CAAC4C,YAAY,CAACN,KAAK,EAAEjI,KAAK,CAAC+H,OAAO,CAACO,MAAM,CAAC;IACtD;EACF;EAEAnC,aAAaA,CAACxC,IAAY,EAAsB;IAAA,IAApBI,QAAQ,GAAAhC,SAAA,CAAAU,MAAA,QAAAV,SAAA,QAAAT,SAAA,GAAAS,SAAA,MAAG,OAAO;IAC5CgC,QAAQ,GAAG,IAAI,CAACgC,SAAS,CAAChC,QAAQ,CAAC,GAAGA,QAAQ,GAAG,OAAO;IACxD,IAAIA,QAAQ,KAAK,OAAO,EAAE;MACxB,OAAOzD,UAAU,CAACqD,IAAI,CAAC,CACpB6B,KAAK,CAAC,IAAI,CAAC,CACXrB,MAAM,CAAC,CAACC,KAAK,EAAEoE,IAAI,EAAEC,CAAC,KAAK;QAC1B,IAAIA,CAAC,KAAK,CAAC,EAAE;UACXrE,KAAK,CAACsE,MAAM,CAAC,IAAI,EAAE;YAAE,CAACnI,SAAS,CAACoB,QAAQ,GAAGoC;UAAS,CAAC,CAAC;QACxD;QACA,OAAOK,KAAK,CAACsE,MAAM,CAACF,IAAI,CAAC;MAC3B,CAAC,EAAE,IAAI5I,KAAK,CAAC,CAAC,CAAC;IACnB;IACA,MAAMyI,SAAS,GAAG,IAAI,CAAC1C,KAAK,CAACiB,IAAI,CAACC,aAAa,CAACC,aAAa,CAAC,KAAK,CAAC;IACpEuB,SAAS,CAACnH,SAAS,CAACO,GAAG,CAAClB,SAAS,CAACa,SAAS,CAAC;IAC5CiH,SAAS,CAACM,SAAS,GAAGzF,SAAS,CAAC,IAAI,CAAC0C,OAAO,CAACC,IAAI,EAAE9B,QAAQ,EAAEJ,IAAI,CAAC;IAClE,OAAOlD,QAAQ,CACb,IAAI,CAACkF,KAAK,CAAC3E,MAAM,EACjBqH,SAAS,EACT,CACE,CAACtH,IAAI,EAAEqD,KAAK,KAAK;MACf;MACA,MAAM5C,KAAK,GAAGd,eAAe,CAACc,KAAK,CAACT,IAAI,CAAC;MACzC,IAAIS,KAAK,EAAE;QACT,OAAO4C,KAAK,CAACwE,OAAO,CAClB,IAAIhJ,KAAK,CAAC,CAAC,CAAC4E,MAAM,CAACJ,KAAK,CAAC3B,MAAM,CAAC,CAAC,EAAE;UACjC,CAAC5B,SAAS,CAACc,QAAQ,GAAGH;QACxB,CAAC,CACH,CAAC;MACH;MACA,OAAO4C,KAAK;IACd,CAAC,CACF,EACD,CACE,CAACrD,IAAI,EAAEqD,KAAK,KAAK;MACf;MACA,OAAOrD,IAAI,CAAC8H,IAAI,CAACrD,KAAK,CAAC,IAAI,CAAC,CAACrB,MAAM,CAAC,CAAC6B,IAAI,EAAE8C,QAAQ,EAAEL,CAAC,KAAK;QACzD,IAAIA,CAAC,KAAK,CAAC,EAAEzC,IAAI,CAAC0C,MAAM,CAAC,IAAI,EAAE;UAAE,CAACnI,SAAS,CAACoB,QAAQ,GAAGoC;QAAS,CAAC,CAAC;QAClE,OAAOiC,IAAI,CAAC0C,MAAM,CAACI,QAAQ,CAAC;MAC9B,CAAC,EAAE1E,KAAK,CAAC;IACX,CAAC,CACF,EACD,IAAI2E,OAAO,CAAC,CACd,CAAC;EACH;AACF;AACArD,MAAM,CAACsD,QAAQ,GAAG;EAChBnD,IAAI,EAAE,CAAC,MAAM;IACX,OAAOoD,MAAM,CAACpD,IAAI;EACpB,CAAC,EAAE,CAAC;EACJ6B,QAAQ,EAAE,IAAI;EACd3B,SAAS,EAAE,CACT;IAAEG,GAAG,EAAE,OAAO;IAAEc,KAAK,EAAE;EAAQ,CAAC,EAChC;IAAEd,GAAG,EAAE,MAAM;IAAEc,KAAK,EAAE;EAAO,CAAC,EAC9B;IAAEd,GAAG,EAAE,KAAK;IAAEc,KAAK,EAAE;EAAM,CAAC,EAC5B;IAAEd,GAAG,EAAE,IAAI;IAAEc,KAAK,EAAE;EAAK,CAAC,EAC1B;IAAEd,GAAG,EAAE,KAAK;IAAEc,KAAK,EAAE;EAAM,CAAC,EAC5B;IAAEd,GAAG,EAAE,MAAM;IAAEc,KAAK,EAAE;EAAO,CAAC,EAC9B;IAAEd,GAAG,EAAE,KAAK;IAAEc,KAAK,EAAE;EAAW,CAAC,EACjC;IAAEd,GAAG,EAAE,MAAM;IAAEc,KAAK,EAAE;EAAO,CAAC,EAC9B;IAAEd,GAAG,EAAE,YAAY;IAAEc,KAAK,EAAE;EAAa,CAAC,EAC1C;IAAEd,GAAG,EAAE,UAAU;IAAEc,KAAK,EAAE;EAAW,CAAC,EACtC;IAAEd,GAAG,EAAE,KAAK;IAAEc,KAAK,EAAE;EAAM,CAAC,EAC5B;IAAEd,GAAG,EAAE,QAAQ;IAAEc,KAAK,EAAE;EAAS,CAAC,EAClC;IAAEd,GAAG,EAAE,MAAM;IAAEc,KAAK,EAAE;EAAO,CAAC,EAC9B;IAAEd,GAAG,EAAE,KAAK;IAAEc,KAAK,EAAE;EAAM,CAAC;AAEhC,CAAC;AAED,SAAS/E,eAAe,IAAI1B,SAAS,EAAEM,SAAS,EAAE6E,MAAM,IAAIwD,OAAO", "ignoreList": []}