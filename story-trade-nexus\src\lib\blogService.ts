import { initializeFirebase, db } from './firebase';
import { Blog, BlogStatus, CreateBlogData, UpdateBlogData, PublishedBlogPost, CreatePublishedBlogData } from '@/types';
import { uploadImage, deleteImage } from './storageService';
import { BLOG_COLLECTIONS, initializeBlogCollections, verifyBlogCollectionAccess } from './blogCollections';

/**
 * Test Firebase connection and permissions for blog management
 * @returns Promise<{success: boolean, message: string, details?: any}>
 */
export const testFirebaseConnection = async (): Promise<{success: boolean, message: string, details?: any}> => {
  try {
    console.log('🔍 Testing Firebase connection for blog management...');

    // Initialize Firebase
    await initializeFirebase();

    const testResults: any = {
      firebaseInitialized: false,
      collectionsInitialized: false,
      collectionsAccessible: false,
      blogsCollectionAccess: false,
      blogPostsCollectionAccess: false,
      readPermissions: false,
      initializationDetails: null,
      accessDetails: null
    };

    // Test 1: Firebase initialization
    if (db) {
      testResults.firebaseInitialized = true;
      console.log('✅ Firebase initialized successfully');
    } else {
      throw new Error('Firebase database not initialized');
    }

    // Test 2: Initialize blog collections
    console.log('🔧 Initializing blog collections...');
    const initResult = await initializeBlogCollections();
    testResults.collectionsInitialized = initResult.success;
    testResults.initializationDetails = initResult;

    if (initResult.success) {
      console.log('✅ Blog collections initialized successfully');
    } else {
      console.error('❌ Blog collections initialization failed:', initResult.message);
    }

    // Test 3: Verify collection access
    console.log('🔍 Verifying collection access...');
    const accessResult = await verifyBlogCollectionAccess();
    testResults.collectionsAccessible = accessResult.success;
    testResults.accessDetails = accessResult;

    if (accessResult.success) {
      testResults.blogsCollectionAccess = accessResult.details?.blogsReadAccess || false;
      testResults.blogPostsCollectionAccess = accessResult.details?.blogPostsReadAccess || false;
      testResults.readPermissions = accessResult.details?.blogsReadAccess && accessResult.details?.blogPostsReadAccess;
      console.log('✅ Collection access verified successfully');
    } else {
      console.error('❌ Collection access verification failed:', accessResult.message);
    }

    const allTestsPassed = testResults.firebaseInitialized &&
                          testResults.collectionsInitialized &&
                          testResults.collectionsAccessible &&
                          testResults.readPermissions;

    if (allTestsPassed) {
      return {
        success: true,
        message: 'All Firebase connection and collection tests passed successfully',
        details: testResults
      };
    } else {
      return {
        success: false,
        message: 'Some Firebase connection or collection tests failed',
        details: testResults
      };
    }

  } catch (error) {
    console.error('❌ Firebase connection test failed:', error);
    return {
      success: false,
      message: `Firebase connection test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      details: { error: error instanceof Error ? error.message : String(error) }
    };
  }
};

/**
 * Gets all blogs from Firestore
 * @returns Promise<Blog[]> - Array of blogs sorted by creation date (newest first)
 */
export const getAllBlogs = async (): Promise<Blog[]> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { collection, query, getDocs, orderBy } = await import('firebase/firestore');

    console.log('Fetching all blogs from Firestore');

    // Create a reference to the blogs collection
    const blogsRef = collection(db, BLOG_COLLECTIONS.BLOGS);

    // Create a query to get all blogs ordered by creation date (newest first)
    const blogsQuery = query(blogsRef, orderBy('createdAt', 'desc'));

    // Execute the query
    const querySnapshot = await getDocs(blogsQuery);

    // Map the query results to Blog objects
    const blogs: Blog[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();

      // Convert Firestore timestamps to Date objects
      const createdAt = data.createdAt?.toDate ? data.createdAt.toDate() : new Date();
      const updatedAt = data.updatedAt?.toDate ? data.updatedAt.toDate() : new Date();

      // Create a Blog object from the document data
      const blog: Blog = {
        id: doc.id,
        title: data.title || '',
        content: data.content || '',
        tags: Array.isArray(data.tags) ? data.tags : [],
        published: data.published || false,
        status: data.published ? BlogStatus.Published : BlogStatus.Draft,
        createdAt: createdAt,
        updatedAt: updatedAt,
        authorId: data.authorId || '',
        authorName: data.authorName || '',
        authorEmail: data.authorEmail || '',
        slug: data.slug,
        excerpt: data.excerpt,
        readTime: data.readTime,
        views: data.views || 0,
        coverImageUrl: data.coverImageUrl
      };

      blogs.push(blog);
    });

    console.log(`Found ${blogs.length} blogs in Firestore`);
    return blogs;
  } catch (error) {
    console.error('Error getting blogs from Firestore:', error);
    throw error;
  }
};

/**
 * Gets a blog by ID from Firestore
 * @param blogId - The ID of the blog to fetch
 * @returns Promise<Blog | null> - The blog or null if not found
 */
export const getBlog = async (blogId: string): Promise<Blog | null> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { doc, getDoc } = await import('firebase/firestore');

    console.log(`Fetching blog with ID: ${blogId} from Firestore`);

    // Get the blog document from Firestore
    const blogRef = doc(db, BLOG_COLLECTIONS.BLOGS, blogId);
    const blogSnapshot = await getDoc(blogRef);

    if (!blogSnapshot.exists()) {
      console.log(`No blog found with ID: ${blogId} in Firestore`);
      return null;
    }

    // Get the blog data
    const data = blogSnapshot.data();

    // Convert Firestore timestamps to Date objects
    const createdAt = data.createdAt?.toDate ? data.createdAt.toDate() : new Date();
    const updatedAt = data.updatedAt?.toDate ? data.updatedAt.toDate() : new Date();

    // Create a Blog object from the document data
    const blog: Blog = {
      id: blogSnapshot.id,
      title: data.title || '',
      content: data.content || '',
      tags: Array.isArray(data.tags) ? data.tags : [],
      published: data.published || false,
      status: data.published ? BlogStatus.Published : BlogStatus.Draft,
      createdAt: createdAt,
      updatedAt: updatedAt,
      authorId: data.authorId || '',
      authorName: data.authorName || '',
      authorEmail: data.authorEmail || '',
      slug: data.slug,
      excerpt: data.excerpt,
      readTime: data.readTime,
      views: data.views || 0,
      coverImageUrl: data.coverImageUrl
    };

    console.log(`Found blog in Firestore: ${blog.title}`);
    return blog;
  } catch (error) {
    console.error('Error getting blog from Firestore:', error);
    throw error;
  }
};

/**
 * Creates a new blog in Firestore
 * @param blogData - The blog data to create
 * @returns Promise<string> - The ID of the created blog
 */
export const createBlog = async (blogData: CreateBlogData): Promise<string> => {
  try {
    // Validate required fields
    if (!blogData.title?.trim()) {
      throw new Error('Blog title is required and cannot be empty');
    }
    if (!blogData.content?.trim()) {
      throw new Error('Blog content is required and cannot be empty');
    }
    if (!blogData.authorId?.trim()) {
      throw new Error('Author ID is required');
    }
    if (!blogData.authorEmail?.trim()) {
      throw new Error('Author email is required');
    }

    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');

    console.log('Creating new blog in Firestore:', blogData.title);

    // Generate slug from title
    const slug = generateSlug(blogData.title);

    // Estimate reading time (average 200 words per minute)
    const readTime = estimateReadingTime(blogData.content);

    // Create a reference to the blogs collection
    const blogsRef = collection(db, BLOG_COLLECTIONS.BLOGS);

    // Prepare the blog document data
    const blogDocument = {
      title: blogData.title.trim(),
      content: blogData.content.trim(),
      tags: Array.isArray(blogData.tags) ? blogData.tags.filter(tag => tag.trim()) : [],
      published: Boolean(blogData.published),
      authorId: blogData.authorId.trim(),
      authorName: blogData.authorName?.trim() || 'Unknown Author',
      authorEmail: blogData.authorEmail.trim(),
      slug: slug,
      excerpt: blogData.excerpt?.trim() || generateExcerpt(blogData.content),
      readTime: readTime,
      views: 0,
      coverImageUrl: blogData.coverImageUrl?.trim() || null,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    console.log('Blog document to be created:', {
      title: blogDocument.title,
      authorId: blogDocument.authorId,
      authorEmail: blogDocument.authorEmail,
      published: blogDocument.published,
      tagsCount: blogDocument.tags.length
    });

    // Add the blog to Firestore
    const docRef = await addDoc(blogsRef, blogDocument);

    console.log(`Blog created successfully with ID: ${docRef.id}`);
    return docRef.id;
  } catch (error) {
    console.error('Error creating blog:', error);

    // Enhanced error handling with specific error types
    if (error instanceof Error) {
      if (error.message.includes('permission-denied')) {
        throw new Error('Permission denied: You do not have permission to create blogs. Please ensure you are logged in as an admin.');
      } else if (error.message.includes('unavailable')) {
        throw new Error('Firebase service is currently unavailable. Please try again later.');
      } else if (error.message.includes('network')) {
        throw new Error('Network error: Please check your internet connection and try again.');
      } else if (error.message.includes('required')) {
        throw error; // Re-throw validation errors as-is
      } else {
        throw new Error(`Failed to create blog: ${error.message}`);
      }
    } else {
      throw new Error('An unknown error occurred while creating the blog. Please try again.');
    }
  }
};

/**
 * Updates a blog in Firestore
 * @param blogId - The ID of the blog to update
 * @param blogData - The blog data to update
 * @returns Promise<void>
 */
export const updateBlog = async (blogId: string, blogData: UpdateBlogData): Promise<void> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { doc, getDoc, updateDoc, serverTimestamp } = await import('firebase/firestore');

    console.log(`Updating blog with ID: ${blogId}`);

    const blogRef = doc(db, BLOG_COLLECTIONS.BLOGS, blogId);

    // Check if the blog exists
    const blogSnapshot = await getDoc(blogRef);
    if (!blogSnapshot.exists()) {
      throw new Error(`Blog with ID ${blogId} not found`);
    }

    // Prepare update data
    const updateData: Record<string, any> = {
      updatedAt: serverTimestamp()
    };

    // Add fields that are being updated
    if (blogData.title !== undefined) {
      updateData.title = blogData.title;
      updateData.slug = generateSlug(blogData.title);
    }

    if (blogData.content !== undefined) {
      updateData.content = blogData.content;
      updateData.readTime = estimateReadingTime(blogData.content);
      if (!blogData.excerpt) {
        updateData.excerpt = generateExcerpt(blogData.content);
      }
    }

    if (blogData.tags !== undefined) {
      updateData.tags = blogData.tags;
    }

    if (blogData.published !== undefined) {
      updateData.published = blogData.published;
    }

    if (blogData.excerpt !== undefined) {
      updateData.excerpt = blogData.excerpt;
    }

    if (blogData.coverImageUrl !== undefined) {
      updateData.coverImageUrl = blogData.coverImageUrl;
    }

    // Perform the update
    await updateDoc(blogRef, updateData);

    console.log(`Blog ${blogId} updated successfully`);
  } catch (error) {
    console.error('Error updating blog:', error);
    throw error;
  }
};

/**
 * Deletes a blog from Firestore
 * @param blogId - The ID of the blog to delete
 * @returns Promise<void>
 */
export const deleteBlog = async (blogId: string): Promise<void> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { doc, getDoc, deleteDoc } = await import('firebase/firestore');

    console.log(`Deleting blog with ID: ${blogId}`);

    const blogRef = doc(db, BLOG_COLLECTIONS.BLOGS, blogId);

    // Check if the blog exists
    const blogSnapshot = await getDoc(blogRef);
    if (!blogSnapshot.exists()) {
      throw new Error(`Blog with ID ${blogId} not found`);
    }

    // Delete the blog
    await deleteDoc(blogRef);

    console.log(`Blog ${blogId} deleted successfully`);
  } catch (error) {
    console.error('Error deleting blog:', error);
    throw error;
  }
};

/**
 * Toggles the published status of a blog
 * @param blogId - The ID of the blog to toggle
 * @returns Promise<boolean> - The new published status
 */
export const togglePublishStatus = async (blogId: string): Promise<boolean> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { doc, getDoc, updateDoc, serverTimestamp } = await import('firebase/firestore');

    console.log(`Toggling publish status for blog with ID: ${blogId}`);

    const blogRef = doc(db, BLOG_COLLECTIONS.BLOGS, blogId);

    // Get current blog data
    const blogSnapshot = await getDoc(blogRef);
    if (!blogSnapshot.exists()) {
      throw new Error(`Blog with ID ${blogId} not found`);
    }

    const currentData = blogSnapshot.data();
    const newPublishedStatus = !currentData.published;

    // Update the published status
    await updateDoc(blogRef, {
      published: newPublishedStatus,
      updatedAt: serverTimestamp()
    });

    console.log(`Blog ${blogId} publish status toggled to: ${newPublishedStatus}`);
    return newPublishedStatus;
  } catch (error) {
    console.error('Error toggling blog publish status:', error);
    throw error;
  }
};

/**
 * Autosaves a blog draft to Firestore (only for unpublished blogs)
 * @param blogId - The ID of the blog to autosave
 * @param blogData - The blog data to autosave
 * @returns Promise<void>
 */
export const autosaveBlogDraft = async (blogId: string, blogData: UpdateBlogData): Promise<void> => {
  try {
    // Validate inputs
    if (!blogId?.trim()) {
      throw new Error('Blog ID is required for autosave');
    }

    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { doc, getDoc, updateDoc, serverTimestamp } = await import('firebase/firestore');

    console.log(`Autosaving blog draft with ID: ${blogId}`);

    const blogRef = doc(db, BLOG_COLLECTIONS.BLOGS, blogId);

    // Check if the blog exists
    const blogSnapshot = await getDoc(blogRef);
    if (!blogSnapshot.exists()) {
      throw new Error(`Blog with ID ${blogId} not found for autosave`);
    }

    const currentData = blogSnapshot.data();

    // Only autosave if the blog is not published
    if (currentData.published) {
      console.log(`Skipping autosave for published blog ${blogId}`);
      return;
    }

    // Prepare update data for autosave
    const updateData: Record<string, any> = {
      updatedAt: serverTimestamp(),
      published: false // Ensure it stays as draft during autosave
    };

    // Add fields that are being updated (with validation)
    if (blogData.title !== undefined) {
      updateData.title = blogData.title?.trim() || '';
      if (updateData.title) {
        updateData.slug = generateSlug(updateData.title);
      }
    }

    if (blogData.content !== undefined) {
      updateData.content = blogData.content?.trim() || '';
      if (updateData.content) {
        updateData.readTime = estimateReadingTime(updateData.content);
        if (!blogData.excerpt) {
          updateData.excerpt = generateExcerpt(updateData.content);
        }
      }
    }

    if (blogData.tags !== undefined) {
      updateData.tags = Array.isArray(blogData.tags) ? blogData.tags.filter(tag => tag.trim()) : [];
    }

    if (blogData.excerpt !== undefined) {
      updateData.excerpt = blogData.excerpt?.trim() || '';
    }

    if (blogData.coverImageUrl !== undefined) {
      updateData.coverImageUrl = blogData.coverImageUrl?.trim() || null;
    }

    console.log(`Autosaving blog ${blogId} with fields:`, Object.keys(updateData).filter(key => key !== 'updatedAt'));

    // Perform the autosave update
    await updateDoc(blogRef, updateData);

    console.log(`Blog draft ${blogId} autosaved successfully`);
  } catch (error) {
    console.error('Error autosaving blog draft:', error);

    // Enhanced error handling for autosave
    if (error instanceof Error) {
      if (error.message.includes('permission-denied')) {
        throw new Error('Permission denied: Unable to autosave blog draft. Please ensure you are logged in as an admin.');
      } else if (error.message.includes('unavailable')) {
        console.warn('Firebase unavailable during autosave, will retry later');
        throw new Error('Service temporarily unavailable during autosave');
      } else if (error.message.includes('network')) {
        console.warn('Network error during autosave, will retry later');
        throw new Error('Network error during autosave');
      } else if (error.message.includes('not found')) {
        throw error; // Re-throw "not found" errors as-is
      } else {
        throw new Error(`Autosave failed: ${error.message}`);
      }
    } else {
      throw new Error('Unknown error occurred during autosave');
    }
  }
};

// Helper functions

/**
 * Generates a URL-friendly slug from a title
 * @param title - The title to convert to a slug
 * @returns string - The generated slug
 */
const generateSlug = (title: string): string => {
  return title
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
};

/**
 * Generates an excerpt from content
 * @param content - The content to generate excerpt from
 * @returns string - The generated excerpt
 */
const generateExcerpt = (content: string): string => {
  // Remove HTML tags and get first 150 characters
  const plainText = content.replace(/<[^>]*>/g, '');
  return plainText.length > 150 ? plainText.substring(0, 150) + '...' : plainText;
};

/**
 * Estimates reading time based on content length
 * @param content - The content to analyze
 * @returns number - Estimated reading time in minutes
 */
const estimateReadingTime = (content: string): number => {
  const wordsPerMinute = 200;
  const plainText = content.replace(/<[^>]*>/g, '');
  const wordCount = plainText.split(/\s+/).length;
  return Math.ceil(wordCount / wordsPerMinute);
};

/**
 * Uploads a blog cover image to Firebase Storage
 * @param imageFile - The image file to upload
 * @param blogId - The blog ID for path generation (optional, generates unique path if not provided)
 * @param onProgress - Optional callback for tracking upload progress
 * @returns Promise<string> - The download URL of the uploaded image
 */
export const uploadBlogCoverImage = async (
  imageFile: File,
  blogId?: string,
  onProgress?: (progress: number) => void
): Promise<string> => {
  try {
    console.log('Uploading blog cover image:', imageFile.name);

    // Generate a unique path for the blog cover image
    const timestamp = new Date().getTime();
    const randomString = Math.random().toString(36).substring(2, 6);
    const fileExtension = 'webp'; // Use WebP for optimization

    const path = blogId
      ? `blog-covers/${blogId}/${timestamp}-${randomString}.${fileExtension}`
      : `blog-covers/temp/${timestamp}-${randomString}.${fileExtension}`;

    // Upload the image with optimization
    const downloadURL = await uploadImage(imageFile, path, onProgress, {
      maxWidth: 1200,
      maxHeight: 800,
      quality: 0.85
    });

    console.log('Blog cover image uploaded successfully:', downloadURL);
    return downloadURL;
  } catch (error) {
    console.error('Error uploading blog cover image:', error);
    throw error;
  }
};

/**
 * Deletes a blog cover image from Firebase Storage
 * @param imageUrl - The URL of the image to delete
 * @returns Promise<boolean> - True if deletion was successful
 */
export const deleteBlogCoverImage = async (imageUrl: string): Promise<boolean> => {
  try {
    console.log('Deleting blog cover image:', imageUrl);
    const success = await deleteImage(imageUrl);
    console.log('Blog cover image deletion result:', success);
    return success;
  } catch (error) {
    console.error('Error deleting blog cover image:', error);
    return false;
  }
};

/**
 * Publishes a blog post to the blogPosts collection (for public-facing blog posts)
 * @param blogData - The blog data to publish
 * @returns Promise<string> - The ID of the published blog post
 */
export const publishBlogPost = async (blogData: CreatePublishedBlogData): Promise<string> => {
  try {
    // Validate required fields for publishing
    if (!blogData.title?.trim()) {
      throw new Error('Blog title is required for publishing');
    }
    if (!blogData.content?.trim()) {
      throw new Error('Blog content is required for publishing');
    }
    if (!blogData.author?.trim()) {
      throw new Error('Author name is required for publishing');
    }

    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');

    console.log('Publishing blog post to blogPosts collection:', blogData.title);

    // Generate slug from title
    const slug = generateSlug(blogData.title);

    // Estimate reading time (average 200 words per minute)
    const readTime = estimateReadingTime(blogData.content);

    // Create a reference to the blogPosts collection
    const blogPostsRef = collection(db, BLOG_COLLECTIONS.BLOG_POSTS);

    // Prepare the published blog post document data
    const publishedBlogDocument = {
      title: blogData.title.trim(),
      content: blogData.content.trim(),
      excerpt: blogData.excerpt?.trim() || generateExcerpt(blogData.content),
      author: blogData.author.trim(),
      tags: Array.isArray(blogData.tags) ? blogData.tags.filter(tag => tag.trim()) : [],
      coverImageUrl: blogData.coverImageUrl?.trim() || null,
      published: true, // Always true for blogPosts collection
      slug: slug,
      readTime: readTime,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    console.log('Publishing blog post document:', {
      title: publishedBlogDocument.title,
      author: publishedBlogDocument.author,
      slug: publishedBlogDocument.slug,
      tagsCount: publishedBlogDocument.tags.length,
      hasContent: !!publishedBlogDocument.content,
      hasExcerpt: !!publishedBlogDocument.excerpt
    });

    // Add the published blog post to Firestore
    const docRef = await addDoc(blogPostsRef, publishedBlogDocument);

    console.log(`Blog post published successfully with ID: ${docRef.id}`);
    return docRef.id;
  } catch (error) {
    console.error('Error publishing blog post:', error);

    // Enhanced error handling for publishing
    if (error instanceof Error) {
      if (error.message.includes('permission-denied')) {
        throw new Error('Permission denied: You do not have permission to publish blog posts. Please ensure you are logged in as an admin.');
      } else if (error.message.includes('unavailable')) {
        throw new Error('Firebase service is currently unavailable. Please try again later.');
      } else if (error.message.includes('network')) {
        throw new Error('Network error: Please check your internet connection and try again.');
      } else if (error.message.includes('required')) {
        throw error; // Re-throw validation errors as-is
      } else {
        throw new Error(`Failed to publish blog post: ${error.message}`);
      }
    } else {
      throw new Error('An unknown error occurred while publishing the blog post. Please try again.');
    }
  }
};

/**
 * Gets all published blog posts from the blogPosts collection
 * @returns Promise<PublishedBlogPost[]> - Array of published blog posts sorted by creation date (newest first)
 */
export const getAllPublishedBlogPosts = async (): Promise<PublishedBlogPost[]> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { collection, query, getDocs, orderBy } = await import('firebase/firestore');

    console.log('Fetching all published blog posts from Firestore');

    // Create a reference to the blogPosts collection
    const blogPostsRef = collection(db, BLOG_COLLECTIONS.BLOG_POSTS);

    // Create a query to get all published blog posts ordered by creation date (newest first)
    const blogPostsQuery = query(blogPostsRef, orderBy('createdAt', 'desc'));

    // Execute the query
    const querySnapshot = await getDocs(blogPostsQuery);

    // Map the query results to PublishedBlogPost objects
    const blogPosts: PublishedBlogPost[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();

      // Convert Firestore timestamps to Date objects
      const createdAt = data.createdAt?.toDate ? data.createdAt.toDate() : new Date();
      const updatedAt = data.updatedAt?.toDate ? data.updatedAt.toDate() : new Date();

      // Create a PublishedBlogPost object from the document data
      const blogPost: PublishedBlogPost = {
        id: doc.id,
        title: data.title || '',
        content: data.content || '',
        excerpt: data.excerpt || '',
        author: data.author || 'Admin',
        tags: Array.isArray(data.tags) ? data.tags : [],
        coverImageUrl: data.coverImageUrl,
        published: true, // Always true for blogPosts collection
        createdAt: createdAt,
        updatedAt: updatedAt,
        slug: data.slug || '',
        readTime: data.readTime || 1
      };

      blogPosts.push(blogPost);
    });

    console.log(`Found ${blogPosts.length} published blog posts in Firestore`);
    return blogPosts;
  } catch (error) {
    console.error('Error getting published blog posts from Firestore:', error);
    throw error;
  }
};

/**
 * Generates a SEO-friendly slug from a title
 * @param title - The title to convert to a slug
 * @returns string - The generated slug
 */
export const generateSlugFromTitle = (title: string): string => {
  return title
    .toLowerCase()
    .trim()
    // Replace spaces and special characters with hyphens
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    // Remove leading/trailing hyphens
    .replace(/^-+|-+$/g, '')
    // Limit length to 60 characters for SEO
    .substring(0, 60)
    .replace(/-+$/, ''); // Remove trailing hyphen if substring cuts mid-word
};

/**
 * Checks if a slug already exists in the blogPosts collection
 * @param slug - The slug to check
 * @returns Promise<boolean> - True if slug exists, false otherwise
 */
export const checkSlugExists = async (slug: string): Promise<boolean> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { collection, query, where, getDocs } = await import('firebase/firestore');

    const blogPostsRef = collection(db, BLOG_COLLECTIONS.BLOG_POSTS);
    const slugQuery = query(blogPostsRef, where('slug', '==', slug));
    const querySnapshot = await getDocs(slugQuery);

    return !querySnapshot.empty;
  } catch (error) {
    console.error('Error checking slug existence:', error);
    return false;
  }
};

/**
 * Generates a unique slug by appending numbers if conflicts exist
 * @param baseSlug - The base slug to make unique
 * @returns Promise<string> - A unique slug
 */
export const generateUniqueSlug = async (baseSlug: string): Promise<string> => {
  let uniqueSlug = baseSlug;
  let counter = 1;

  while (await checkSlugExists(uniqueSlug)) {
    uniqueSlug = `${baseSlug}-${counter}`;
    counter++;
  }

  return uniqueSlug;
};

/**
 * Gets a published blog post by its slug
 * @param slug - The slug of the blog post to fetch
 * @returns Promise<PublishedBlogPost | null> - The blog post or null if not found
 */
export const getPublishedBlogPostBySlug = async (slug: string): Promise<PublishedBlogPost | null> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { collection, query, where, getDocs } = await import('firebase/firestore');

    console.log(`Fetching published blog post with slug: ${slug}`);

    // Create a reference to the blogPosts collection
    const blogPostsRef = collection(db, BLOG_COLLECTIONS.BLOG_POSTS);

    // Create a query to get the blog post by slug
    const slugQuery = query(blogPostsRef, where('slug', '==', slug));

    // Execute the query
    const querySnapshot = await getDocs(slugQuery);

    if (querySnapshot.empty) {
      console.log(`No published blog post found with slug: ${slug}`);
      return null;
    }

    // Get the first (and should be only) document
    const doc = querySnapshot.docs[0];
    const data = doc.data();

    // Convert Firestore timestamps to Date objects
    const createdAt = data.createdAt?.toDate ? data.createdAt.toDate() : new Date();
    const updatedAt = data.updatedAt?.toDate ? data.updatedAt.toDate() : new Date();

    // Create a PublishedBlogPost object from the document data
    const blogPost: PublishedBlogPost = {
      id: doc.id,
      title: data.title || '',
      content: data.content || '',
      excerpt: data.excerpt || '',
      author: data.author || 'Admin',
      tags: Array.isArray(data.tags) ? data.tags : [],
      coverImageUrl: data.coverImageUrl,
      published: true, // Always true for blogPosts collection
      createdAt: createdAt,
      updatedAt: updatedAt,
      slug: data.slug || '',
      readTime: data.readTime || 1
    };

    console.log(`Found published blog post: ${blogPost.title}`);
    return blogPost;
  } catch (error) {
    console.error('Error getting published blog post by slug:', error);
    throw error;
  }
};

/**
 * Gets adjacent blog posts (previous and next) for navigation
 * @param currentSlug - The slug of the current blog post
 * @returns Promise<{previous: PublishedBlogPost | null, next: PublishedBlogPost | null}>
 */
export const getAdjacentBlogPosts = async (currentSlug: string): Promise<{
  previous: PublishedBlogPost | null;
  next: PublishedBlogPost | null;
}> => {
  try {
    // Get all published blog posts sorted by creation date
    const allPosts = await getAllPublishedBlogPosts();

    // Find the current post index
    const currentIndex = allPosts.findIndex(post => post.slug === currentSlug);

    if (currentIndex === -1) {
      return { previous: null, next: null };
    }

    // Get previous and next posts
    const previous = currentIndex > 0 ? allPosts[currentIndex - 1] : null;
    const next = currentIndex < allPosts.length - 1 ? allPosts[currentIndex + 1] : null;

    return { previous, next };
  } catch (error) {
    console.error('Error getting adjacent blog posts:', error);
    return { previous: null, next: null };
  }
};

/**
 * Migrates existing blog posts to add slugs if they don't have them
 * This is a utility function for backward compatibility
 * @returns Promise<number> - Number of posts updated
 */
export const migrateBlogPostSlugs = async (): Promise<number> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { collection, getDocs, doc, updateDoc } = await import('firebase/firestore');

    console.log('Starting blog post slug migration...');

    // Get all blog posts from both collections
    const blogPostsRef = collection(db, BLOG_COLLECTIONS.BLOG_POSTS);
    const querySnapshot = await getDocs(blogPostsRef);

    let updatedCount = 0;
    const usedSlugs = new Set<string>();

    for (const docSnapshot of querySnapshot.docs) {
      const data = docSnapshot.data();

      // Skip if slug already exists
      if (data.slug && data.slug.trim()) {
        usedSlugs.add(data.slug);
        continue;
      }

      // Generate slug from title
      if (data.title) {
        const baseSlug = generateSlugFromTitle(data.title);
        let uniqueSlug = baseSlug;
        let counter = 1;

        // Ensure uniqueness within this migration batch
        while (usedSlugs.has(uniqueSlug)) {
          uniqueSlug = `${baseSlug}-${counter}`;
          counter++;
        }

        // Update the document with the new slug
        const docRef = doc(db, BLOG_COLLECTIONS.BLOG_POSTS, docSnapshot.id);
        await updateDoc(docRef, { slug: uniqueSlug });

        usedSlugs.add(uniqueSlug);
        updatedCount++;

        console.log(`Updated blog post "${data.title}" with slug: ${uniqueSlug}`);
      }
    }

    console.log(`Blog post slug migration completed. Updated ${updatedCount} posts.`);
    return updatedCount;
  } catch (error) {
    console.error('Error during blog post slug migration:', error);
    throw error;
  }
};

/**
 * Searches for a blog post by title if slug is not found (fallback mechanism)
 * @param searchTitle - The title to search for
 * @returns Promise<PublishedBlogPost | null> - The blog post or null if not found
 */
export const searchBlogPostByTitle = async (searchTitle: string): Promise<PublishedBlogPost | null> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { collection, getDocs } = await import('firebase/firestore');

    console.log(`Searching for blog post with title: ${searchTitle}`);

    // Get all published blog posts
    const blogPostsRef = collection(db, BLOG_COLLECTIONS.BLOG_POSTS);
    const querySnapshot = await getDocs(blogPostsRef);

    // Search for a post with matching title (case-insensitive)
    for (const doc of querySnapshot.docs) {
      const data = doc.data();

      if (data.title && data.title.toLowerCase().trim() === searchTitle.toLowerCase().trim()) {
        // Convert Firestore timestamps to Date objects
        const createdAt = data.createdAt?.toDate ? data.createdAt.toDate() : new Date();
        const updatedAt = data.updatedAt?.toDate ? data.updatedAt.toDate() : new Date();

        // Create a PublishedBlogPost object from the document data
        const blogPost: PublishedBlogPost = {
          id: doc.id,
          title: data.title || '',
          content: data.content || '',
          excerpt: data.excerpt || '',
          author: data.author || 'Admin',
          tags: Array.isArray(data.tags) ? data.tags : [],
          coverImageUrl: data.coverImageUrl,
          published: true,
          createdAt: createdAt,
          updatedAt: updatedAt,
          slug: data.slug || '',
          readTime: data.readTime || 1
        };

        console.log(`Found blog post by title: ${blogPost.title}`);
        return blogPost;
      }
    }

    console.log(`No blog post found with title: ${searchTitle}`);
    return null;
  } catch (error) {
    console.error('Error searching blog post by title:', error);
    throw error;
  }
};
