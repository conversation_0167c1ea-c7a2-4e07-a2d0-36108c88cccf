import{y as i,u as p,ab as u,r as g,j as s,H as k,x as b,X as j,b9 as y,a8 as o,L as N,ah as f,t as w,ak as v,O as M,aH as A,ag as C}from"./index-Bm_kDzMk.js";import{U as H}from"./users-vJ223Oqr.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const S=i("BookCheck",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}],["path",{d:"m9 9.5 2 2 4-4",key:"1dth82"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const O=i("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const L=i("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),q=({children:l,title:z="Admin Dashboard",description:B="Manage your book-sharing platform from here."})=>{var t;const{currentUser:a,signOut:r}=p(),d=u(),[n,c]=g.useState(!1),m=[{title:"Dashboard",icon:s.jsx(v,{className:"h-5 w-5"}),link:"/admin",description:"Admin dashboard overview"},{title:"Book Approvals",icon:s.jsx(S,{className:"h-5 w-5"}),link:"/admin/books",description:"Review and approve new book submissions"},{title:"User Management",icon:s.jsx(H,{className:"h-5 w-5"}),link:"/admin/users",description:"Manage users and permissions"},{title:"Blog Manager",icon:s.jsx(L,{className:"h-5 w-5"}),link:"/admin/blogs",description:"Create and manage blog posts and content"},{title:"Contact Messages",icon:s.jsx(M,{className:"h-5 w-5"}),link:"/admin/messages",description:"View and manage contact messages from users"},{title:"Feedback",icon:s.jsx(A,{className:"h-5 w-5"}),link:"/admin/feedback",description:"View and manage user feedback and support requests"},{title:"Admin Tools",icon:s.jsx(C,{className:"h-5 w-5"}),link:"/admin/utilities",description:"Administrative utilities and functions"},{title:"Admin Settings",icon:s.jsx(O,{className:"h-5 w-5"}),link:"/admin/settings",description:"Configure admin preferences and system settings"}],h=async()=>{try{await r()}catch(e){console.error("Error signing out:",e)}},x=e=>d.pathname===e;return s.jsxs("div",{className:"min-h-screen flex flex-col",children:[s.jsx(k,{}),s.jsxs("main",{className:"flex-grow flex flex-col md:flex-row",children:[s.jsx("div",{className:"md:hidden p-4 bg-white border-b",children:s.jsxs(b,{variant:"outline",size:"icon",onClick:()=>c(!n),className:"ml-auto flex",children:[n?s.jsx(j,{className:"h-5 w-5"}):s.jsx(y,{className:"h-5 w-5"}),s.jsx("span",{className:"sr-only",children:"Toggle menu"})]})}),s.jsxs("aside",{className:o("w-full md:w-64 bg-white shadow-md md:shadow-none transition-all duration-300 ease-in-out","md:block",n?"block":"hidden"),children:[s.jsxs("div",{className:"p-6 border-b",children:[s.jsx("h2",{className:"text-xl font-bold text-navy-800 mb-2",children:"Admin Panel"}),s.jsxs("p",{className:"text-sm text-gray-500",children:["Welcome, ",(a==null?void 0:a.displayName)||((t=a==null?void 0:a.email)==null?void 0:t.split("@")[0])||"Admin"]})]}),s.jsxs("nav",{className:"p-4 space-y-1",children:[m.map(e=>s.jsxs(N,{to:e.link,className:o("flex items-center px-4 py-3 rounded-md transition-colors",x(e.link)?"bg-burgundy-50 text-burgundy-700 font-medium":"text-gray-700 hover:bg-gray-100"),title:e.description,children:[s.jsx("span",{className:"mr-3",children:e.icon}),s.jsx("span",{children:e.title})]},e.title)),s.jsxs("button",{onClick:h,className:"w-full flex items-center px-4 py-3 rounded-md text-gray-700 hover:bg-gray-100 transition-colors",children:[s.jsx(f,{className:"h-5 w-5 mr-3"}),s.jsx("span",{children:"Sign Out"})]})]})]}),s.jsx("div",{className:"flex-1 p-4 md:p-8 bg-gray-50",children:s.jsx("div",{className:"max-w-5xl mx-auto",children:l})})]}),s.jsx(w,{})]})};export{q as A,L as F};
