import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/lib/AuthContext';
import { createBlog, autosaveBlogDraft } from '@/lib/blogService';
import { toast } from 'sonner';
import { Save, Clock, Database, HardDrive, CheckCircle, XCircle } from 'lucide-react';

interface AutosaveStatus {
  localStorage: 'idle' | 'saving' | 'success' | 'error';
  firestore: 'idle' | 'saving' | 'success' | 'error';
  lastLocalSave?: Date;
  lastFirestoreSave?: Date;
}

const AutosaveTester: React.FC = () => {
  const { currentUser, isAdmin } = useAuth();
  const [testBlogId, setTestBlogId] = useState<string | null>(null);
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [autosaveStatus, setAutosaveStatus] = useState<AutosaveStatus>({
    localStorage: 'idle',
    firestore: 'idle'
  });

  // localStorage autosave (every 3 seconds)
  useEffect(() => {
    if (!title && !content) return;

    const timeout = setTimeout(() => {
      try {
        setAutosaveStatus(prev => ({ ...prev, localStorage: 'saving' }));
        
        const draftData = {
          title,
          content,
          timestamp: new Date().toISOString(),
          testMode: true
        };
        
        localStorage.setItem('autosave-test-draft', JSON.stringify(draftData));
        
        setAutosaveStatus(prev => ({ 
          ...prev, 
          localStorage: 'success',
          lastLocalSave: new Date()
        }));
        
        console.log('LocalStorage autosave successful');
      } catch (error) {
        console.error('LocalStorage autosave failed:', error);
        setAutosaveStatus(prev => ({ ...prev, localStorage: 'error' }));
      }
    }, 3000);

    return () => clearTimeout(timeout);
  }, [title, content]);

  // Firestore autosave (every 8 seconds)
  useEffect(() => {
    if (!testBlogId || (!title && !content)) return;

    const timeout = setTimeout(async () => {
      try {
        setAutosaveStatus(prev => ({ ...prev, firestore: 'saving' }));
        
        await autosaveBlogDraft(testBlogId, {
          title: title || 'Autosave Test Blog',
          content: content || 'Testing autosave functionality...',
          tags: ['autosave', 'test'],
          excerpt: 'Autosave test excerpt'
        });
        
        setAutosaveStatus(prev => ({ 
          ...prev, 
          firestore: 'success',
          lastFirestoreSave: new Date()
        }));
        
        console.log('Firestore autosave successful');
      } catch (error) {
        console.error('Firestore autosave failed:', error);
        setAutosaveStatus(prev => ({ ...prev, firestore: 'error' }));
      }
    }, 8000);

    return () => clearTimeout(timeout);
  }, [testBlogId, title, content]);

  const createTestBlog = async () => {
    if (!currentUser || !isAdmin) {
      toast.error('You must be logged in as an admin');
      return;
    }

    try {
      const blogData = {
        title: 'Autosave Test Blog',
        content: 'This is a test blog for autosave functionality.',
        tags: ['autosave', 'test'],
        published: false,
        authorId: currentUser.uid,
        authorName: currentUser.displayName || currentUser.email?.split('@')[0] || 'Test User',
        authorEmail: currentUser.email || '<EMAIL>',
        excerpt: 'Test blog for autosave functionality'
      };

      const blogId = await createBlog(blogData);
      setTestBlogId(blogId);
      toast.success(`Test blog created with ID: ${blogId}`);
    } catch (error) {
      console.error('Failed to create test blog:', error);
      toast.error('Failed to create test blog');
    }
  };

  const loadFromLocalStorage = () => {
    try {
      const saved = localStorage.getItem('autosave-test-draft');
      if (saved) {
        const data = JSON.parse(saved);
        setTitle(data.title || '');
        setContent(data.content || '');
        toast.success('Draft loaded from localStorage');
      } else {
        toast.info('No localStorage draft found');
      }
    } catch (error) {
      console.error('Failed to load from localStorage:', error);
      toast.error('Failed to load from localStorage');
    }
  };

  const clearLocalStorage = () => {
    try {
      localStorage.removeItem('autosave-test-draft');
      toast.success('localStorage draft cleared');
      setAutosaveStatus(prev => ({ ...prev, localStorage: 'idle', lastLocalSave: undefined }));
    } catch (error) {
      console.error('Failed to clear localStorage:', error);
      toast.error('Failed to clear localStorage');
    }
  };

  const getStatusIcon = (status: AutosaveStatus['localStorage']) => {
    switch (status) {
      case 'idle':
        return <Clock className="h-4 w-4 text-gray-400" />;
      case 'saving':
        return <Clock className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
    }
  };

  const getStatusColor = (status: AutosaveStatus['localStorage']) => {
    switch (status) {
      case 'idle':
        return 'bg-gray-100 text-gray-600';
      case 'saving':
        return 'bg-blue-100 text-blue-600';
      case 'success':
        return 'bg-green-100 text-green-600';
      case 'error':
        return 'bg-red-100 text-red-600';
    }
  };

  const formatTime = (date?: Date) => {
    return date ? date.toLocaleTimeString() : 'Never';
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Save className="h-5 w-5 text-blue-500" />
          Autosave Functionality Tester
        </CardTitle>
        <CardDescription>
          Test both localStorage and Firestore autosave mechanisms. 
          LocalStorage saves every 3 seconds, Firestore saves every 8 seconds.
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Setup */}
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Button 
              onClick={createTestBlog}
              disabled={!!testBlogId || !isAdmin}
              variant="outline"
            >
              Create Test Blog
            </Button>
            {testBlogId && (
              <Badge variant="secondary">
                Test Blog ID: {testBlogId}
              </Badge>
            )}
          </div>
        </div>

        {/* Autosave Status */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 border rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <HardDrive className="h-4 w-4" />
              <span className="font-medium">LocalStorage Autosave</span>
              {getStatusIcon(autosaveStatus.localStorage)}
            </div>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Status:</span>
                <Badge variant="outline" className={getStatusColor(autosaveStatus.localStorage)}>
                  {autosaveStatus.localStorage}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Last Save:</span>
                <span>{formatTime(autosaveStatus.lastLocalSave)}</span>
              </div>
            </div>
            <div className="flex gap-2 mt-3">
              <Button size="sm" variant="outline" onClick={loadFromLocalStorage}>
                Load Draft
              </Button>
              <Button size="sm" variant="outline" onClick={clearLocalStorage}>
                Clear
              </Button>
            </div>
          </div>

          <div className="p-4 border rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Database className="h-4 w-4" />
              <span className="font-medium">Firestore Autosave</span>
              {getStatusIcon(autosaveStatus.firestore)}
            </div>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Status:</span>
                <Badge variant="outline" className={getStatusColor(autosaveStatus.firestore)}>
                  {autosaveStatus.firestore}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span>Last Save:</span>
                <span>{formatTime(autosaveStatus.lastFirestoreSave)}</span>
              </div>
              <div className="flex justify-between">
                <span>Requires:</span>
                <span>Test Blog ID</span>
              </div>
            </div>
          </div>
        </div>

        {/* Test Form */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Title</label>
            <Input
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Start typing to trigger autosave..."
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Content</label>
            <Textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder="Start typing to trigger autosave..."
              rows={6}
            />
          </div>
        </div>

        {/* Instructions */}
        <div className="p-4 bg-blue-50 rounded-lg">
          <h4 className="font-medium text-blue-800 mb-2">How to Test</h4>
          <ol className="text-sm text-blue-600 space-y-1">
            <li>1. Create a test blog first (required for Firestore autosave)</li>
            <li>2. Start typing in the title or content fields</li>
            <li>3. Watch the autosave status indicators</li>
            <li>4. LocalStorage saves every 3 seconds, Firestore every 8 seconds</li>
            <li>5. Test loading from localStorage and clearing drafts</li>
          </ol>
        </div>
      </CardContent>
    </Card>
  );
};

export default AutosaveTester;
