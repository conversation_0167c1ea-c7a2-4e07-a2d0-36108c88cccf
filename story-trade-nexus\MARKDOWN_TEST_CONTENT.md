# Test Markdown Content for <PERSON>logEditor

Use this content to test the Markdown editor functionality:

```markdown
# Welcome to PeerBooks Blog

## Introduction
This is a **comprehensive guide** to using our *enhanced blog editor* with Markdown support.

### Key Features
- Rich text editing with ReactQuill
- Markdown editing with live preview
- Auto-save to both localStorage and Firestore
- Real-time preview updates

### Getting Started

#### Rich Text Mode
The traditional WYSIWYG editor provides:
1. **Bold** and *italic* formatting
2. Headers and lists
3. Links and blockquotes
4. Code blocks

#### Markdown Mode
Write in plain text with formatting syntax:

**Bold text** and *italic text*

> This is a blockquote with important information

##### Code Examples
Inline `code` and code blocks:

```javascript
const blogPost = {
  title: "My First Post",
  content: "Hello, world!",
  published: true
};
```

##### Lists
Unordered list:
- First item
- Second item
  - Nested item
  - Another nested item

Ordered list:
1. Step one
2. Step two
3. Step three

##### Links and Images
[Visit PeerBooks](https://peerbooks.example.com)

![Alt text for image](https://example.com/image.jpg)

### Advanced Features

#### Tables (if supported)
| Feature | Rich Text | Markdown |
|---------|-----------|----------|
| WYSIWYG | ✅ | ❌ |
| Raw Control | ❌ | ✅ |
| Preview | ✅ | ✅ |

#### Horizontal Rule
---

### Conclusion
The enhanced blog editor provides flexibility for all types of content creators, from beginners who prefer visual editing to advanced users who want full control over their content structure.

**Happy blogging!** 🎉
```

## Testing Checklist

### Markdown Rendering Tests
- [ ] Headers (H1-H6) render with proper styling
- [ ] Bold and italic text formatting works
- [ ] Lists (ordered and unordered) display correctly
- [ ] Links are clickable and styled properly
- [ ] Blockquotes have proper indentation and styling
- [ ] Code blocks and inline code have monospace font
- [ ] Line breaks and paragraphs are handled correctly

### Editor Mode Tests
- [ ] Switch from Rich Text to Markdown mode
- [ ] Switch from Markdown to Rich Text mode
- [ ] Warning message appears when switching modes
- [ ] Content is preserved during mode switches
- [ ] Preview updates correctly for both modes

### Auto-save Tests
- [ ] localStorage saves every 4 seconds
- [ ] Firestore saves every 10 seconds (for drafts)
- [ ] Draft indicator appears when local draft exists
- [ ] Clear draft button removes localStorage data
- [ ] Draft restoration prompt appears on dialog open

### Preview Tests
- [ ] Live preview updates as you type
- [ ] Markdown content renders correctly in preview
- [ ] Rich text content displays properly in preview
- [ ] Preview styling matches final blog appearance
- [ ] Preview scrolls independently from editor

### Error Handling Tests
- [ ] Invalid Markdown syntax handled gracefully
- [ ] localStorage quota exceeded handled properly
- [ ] Network failures don't break functionality
- [ ] Empty content handled correctly
- [ ] Large content doesn't cause performance issues
