import{ax as _,r as s,ay as H,aC as I,j as r,aD as x,az as M,aE as z,a8 as S}from"./index-Bm_kDzMk.js";import{u as B}from"./index-Cr71giPO.js";var v="Switch",[q,X]=_(v),[A,D]=q(v),g=s.forwardRef((e,o)=>{const{__scopeSwitch:t,name:a,checked:n,defaultChecked:l,required:i,disabled:c,value:d="on",onCheckedChange:b,form:m,...p}=e,[u,j]=s.useState(null),R=H(o,f=>j(f)),k=s.useRef(!1),w=u?m||!!u.closest("form"):!0,[h=!1,N]=I({prop:n,defaultProp:l,onChange:b});return r.jsxs(A,{scope:t,checked:h,disabled:c,children:[r.jsx(x.button,{type:"button",role:"switch","aria-checked":h,"aria-required":i,"data-state":P(h),"data-disabled":c?"":void 0,disabled:c,value:d,...p,ref:R,onClick:M(e.onClick,f=>{N(T=>!T),w&&(k.current=f.isPropagationStopped(),k.current||f.stopPropagation())})}),w&&r.jsx(O,{control:u,bubbles:!k.current,name:a,value:d,checked:h,required:i,disabled:c,form:m,style:{transform:"translateX(-100%)"}})]})});g.displayName=v;var C="SwitchThumb",y=s.forwardRef((e,o)=>{const{__scopeSwitch:t,...a}=e,n=D(C,t);return r.jsx(x.span,{"data-state":P(n.checked),"data-disabled":n.disabled?"":void 0,...a,ref:o})});y.displayName=C;var O=e=>{const{control:o,checked:t,bubbles:a=!0,...n}=e,l=s.useRef(null),i=B(t),c=z(o);return s.useEffect(()=>{const d=l.current,b=window.HTMLInputElement.prototype,p=Object.getOwnPropertyDescriptor(b,"checked").set;if(i!==t&&p){const u=new Event("click",{bubbles:a});p.call(d,t),d.dispatchEvent(u)}},[i,t,a]),r.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:t,...n,tabIndex:-1,ref:l,style:{...e.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function P(e){return e?"checked":"unchecked"}var E=g,F=y;const L=s.forwardRef(({className:e,...o},t)=>r.jsx(E,{className:S("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...o,ref:t,children:r.jsx(F,{className:S("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));L.displayName=E.displayName;export{L as S};
