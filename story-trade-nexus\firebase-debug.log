[debug] [2025-06-22T17:10:56.788Z] ----------------------------------------------------------------------
[debug] [2025-06-22T17:10:56.790Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js init
[debug] [2025-06-22T17:10:56.790Z] CLI Version:   14.5.1
[debug] [2025-06-22T17:10:56.791Z] Platform:      win32
[debug] [2025-06-22T17:10:56.791Z] Node Version:  v24.0.0
[debug] [2025-06-22T17:10:56.791Z] Time:          Sun Jun 22 2025 22:40:56 GMT+0530 (India Standard Time)
[debug] [2025-06-22T17:10:56.791Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-06-22T17:10:56.793Z] >>> [apiv2][query] GET https://firebase-public.firebaseio.com/cli.json [none]
[debug] [2025-06-22T17:10:57.011Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-22T17:10:57.011Z] > authorizing via signed-in user (<EMAIL>)
[info] 
     ######## #### ########  ######## ########     ###     ######  ########
     ##        ##  ##     ## ##       ##     ##  ##   ##  ##       ##
     ######    ##  ########  ######   ########  #########  ######  ######
     ##        ##  ##    ##  ##       ##     ## ##     ##       ## ##
     ##       #### ##     ## ######## ########  ##     ##  ######  ########

You're about to initialize a Firebase project in this directory:

  C:\Users\<USER>\Documents\augment-projects\book-sharing\story-trade-nexus

Before we get started, keep in mind:

  * You are initializing within an existing Firebase project directory

[debug] [2025-06-22T17:10:57.603Z] <<< [apiv2][status] GET https://firebase-public.firebaseio.com/cli.json 200
[debug] [2025-06-22T17:10:57.604Z] <<< [apiv2][body] GET https://firebase-public.firebaseio.com/cli.json {"cloudBuildErrorAfter":1594252800000,"cloudBuildWarnAfter":1590019200000,"defaultNode10After":1594252800000,"minVersion":"3.0.5","node8DeploysDisabledAfter":1613390400000,"node8RuntimeDisabledAfter":1615809600000,"node8WarnAfter":1600128000000}
[debug] [2025-06-22T17:21:18.181Z] ExitPromptError: User force closed the prompt with SIGINT
    at Interface.sigint (C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\node_modules\@inquirer\core\dist\commonjs\lib\create-prompt.js:101:37)
    at Interface.emit (node:events:507:28)
    at Interface.emit (node:domain:489:12)
    at [_ttyWrite] [as _ttyWrite] (node:internal/readline/interface:1241:18)
    at ReadStream.onkeypress (node:internal/readline/interface:288:20)
    at ReadStream.emit (node:events:519:35)
    at ReadStream.emit (node:domain:489:12)
    at emitKeys (node:internal/readline/utils:370:14)
    at emitKeys.next (<anonymous>)
    at ReadStream.onData (node:internal/readline/emitKeypressEvents:64:36)
[error] 
[error] Error: An unexpected error has occurred.
