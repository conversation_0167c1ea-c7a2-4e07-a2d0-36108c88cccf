import{y as W,j as e,p as ce,r as d,a8 as v,aB as de,a1 as X,x as E,be as ue,ab as me,u as he,J as T,a2 as xe,R as ge,bf as fe,H as pe,S as ye,I as ve,q as M,N as V,$ as I,ai as je,s as be,L as Ne,t as we}from"./index-Bm_kDzMk.js";import{S as Pe,a as ke,b as Se,c as Ce,d as Be}from"./select-BZgYO6JR.js";import{C as Z}from"./chevron-left-Cxv7-6br.js";import"./index-Cr71giPO.js";import"./chevron-up-B0Gx3VCi.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ee=W("ArrowUpDown",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ae=W("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]),O=[{value:"community-distance",label:"Community + Distance",description:"Same community first, then by distance"},{value:"price-low-high",label:"Price: Low to High",description:"Selling price from lowest to highest"},{value:"price-high-low",label:"Price: High to Low",description:"Selling price from highest to lowest"},{value:"rental-low-high",label:"Rental: Low to High",description:"Rental price from lowest to highest"},{value:"rental-high-low",label:"Rental: High to Low",description:"Rental price from highest to lowest"},{value:"distance",label:"Distance",description:"Closest to farthest"},{value:"newest-first",label:"Newest First",description:"Most recently added books first"},{value:"oldest-first",label:"Oldest First",description:"Oldest books first"}],L=s=>!s.availability.includes("Sale")||!s.price?null:s.price,R=s=>{var n;if(!s.availability.includes("Rent")||!s.rentalPrice)return null;const t=s.rentalPrice,a=((n=s.rentalPeriod)==null?void 0:n.toLowerCase())||"per day";return a.includes("week")?t/7:a.includes("month")?t/30:a.includes("year")?t/365:t},m=s=>(s.createdAt instanceof Date?s.createdAt:new Date(s.createdAt)).getTime(),Ie=(s,t,a)=>!s||s.length===0?[]:[...s].sort((n,i)=>{if(t!=="distance"){const r=a&&n.ownerCommunity&&n.ownerCommunity===a,l=a&&i.ownerCommunity&&i.ownerCommunity===a;if(r&&!l)return-1;if(l&&!r)return 1}switch(t){case"community-distance":return n.distance!==void 0&&i.distance!==void 0?n.distance-i.distance:n.distance!==void 0?-1:i.distance!==void 0?1:m(i)-m(n);case"price-low-high":{const r=L(n),l=L(i);return r!==null&&l!==null?r-l:r!==null?-1:l!==null?1:n.distance!==void 0&&i.distance!==void 0?n.distance-i.distance:m(i)-m(n)}case"price-high-low":{const r=L(n),l=L(i);return r!==null&&l!==null?l-r:r!==null?-1:l!==null?1:n.distance!==void 0&&i.distance!==void 0?n.distance-i.distance:m(i)-m(n)}case"rental-low-high":{const r=R(n),l=R(i);return r!==null&&l!==null?r-l:r!==null?-1:l!==null?1:n.distance!==void 0&&i.distance!==void 0?n.distance-i.distance:m(i)-m(n)}case"rental-high-low":{const r=R(n),l=R(i);return r!==null&&l!==null?l-r:r!==null?-1:l!==null?1:n.distance!==void 0&&i.distance!==void 0?n.distance-i.distance:m(i)-m(n)}case"distance":return n.distance!==void 0&&i.distance!==void 0?n.distance-i.distance:n.distance!==void 0?-1:i.distance!==void 0?1:m(i)-m(n);case"newest-first":return m(i)-m(n);case"oldest-first":return m(n)-m(i);default:return m(i)-m(n)}}),Y=()=>"community-distance",Le=(s,t)=>{switch(t){case"price-low-high":case"price-high-low":return s.some(a=>L(a)!==null);case"rental-low-high":case"rental-high-low":return s.some(a=>R(a)!==null);case"distance":case"community-distance":return s.some(a=>a.distance!==void 0);default:return!0}},Re=s=>O.filter(t=>Le(s,t.value)),Fe=({sortCriteria:s,onSortChange:t,books:a,disabled:n=!1,className:i=""})=>{const r=Re(a),l=O.find(o=>o.value===s),p=o=>{switch(o){case"price-low-high":case"price-high-low":return a.filter(u=>u.availability.includes("Sale")&&u.price).length;case"rental-low-high":case"rental-high-low":return a.filter(u=>u.availability.includes("Rent")&&u.rentalPrice).length;case"distance":case"community-distance":return a.filter(u=>u.distance!==void 0).length;default:return a.length}};return e.jsx("div",{className:i,children:e.jsxs(Pe,{value:s,onValueChange:o=>t(o),disabled:n,children:[e.jsx(ke,{className:"h-10 w-full",children:e.jsxs("div",{className:"flex items-center gap-2 w-full",children:[e.jsx(ee,{className:"h-4 w-4 text-gray-500 flex-shrink-0"}),e.jsx(Se,{placeholder:"Sort by...",children:e.jsx("span",{className:"text-sm",children:(l==null?void 0:l.label)||"Sort by..."})})]})}),e.jsx(Ce,{children:r.map(o=>{const u=p(o.value),h=o.value===s;return e.jsx(Be,{value:o.value,className:"cursor-pointer",children:e.jsx("div",{className:"flex items-start justify-between w-full",children:e.jsxs("div",{className:"flex flex-col flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"font-medium",children:o.label}),h&&e.jsx(ce,{className:"h-4 w-4 text-burgundy-600"})]}),e.jsx("span",{className:"text-xs text-gray-500 mt-1",children:o.description}),u<a.length&&e.jsxs("span",{className:"text-xs text-blue-600 mt-1",children:[u," of ",a.length," books have this data"]})]})})},o.value)})})]})})},se=({className:s,...t})=>e.jsx("nav",{role:"navigation","aria-label":"pagination",className:v("mx-auto flex w-full justify-center",s),...t});se.displayName="Pagination";const te=d.forwardRef(({className:s,...t},a)=>e.jsx("ul",{ref:a,className:v("flex flex-row items-center gap-1",s),...t}));te.displayName="PaginationContent";const F=d.forwardRef(({className:s,...t},a)=>e.jsx("li",{ref:a,className:v("",s),...t}));F.displayName="PaginationItem";const $=({className:s,isActive:t,size:a="icon",...n})=>e.jsx("a",{"aria-current":t?"page":void 0,className:v(de({variant:t?"outline":"ghost",size:a}),s),...n});$.displayName="PaginationLink";const ae=({className:s,...t})=>e.jsxs($,{"aria-label":"Go to previous page",size:"default",className:v("gap-1 pl-2.5",s),...t,children:[e.jsx(Z,{className:"h-4 w-4"}),e.jsx("span",{children:"Previous"})]});ae.displayName="PaginationPrevious";const ne=({className:s,...t})=>e.jsxs($,{"aria-label":"Go to next page",size:"default",className:v("gap-1 pr-2.5",s),...t,children:[e.jsx("span",{children:"Next"}),e.jsx(X,{className:"h-4 w-4"})]});ne.displayName="PaginationNext";const ie=({className:s,...t})=>e.jsxs("span",{"aria-hidden":!0,className:v("flex h-9 w-9 items-center justify-center",s),...t,children:[e.jsx(Ae,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"More pages"})]});ie.displayName="PaginationEllipsis";const re=(s,t,a=12)=>{const n=Math.max(1,t),i=Math.max(1,Math.ceil(s/a)),r=Math.min(n,i),l=(r-1)*a,p=Math.min(l+a-1,s-1),o=r<i,u=r>1,h=Ee(r,i);return{currentPage:r,totalPages:i,totalItems:s,itemsPerPage:a,startIndex:l,endIndex:p,hasNextPage:o,hasPreviousPage:u,pageNumbers:h}},Ee=(s,t)=>{if(t<=7)return Array.from({length:t},(n,i)=>i+1);const a=[];if(s<=4)a.push(1,2,3,4,5),t>6&&a.push(-1),a.push(t);else if(s>=t-3){a.push(1),t>6&&a.push(-1);for(let n=t-4;n<=t;n++)a.push(n)}else a.push(1),a.push(-1),a.push(s-1,s,s+1),a.push(-1),a.push(t);return a},Te=(s,t,a=12)=>{const n=re(s.length,t,a),i=n.startIndex,r=i+a;return{items:s.slice(i,r),pagination:n}},q=s=>{if(s.totalItems===0)return"No books found";const t=s.startIndex+1,a=Math.min(s.endIndex+1,s.totalItems);return s.totalPages===1?`Showing ${s.totalItems} book${s.totalItems===1?"":"s"}`:`Showing ${t}-${a} of ${s.totalItems} books`},Me=(s,t)=>`Go to page ${s} of ${t}`,J=s=>{if(s==null)return 1;const t=typeof s=="string"?parseInt(s,10):s;return isNaN(t)||t<1?1:t},Q=(s,t,a=!1)=>{const n=new URLSearchParams(s);return a||t===1?n.delete("page"):n.set("page",t.toString()),n},$e=({pagination:s,onPageChange:t,onNextPage:a,onPreviousPage:n,className:i,showInfo:r=!0,compact:l=!1})=>{const{currentPage:p,totalPages:o,hasNextPage:u,hasPreviousPage:h,pageNumbers:B}=s;if(o<=1)return r&&s.totalItems>0?e.jsx("div",{className:v("flex justify-center py-4",i),children:e.jsx("p",{className:"text-sm text-gray-600",children:q(s)})}):null;const x=g=>{g!==p&&g>=1&&g<=o&&t(g)},j=()=>{h&&n()},y=()=>{u&&a()};return e.jsxs("div",{className:v("flex flex-col items-center space-y-4 py-6",i),children:[r&&e.jsx("div",{className:"text-sm text-gray-600 text-center",children:q(s)}),e.jsx(se,{children:e.jsxs(te,{children:[e.jsx(F,{children:e.jsx(ae,{onClick:j,className:v("cursor-pointer",!h&&"pointer-events-none opacity-50"),"aria-disabled":!h})}),!l&&B.map((g,P)=>e.jsx(F,{children:g===-1?e.jsx(ie,{}):e.jsx($,{onClick:()=>x(g),isActive:g===p,className:"cursor-pointer","aria-label":Me(g,o),children:g})},P)),l&&e.jsx(F,{children:e.jsxs("span",{className:"px-3 py-2 text-sm",children:["Page ",p," of ",o]})}),e.jsx(F,{children:e.jsx(ne,{onClick:y,className:v("cursor-pointer",!u&&"pointer-events-none opacity-50"),"aria-disabled":!u})})]})}),e.jsxs("div",{className:"flex sm:hidden items-center space-x-2",children:[e.jsxs(E,{variant:"outline",size:"sm",onClick:j,disabled:!h,className:"flex items-center space-x-1",children:[e.jsx(Z,{className:"h-4 w-4"}),e.jsx("span",{children:"Previous"})]}),e.jsxs("span",{className:"px-3 py-1 text-sm bg-gray-100 rounded",children:[p," / ",o]}),e.jsxs(E,{variant:"outline",size:"sm",onClick:y,disabled:!u,className:"flex items-center space-x-1",children:[e.jsx("span",{children:"Next"}),e.jsx(X,{className:"h-4 w-4"})]})]})]})},K="peerbooks-sort-preference",Oe=()=>{const[s,t]=d.useState(Y());d.useEffect(()=>{try{const r=localStorage.getItem(K);if(r){const l=r;["community-distance","price-low-high","price-high-low","rental-low-high","rental-high-low","distance","newest-first","oldest-first"].includes(l)&&t(l)}}catch(r){console.warn("Failed to load sort preference from localStorage:",r)}},[]);const a=d.useCallback(r=>{t(r);try{localStorage.setItem(K,r)}catch(l){console.warn("Failed to save sort preference to localStorage:",l)}},[]),n=d.useCallback((r,l)=>Ie(r,s,l),[s]),i=d.useCallback(()=>{const r=Y();a(r)},[a]);return{sortCriteria:s,setSortCriteria:a,sortBooks:n,resetSort:i}},Ge=(s,t=[],a={})=>{const{itemsPerPage:n=12,resetOnDependencyChange:i=!0}=a,[r,l]=ue();me();const p=J(r.get("page")),[o,u]=d.useState(p),[h,B]=d.useState(s),x=d.useMemo(()=>re(h,o,n),[h,o,n]);d.useEffect(()=>{const f=J(r.get("page"));f!==o&&u(f)},[r,o]),d.useEffect(()=>{B(s)},[s]),d.useEffect(()=>{i&&t.length>0&&o>1&&P()},t),d.useEffect(()=>{o>x.totalPages&&x.totalPages>0&&j(x.totalPages)},[o,x.totalPages]);const j=d.useCallback(f=>{const N=Math.max(1,Math.min(f,x.totalPages||1));if(N!==o){u(N);const w=Q(r,N);l(w,{replace:!0})}},[o,x.totalPages,r,l]),y=d.useCallback(()=>{x.hasNextPage&&j(o+1)},[o,x.hasNextPage,j]),g=d.useCallback(()=>{x.hasPreviousPage&&j(o-1)},[o,x.hasPreviousPage,j]),P=d.useCallback(()=>{if(o!==1){u(1);const f=Q(r,1,!0);l(f,{replace:!0})}},[o,r,l]),A=d.useCallback(f=>{B(f)},[]);return{currentPage:o,pagination:x,goToPage:j,goToNextPage:y,goToPreviousPage:g,resetToFirstPage:P,updateTotalItems:A}},De=(s,t,a,n,i)=>Ge(s,[t,a,n,i],{itemsPerPage:12,resetOnDependencyChange:!0}),Ye=()=>{var U;const{userData:s}=he(),{sortCriteria:t,setSortCriteria:a,sortBooks:n}=Oe(),[i,r]=d.useState(""),[l,p]=d.useState("All"),[o,u]=d.useState("All"),[h,B]=d.useState([]),[x,j]=d.useState(0),[y,g]=d.useState(!0),[P,A]=d.useState(null),[f,N]=d.useState(null),w=De(x,i,l,o,t),le=["All","Fiction","Classics","Fantasy","Young Adult","Philosophy","Romance","Dystopian"],oe=["All","For Rent","For Sale","For Exchange"];d.useEffect(()=>{G()},[]);const G=async()=>{try{g(!0),A(null),N("loading"),console.log("BrowseBooks: Fetching books from Firebase"),T.info("Getting your location to find nearby books...",{duration:3e3,id:"location-toast"});const c=s==null?void 0:s.community;console.log("BrowseBooks: User community for sorting:",c);const b=await xe(!1,c);if(b.some(C=>C.distance!==void 0)){N("success");const C=c?`Books sorted by community (${c} first) and distance`:"Books sorted by distance (closest first)";T.success(C,{id:"location-toast",duration:4e3})}else{N("error");const C=c?`Books sorted by community (${c} first) and newest first`:"Books sorted by newest first";T.info(C,{id:"location-toast",duration:3e3})}B(b),console.log(`BrowseBooks: Fetched ${b.length} books from Firebase`)}catch(c){console.error("Error fetching books:",c),N("error"),c instanceof Error?(A(`Failed to load books: ${c.message}. Please try again.`),(c.message.includes("permission")||c.message.includes("denied"))&&(N("denied"),T.error("Location access denied. Books sorted by newest first.",{id:"location-toast",duration:5e3}))):A("Failed to load books. Please try again.")}finally{g(!1)}},D=()=>{console.log("BrowseBooks: Refreshing books"),G()},z=h.filter(c=>{const b=i===""||c.title.toLowerCase().includes(i.toLowerCase())||c.author.toLowerCase().includes(i.toLowerCase()),_=l==="All"||c.genre.includes(l),C=o==="All"||c.availability.includes(o);return b&&_&&C}),k=n(z,s==null?void 0:s.community);ge.useEffect(()=>{w.updateTotalItems(k.length)},[k.length,w]);const S=Te(k,w.currentPage,12),H=S.items;return fe(H),e.jsxs("div",{className:"min-h-screen flex flex-col",children:[e.jsx(pe,{}),e.jsx("main",{className:"flex-grow bg-beige-50",children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-playfair font-bold text-navy-800 mb-2",children:"Browse Books"}),e.jsx("p",{className:"text-gray-600",children:"Discover books available for exchange, rent, or purchase"})]}),e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md mb-8",children:[e.jsxs("div",{className:"hidden sm:grid sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-2",children:[e.jsx("div",{className:"sm:col-span-2 lg:col-span-1",children:e.jsx("label",{className:"text-xs font-medium text-gray-700 uppercase tracking-wide",children:"Search"})}),e.jsx("div",{children:e.jsx("label",{className:"text-xs font-medium text-gray-700 uppercase tracking-wide",children:"Genre"})}),e.jsx("div",{children:e.jsx("label",{className:"text-xs font-medium text-gray-700 uppercase tracking-wide",children:"Availability"})}),e.jsx("div",{children:e.jsx("label",{className:"text-xs font-medium text-gray-700 uppercase tracking-wide",children:"Sort By"})})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"relative sm:col-span-2 lg:col-span-1",children:[e.jsx(ye,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500"}),e.jsx(ve,{type:"text",placeholder:"Search by title or author...",className:"pl-10 h-10",value:i,onChange:c=>r(c.target.value),disabled:y})]}),e.jsx("div",{children:e.jsx("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",value:l,onChange:c=>p(c.target.value),disabled:y,"aria-label":"Filter by genre",children:le.map(c=>e.jsx("option",{value:c,children:c},c))})}),e.jsx("div",{children:e.jsx("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",value:o,onChange:c=>u(c.target.value),disabled:y,"aria-label":"Filter by availability",children:oe.map(c=>e.jsx("option",{value:c,children:c},c))})}),e.jsx("div",{children:e.jsx(Fe,{sortCriteria:t,onSortChange:a,books:z,disabled:y})})]}),t!=="community-distance"&&e.jsx("div",{className:"mt-4 pt-4 border-t border-gray-200",children:e.jsxs("div",{className:"flex items-center gap-2 text-sm text-burgundy-700 bg-burgundy-50 px-3 py-2 rounded-md",children:[e.jsx(ee,{className:"h-4 w-4"}),e.jsx("span",{className:"font-medium",children:"Active sort:"}),e.jsx("span",{children:(U=O.find(c=>c.value===t))==null?void 0:U.label}),e.jsx("button",{onClick:()=>a("community-distance"),className:"ml-auto text-xs text-burgundy-600 hover:text-burgundy-800 underline",children:"Reset to default"})]})})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-2 text-sm text-gray-600",children:[e.jsx("span",{className:"font-medium",children:k.length>0?e.jsxs(e.Fragment,{children:["Showing ",S.pagination.startIndex+1,"-",Math.min(S.pagination.endIndex+1,k.length)," of ",k.length," books",S.pagination.totalPages>1&&e.jsxs("span",{className:"ml-1",children:["(Page ",S.pagination.currentPage," of ",S.pagination.totalPages,")"]})]}):`0 of ${h.length} books`}),(i||l!=="All"||o!=="All")&&e.jsx("span",{className:"text-blue-600",children:"(filtered)"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex items-center",children:[f==="loading"&&e.jsxs("div",{className:"flex items-center text-sm text-gray-600",children:[e.jsx(M,{className:"h-4 w-4 mr-1 text-gray-400 animate-pulse"}),e.jsx("span",{children:"Getting your location..."})]}),f==="success"&&e.jsxs("div",{className:"flex items-center text-sm text-green-600",children:[e.jsx(M,{className:"h-4 w-4 mr-1 text-green-500"}),e.jsx("span",{children:s!=null&&s.community?`Books sorted by community (${s.community} first) and distance`:"Books sorted by distance (closest first)"})]}),f==="error"&&e.jsxs("div",{className:"flex items-center text-sm text-gray-600",children:[e.jsx(M,{className:"h-4 w-4 mr-1 text-gray-400"}),e.jsx("span",{children:s!=null&&s.community?`Books sorted by community (${s.community} first) and newest first`:"Books sorted by newest first"})]}),f==="denied"&&e.jsxs("div",{className:"flex items-center text-sm text-amber-600",children:[e.jsx(M,{className:"h-4 w-4 mr-1 text-amber-500"}),e.jsx("span",{children:"Location access denied. Books sorted by newest first."})]})]}),e.jsx(E,{variant:"outline",onClick:D,disabled:y,className:"text-sm",children:y?e.jsxs(e.Fragment,{children:[e.jsx(V,{className:"h-4 w-4 mr-2 animate-spin"}),"Loading..."]}):e.jsxs(e.Fragment,{children:[e.jsx(V,{className:"h-4 w-4 mr-2"}),"Refresh Books"]})})]})]}),P&&e.jsxs("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:[e.jsx("p",{children:P}),e.jsx(E,{variant:"link",onClick:D,className:"text-red-700 p-0 h-auto text-sm",children:"Try Again"})]}),y?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:[...Array(8)].map((c,b)=>e.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[e.jsx(I,{className:"h-64 w-full"}),e.jsxs("div",{className:"p-4",children:[e.jsx(I,{className:"h-6 w-3/4 mb-2"}),e.jsx(I,{className:"h-4 w-1/2 mb-4"}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx(I,{className:"h-8 w-20"}),e.jsx(I,{className:"h-8 w-20"})]})]})]},b))}):k.length>0?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:H.map((c,b)=>e.jsx(je,{book:c,index:b,priority:b<8},c.id))}),e.jsx($e,{pagination:S.pagination,onPageChange:w.goToPage,onNextPage:w.goToNextPage,onPreviousPage:w.goToPreviousPage,className:"mt-8",showInfo:!1})]}):h.length===0?e.jsxs("div",{className:"text-center py-16 bg-beige-50 rounded-lg",children:[e.jsx(be,{className:"h-12 w-12 mx-auto text-gray-400 mb-4"}),e.jsx("h3",{className:"text-xl font-medium text-gray-700 mb-2",children:"No Books Available Yet"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Be the first to add books to our community!"}),e.jsx(Ne,{to:"/add-books",children:e.jsx(E,{children:"Add Your Books"})})]}):e.jsxs("div",{className:"text-center py-16",children:[e.jsx("p",{className:"text-lg text-gray-600 mb-2",children:"No books found matching your criteria"}),e.jsx("p",{className:"text-burgundy-500",children:"Try adjusting your filters or search term"})]})]})}),e.jsx(we,{})]})};export{Ye as default};
