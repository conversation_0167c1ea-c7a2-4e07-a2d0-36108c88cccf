const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.esm-ehpEbksy.js","assets/index.esm2017-H7c5Bkvh.js","assets/index-Bm_kDzMk.js","assets/index-CM4Ud3ZP.css"])))=>i.map(i=>d[i]);
import{z as d,f as te,r as w,u as re,h as se,j as e,H as ne,i as le,k as u,l as m,m as g,n as h,I as F,o as b,p as _,X as ae,q as ce,a as Y,s as de,t as ue,J as p,v as me,_ as $,w as ge}from"./index-Bm_kDzMk.js";import{T as he}from"./textarea-3apq9z-y.js";import{u as pe}from"./storageService-eCr3LQAS.js";import{getCurrentPosition as xe}from"./geolocationUtils-DGx5pdrF.js";import{I as be}from"./image-C6TIrua_.js";import{C as fe}from"./circle-alert-FwbBEnuF.js";import"./index.esm-3FY_JRiK.js";import"./index.esm2017-H7c5Bkvh.js";const je=d.object({title:d.string().min(1,{message:"Book title is required"}),author:d.string().min(1,{message:"Author name is required"}),isbn:d.string().optional(),genre:d.string().min(1,{message:"Please select at least one genre"}),condition:d.string().min(1,{message:"Please select a condition"}),description:d.string().min(10,{message:"Description should be at least 10 characters"}),availability:d.string().min(1,{message:"Please select availability option"}),price:d.string().optional(),rentalPrice:d.string().optional(),rentalPeriod:d.string().optional(),securityDepositRequired:d.boolean().optional().default(!1),securityDepositAmount:d.string().optional()}),Ce=()=>{const R=te(),[s,P]=w.useState(!1);w.useState("");const[f,D]=w.useState([]),[I,L]=w.useState([]),[N,C]=w.useState(0),[E,U]=w.useState(0),[W,B]=w.useState(!1),[T,H]=w.useState(null),[O,V]=w.useState(null),{currentUser:a,userData:v}=re(),z=i=>{const o=i.target.files;if(!o)return;if(f.length+o.length>4){p.error("Maximum 4 images allowed");return}const c=Array.from(o),l=[...f,...c];D(l);const r=c.map(y=>URL.createObjectURL(y)),j=[...I,...r];L(j),f.length===0&&c.length>0&&C(0)},J=i=>{const o=[...f],c=[...I];o.splice(i,1),c.splice(i,1),D(o),L(c),i===N?(o.length>0,C(0)):i<N&&C(N-1)},X=i=>{C(i)},K=async()=>{B(!0),H(null);try{console.log("Attempting to capture GPS location...");const i=await xe({enableHighAccuracy:!0,timeout:15e3,maximumAge:6e4});return console.log("GPS location captured successfully:",i),V(i),i}catch(i){console.error("Error capturing GPS location:",i);let o="Unable to get your current location.";return i instanceof Error&&(i.message.includes("permission")?o="Location permission denied. Using your registered address instead.":i.message.includes("timeout")?o="Location request timed out. Using your registered address instead.":i.message.includes("unavailable")&&(o="Location service unavailable. Using your registered address instead.")),H(o),null}finally{B(!1)}},Q=async()=>{if(!(v!=null&&v.pincode))return console.log("No pincode available for fallback location"),null;try{const o={500001:{latitude:17.385,longitude:78.4867},500032:{latitude:17.4399,longitude:78.3489},500081:{latitude:17.4485,longitude:78.3908},400001:{latitude:18.9322,longitude:72.8264},400051:{latitude:19.0596,longitude:72.8295},110001:{latitude:28.6139,longitude:77.209},110016:{latitude:28.5494,longitude:77.2001},560001:{latitude:12.9716,longitude:77.5946},560066:{latitude:12.9698,longitude:77.75},600001:{latitude:13.0827,longitude:80.2707},600028:{latitude:13.0569,longitude:80.2091}}[v.pincode];if(o)return console.log("Fallback location from pincode mapping:",o),o;const c=v.pincode.substring(0,2),r={50:{latitude:17.385,longitude:78.4867},40:{latitude:19.076,longitude:72.8777},11:{latitude:28.7041,longitude:77.1025},56:{latitude:12.9716,longitude:77.5946},60:{latitude:13.0827,longitude:80.2707},70:{latitude:22.5726,longitude:88.3639},30:{latitude:26.9124,longitude:75.7873},22:{latitude:26.8467,longitude:80.9462}}[c];return r?(console.log("Fallback location from state mapping:",r),r):(console.log("No fallback location available for pincode:",v.pincode),null)}catch(i){return console.error("Error getting fallback location from pincode:",i),null}},Z=async()=>{if(!(a!=null&&a.uid))return console.log("No current user available for community retrieval"),null;try{console.log("Retrieving user community from Firestore..."),await ge();const{doc:i,getDoc:o,getFirestore:c}=await $(async()=>{const{doc:y,getDoc:x,getFirestore:S}=await import("./index.esm-ehpEbksy.js");return{doc:y,getDoc:x,getFirestore:S}},__vite__mapDeps([0,1])),l=c(),r=i(l,"users",a.uid),j=await o(r);if(j.exists()){const x=j.data().community;return x&&typeof x=="string"&&x.trim()!==""?(console.log("User community retrieved successfully:",x),x.trim()):(console.log("User community is empty or not set"),null)}else return console.log("User document not found in Firestore"),null}catch(i){return console.error("Error retrieving user community:",i),null}},t=se({resolver:me(je),defaultValues:{title:"",author:"",isbn:"",genre:"",condition:"",description:"",availability:"",price:"",rentalPrice:"",rentalPeriod:"per week",securityDepositRequired:!1,securityDepositAmount:""}}),ee=async i=>{var o,c;P(!0);try{console.log("Adding book:",i);const{createBook:l}=await $(async()=>{const{createBook:n}=await import("./index-Bm_kDzMk.js").then(oe=>oe.bi);return{createBook:n}},__vite__mapDeps([2,3]));if(!a){p.error("You must be signed in to add a book"),P(!1);return}console.log("Capturing location for book listing...");let r=null;r=await K(),r||(console.log("GPS capture failed, trying fallback location..."),r=await Q()),r?console.log("Location captured for book:",r):console.log("No location could be determined for book"),console.log("Retrieving user community for book listing...");let j=null;try{j=await Z(),j?(console.log("✅ User community retrieved successfully for book:",j),p.info(`Community "${j}" will be added to your book listing`)):(console.warn("⚠️ No community information available for user - book will be created without community data"),p.warning("No community information found in your profile. Consider updating your profile to help others find books in your area."))}catch(n){console.error("❌ Error retrieving user community:",n),console.error("Community error details:",{message:n.message,stack:n.stack,currentUser:a==null?void 0:a.uid,timestamp:new Date().toISOString()}),p.error("Failed to retrieve community information. Your book will be listed without community data.")}let y=[];try{y=i.genre.split(",").map(n=>n.trim()).filter(n=>n.length>0),y.length===0&&(y=[i.genre.trim()])}catch(n){console.error("Error processing genre:",n),y=[i.genre.trim()]}let x=null;if(i.price&&(x=Number(i.price),isNaN(x))){p.error("Invalid price value. Please enter a valid number."),P(!1);return}let S=null;if(i.rentalPrice&&(S=Number(i.rentalPrice),isNaN(S))){p.error("Invalid rental price value. Please enter a valid number."),P(!1);return}let q=i.rentalPeriod;S||(q=null);let A=null;if(i.securityDepositRequired&&i.securityDepositAmount&&(A=Number(i.securityDepositAmount),isNaN(A))){p.error("Invalid security deposit amount. Please enter a valid number."),P(!1);return}let k=[],G="https://via.placeholder.com/150?text=No+Image";if(f.length>0)try{p.info("Uploading images..."),k=await pe(f,a.uid,n=>{U(n),console.log(`Upload progress: ${n}%`)}),console.log("Uploaded image URLs:",k),k.length>0&&(G=k[N]||k[0])}catch(n){console.error("Error uploading images:",n),p.error("Failed to upload images. Using default image instead.")}const M={title:i.title.trim(),author:i.author.trim(),isbn:((o=i.isbn)==null?void 0:o.trim())||null,genre:y,condition:i.condition,description:i.description.trim(),imageUrl:G,imageUrls:k.length>0?k:void 0,displayImageIndex:k.length>0?N:void 0,availability:i.availability,price:x,rentalPrice:S,rentalPeriod:q,securityDepositRequired:i.securityDepositRequired,securityDepositAmount:A,ownerId:a.uid,ownerName:a.displayName||((c=a.email)==null?void 0:c.split("@")[0])||"Unknown",ownerEmail:a.email||void 0,ownerCommunity:j||void 0,ownerCoordinates:r,ownerPincode:(v==null?void 0:v.pincode)||void 0,ownerRating:0,perceivedValue:5};console.log("Prepared book data:",M);const ie=await l(M);console.log("Book created successfully with ID:",ie),p.success("Book added successfully! It will be visible after admin approval."),R("/browse")}catch(l){console.error("Error adding book:",l);let r="Failed to add book. Please try again.";l instanceof Error&&(r=`Error: ${l.message}`,console.error("Error details:",l.message),l.message.includes("permission-denied")?r="You don't have permission to add books. Please check your account.":l.message.includes("network")?r="Network error. Please check your internet connection and try again.":l.message.includes("quota-exceeded")&&(r="Database quota exceeded. Please try again later.")),p.error(r)}finally{P(!1),U(0)}};return e.jsxs("div",{className:"min-h-screen flex flex-col",children:[e.jsx(ne,{}),e.jsx("main",{className:"flex-grow",children:e.jsx("div",{className:"container mx-auto px-4 py-8 max-w-2xl",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800 font-playfair mb-2",children:"Add Your Book"}),e.jsx("p",{className:"text-gray-600",children:"Share your book with the community"})]}),e.jsx(le,{...t,children:e.jsxs("form",{onSubmit:t.handleSubmit(ee),className:"space-y-6",children:[e.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsx(u,{control:t.control,name:"title",render:({field:i})=>e.jsxs(m,{children:[e.jsx(g,{children:"Book Title*"}),e.jsx(h,{children:e.jsx(F,{placeholder:"Enter book title",disabled:s,...i})}),e.jsx(b,{})]})}),e.jsx(u,{control:t.control,name:"author",render:({field:i})=>e.jsxs(m,{children:[e.jsx(g,{children:"Author*"}),e.jsx(h,{children:e.jsx(F,{placeholder:"Enter author name",disabled:s,...i})}),e.jsx(b,{})]})}),e.jsx(u,{control:t.control,name:"isbn",render:({field:i})=>e.jsxs(m,{children:[e.jsx(g,{children:"ISBN (Optional)"}),e.jsx(h,{children:e.jsx(F,{placeholder:"Enter ISBN",disabled:s,...i})}),e.jsx(b,{})]})}),e.jsx(u,{control:t.control,name:"genre",render:({field:i})=>e.jsxs(m,{children:[e.jsx(g,{children:"Genre*"}),e.jsx(h,{children:e.jsxs("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",disabled:s,...i,children:[e.jsx("option",{value:"Fantasy",children:"Fantasy"}),e.jsx("option",{value:"Science Fiction",children:"Science Fiction"}),e.jsx("option",{value:"Mystery",children:"Mystery"}),e.jsx("option",{value:"Thriller",children:"Thriller"}),e.jsx("option",{value:"Horror",children:"Horror"}),e.jsx("option",{value:"Romance",children:"Romance"}),e.jsx("option",{value:"Comedy",children:"Comedy"}),e.jsx("option",{value:"Drama",children:"Drama"}),e.jsx("option",{value:"Historical Fiction",children:"Historical Fiction"}),e.jsx("option",{value:"Paranormal",children:"Paranormal"}),e.jsx("option",{value:"Adventure",children:"Adventure"}),e.jsx("option",{value:"Action",children:"Action"}),e.jsx("option",{value:"Western",children:"Western"}),e.jsx("option",{value:"Literary Fiction",children:"Literary Fiction"}),e.jsx("option",{value:"Dystopian",children:"Dystopian"}),e.jsx("option",{value:"Coming-of-Age",children:"Coming-of-Age"}),e.jsx("option",{value:"Young Adult (YA)",children:"Young Adult (YA)"}),e.jsx("option",{value:"Children’s",children:"Children’s"}),e.jsx("option",{value:"Biography",children:"Biography"}),e.jsx("option",{value:"Memoir",children:"Memoir"}),e.jsx("option",{value:"Self-Help",children:"Self-Help"}),e.jsx("option",{value:"Psychology",children:"Psychology"}),e.jsx("option",{value:"Philosophy",children:"Philosophy"}),e.jsx("option",{value:"Business",children:"Business"}),e.jsx("option",{value:"Finance",children:"Finance"}),e.jsx("option",{value:"Leadership",children:"Leadership"}),e.jsx("option",{value:"Science",children:"Science"}),e.jsx("option",{value:"Technology",children:"Technology"}),e.jsx("option",{value:"History",children:"History"}),e.jsx("option",{value:"Politics",children:"Politics"}),e.jsx("option",{value:"Cooking",children:"Cooking"}),e.jsx("option",{value:"Travel",children:"Travel"}),e.jsx("option",{value:"Health & Wellness",children:"Health & Wellness"}),e.jsx("option",{value:"Religion",children:"Religion"}),e.jsx("option",{value:"Spirituality",children:"Spirituality"}),e.jsx("option",{value:"Parenting",children:"Parenting"}),e.jsx("option",{value:"Home & Garden",children:"Home & Garden"}),e.jsx("option",{value:"Art & Design",children:"Art & Design"}),e.jsx("option",{value:"Graphic Novel",children:"Graphic Novel"}),e.jsx("option",{value:"Comic Book",children:"Comic Book"}),e.jsx("option",{value:"Manga",children:"Manga"}),e.jsx("option",{value:"Classic",children:"Classic"}),e.jsx("option",{value:"Poetry",children:"Poetry"}),e.jsx("option",{value:"Essays",children:"Essays"}),e.jsx("option",{value:"Anthology",children:"Anthology"}),e.jsx("option",{value:"Short Stories",children:"Short Stories"}),e.jsx("option",{value:"Education",children:"Education"}),e.jsx("option",{value:"Reference",children:"Reference"}),e.jsx("option",{value:"True Crime",children:"True Crime"}),e.jsx("option",{value:"Inspirational",children:"Inspirational"})]})}),e.jsx(b,{})]})}),e.jsx(u,{control:t.control,name:"condition",render:({field:i})=>e.jsxs(m,{children:[e.jsx(g,{children:"Condition*"}),e.jsx(h,{children:e.jsxs("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",disabled:s,...i,children:[e.jsx("option",{value:"",children:"Select Condition"}),e.jsx("option",{value:"New",children:"New"}),e.jsx("option",{value:"Like New",children:"Like New"}),e.jsx("option",{value:"Good",children:"Good"}),e.jsx("option",{value:"Fair",children:"Fair"})]})}),e.jsx(b,{})]})})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6",children:[e.jsxs("div",{className:"flex flex-col items-center justify-center mb-4",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Book Images"}),e.jsx("p",{className:"text-sm text-gray-500 mb-2 text-center",children:"Upload up to 4 images of your book. The first image will be the display image."}),e.jsxs("div",{className:"flex items-center justify-center mb-2",children:[e.jsxs("span",{className:"text-sm font-medium text-gray-700 mr-2",children:[f.length,"/4 images"]}),E>0&&E<100&&e.jsx("div",{className:"w-24 h-2 bg-gray-200 rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full bg-burgundy-500",style:{width:`${E}%`}})})]})]}),I.length>0&&e.jsx("div",{className:"grid grid-cols-2 gap-4 mb-4",children:I.map((i,o)=>e.jsxs("div",{className:`relative border rounded-md overflow-hidden ${o===N?"ring-2 ring-burgundy-500":""}`,children:[e.jsx("img",{src:i,alt:`Book image ${o+1}`,className:"w-full h-32 object-contain"}),e.jsxs("div",{className:"absolute top-0 right-0 p-1 flex space-x-1",children:[o!==N&&e.jsx("button",{type:"button",className:"bg-burgundy-500 text-white p-1 rounded-full hover:bg-burgundy-600",onClick:()=>X(o),title:"Set as display image",children:e.jsx(_,{className:"h-4 w-4"})}),e.jsx("button",{type:"button",className:"bg-gray-700 text-white p-1 rounded-full hover:bg-gray-800",onClick:()=>J(o),title:"Remove image",children:e.jsx(ae,{className:"h-4 w-4"})})]}),o===N&&e.jsx("div",{className:"absolute bottom-0 left-0 right-0 bg-burgundy-500 text-white text-xs py-1 text-center",children:"Display Image"})]},o))}),f.length<4&&e.jsxs("div",{className:"flex justify-center",children:[e.jsx("input",{type:"file",id:"image-upload",className:"hidden",accept:"image/*",multiple:f.length<3,onChange:z,disabled:s}),e.jsxs("label",{htmlFor:"image-upload",className:`flex items-center justify-center text-sm bg-burgundy-500 text-white px-4 py-2 rounded-md cursor-pointer hover:bg-burgundy-600 ${s?"opacity-50 cursor-not-allowed":""}`,children:[e.jsx(be,{className:"h-4 w-4 mr-2"}),f.length===0?"Upload Images":"Add More Images"]})]})]}),e.jsx(u,{control:t.control,name:"availability",render:({field:i})=>e.jsxs(m,{children:[e.jsx(g,{children:"Availability*"}),e.jsx(h,{children:e.jsxs("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",disabled:s,onChange:o=>{i.onChange(o)},value:i.value,children:[e.jsx("option",{value:"",children:"Select Availability"}),e.jsx("option",{value:"For Exchange",children:"For Exchange"}),e.jsx("option",{value:"For Sale",children:"For Sale"}),e.jsx("option",{value:"For Rent",children:"For Rent"}),e.jsx("option",{value:"For Sale & Exchange",children:"For Sale & Exchange"}),e.jsx("option",{value:"For Rent & Exchange",children:"For Rent & Exchange"}),e.jsx("option",{value:"For Rent & Sale",children:"For Rent & Sale"}),e.jsx("option",{value:"For Rent, Sale & Exchange",children:"For Rent, Sale & Exchange"})]})}),e.jsx(b,{})]})}),t.watch("availability")&&(t.watch("availability").includes("Sale")||t.watch("availability").includes("Rent"))&&e.jsxs("div",{className:"space-y-6",children:[t.watch("availability").includes("Sale")&&e.jsx(u,{control:t.control,name:"price",render:({field:i})=>e.jsxs(m,{children:[e.jsx(g,{children:"Sale Price (₹)"}),e.jsx(h,{children:e.jsx(F,{type:"number",placeholder:"Enter price",disabled:s,...i})}),e.jsx(b,{})]})}),t.watch("availability").includes("Rent")&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx(u,{control:t.control,name:"rentalPrice",render:({field:i})=>e.jsxs(m,{children:[e.jsx(g,{children:"Rental Price (₹)"}),e.jsx(h,{children:e.jsx(F,{type:"number",placeholder:"Enter rental price",disabled:s,...i})}),e.jsx(b,{})]})}),e.jsx(u,{control:t.control,name:"rentalPeriod",render:({field:i})=>e.jsxs(m,{children:[e.jsx(g,{children:"Period"}),e.jsx(h,{children:e.jsxs("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",disabled:s,...i,children:[e.jsx("option",{value:"per day",children:"Per Day"}),e.jsx("option",{value:"per week",children:"Per Week"}),e.jsx("option",{value:"per month",children:"Per Month"})]})}),e.jsx(b,{})]})})]}),e.jsx("div",{className:"mt-4",children:e.jsx(u,{control:t.control,name:"securityDepositRequired",render:({field:i})=>e.jsxs(m,{className:"flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4",children:[e.jsx(h,{children:e.jsx("input",{type:"checkbox",className:"h-4 w-4 mt-1",checked:i.value,onChange:o=>i.onChange(o.target.checked),disabled:s})}),e.jsxs("div",{className:"space-y-1 leading-none",children:[e.jsx(g,{children:"Security Deposit Required"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Require a security deposit for renting this book"})]})]})})}),t.watch("securityDepositRequired")&&e.jsx("div",{className:"mt-4",children:e.jsx(u,{control:t.control,name:"securityDepositAmount",render:({field:i})=>e.jsxs(m,{children:[e.jsx(g,{children:"Security Deposit Amount (₹)"}),e.jsx(h,{children:e.jsx(F,{type:"number",placeholder:"Enter security deposit amount",disabled:s,...i})}),e.jsx(b,{})]})})})]})]})]})]}),e.jsx(u,{control:t.control,name:"description",render:({field:i})=>e.jsxs(m,{children:[e.jsx(g,{children:"Description*"}),e.jsx(h,{children:e.jsx(he,{placeholder:"Describe the book, its condition, and any other details...",className:"min-h-[120px]",disabled:s,...i})}),e.jsx(b,{})]})}),e.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(ce,{className:"h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-sm font-medium text-blue-900 mb-2",children:"Location Information"}),e.jsx("p",{className:"text-sm text-blue-800 mb-2",children:"When you submit this book listing, we will automatically capture your current GPS location to help other users find books near them. This location data will be used solely for:"}),e.jsxs("ul",{className:"text-sm text-blue-800 list-disc list-inside space-y-1 mb-3",children:[e.jsx("li",{children:"Calculating and displaying distance to other users browsing books"}),e.jsx("li",{children:"Helping users find books in their local area"}),e.jsx("li",{children:"Improving the book discovery experience"})]}),e.jsx("p",{className:"text-sm text-blue-800",children:"If GPS location cannot be accessed, we'll use your registered address as a fallback. Your exact location will not be displayed to other users - only the calculated distance."}),W&&e.jsxs("div",{className:"mt-3 flex items-center space-x-2 text-sm text-blue-700",children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),e.jsx("span",{children:"Capturing your location..."})]}),O&&e.jsxs("div",{className:"mt-3 flex items-center space-x-2 text-sm text-green-700",children:[e.jsx(_,{className:"h-4 w-4 text-green-600"}),e.jsx("span",{children:"Location captured successfully"})]}),T&&e.jsxs("div",{className:"mt-3 flex items-start space-x-2 text-sm text-amber-700",children:[e.jsx(fe,{className:"h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:T})]})]})]})}),e.jsxs("div",{className:"flex justify-end space-x-4",children:[e.jsx(Y,{type:"button",variant:"outline",disabled:s,onClick:()=>R("/"),children:"Cancel"}),e.jsxs(Y,{type:"submit",disabled:s,className:"flex items-center gap-2",children:[e.jsx(de,{className:"h-4 w-4"}),"Add Book"]})]})]})})]})})}),e.jsx(ue,{})]})};export{Ce as default};
