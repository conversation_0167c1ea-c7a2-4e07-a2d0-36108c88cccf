
import { GeoCoordinates } from '@/lib/geolocationUtils';

export type BookCondition = 'New' | 'Like New' | 'Good' | 'Fair';

export type BookAvailability = 'For Rent' | 'For Exchange' | 'For Sale' | 'For Rent & Sale' |
  'For Rent & Exchange' | 'For Sale & Exchange' | 'For Rent, Sale & Exchange';

export interface Book {
  id: string;
  title: string;
  author: string;
  isbn?: string;
  genre: string[];
  condition: BookCondition;
  description: string;
  imageUrl: string;
  imageUrls?: string[]; // Array of all image URLs
  displayImageIndex?: number; // Index of the image to use as display image
  perceivedValue: number; // Scale of 1-10
  price?: number; // In currency (if for sale)
  rentalPrice?: number; // In currency (if for rent)
  rentalPeriod?: string; // e.g., "per week", "per month"
  securityDepositRequired?: boolean; // Whether security deposit is required for rental
  securityDepositAmount?: number; // Amount of security deposit in currency
  availability: BookAvailability;
  ownerId: string;
  ownerName: string;
  ownerEmail?: string; // Owner's email address
  ownerLocation?: string; // Legacy field for backward compatibility
  ownerCommunity?: string; // Owner's community name (primary location identifier)
  ownerCoordinates?: GeoCoordinates; // Owner's GPS coordinates
  ownerPincode?: string; // Owner's pincode/postal code
  ownerRating: number;
  distance?: number; // Distance from user in km
  createdAt: Date;
  approvalStatus?: BookApprovalStatus; // Book approval status
  rejectionReason?: string; // Reason for rejection if applicable
  status?: BookStatus; // Current availability status (Available, Sold Out, Rented Out)
  nextAvailableDate?: Date; // Expected return/availability date for rented books
}

export interface User {
  id: string;
  name: string;
  email: string;
  location: string;
  avatar?: string;
  rating: number;
  reviewCount: number;
  joinedDate: Date;
  wishlist: string[]; // Book IDs
  ownedBooks: string[]; // Book IDs
  verified: boolean;
}

export enum UserRole {
  User = 'user',
  Admin = 'admin'
}

export enum BookApprovalStatus {
  Pending = 'pending',
  Approved = 'approved',
  Rejected = 'rejected'
}

export type BookStatus = 'Available' | 'Sold Out' | 'Rented Out';

export interface BookEditFormData {
  title: string;
  author: string;
  isbn?: string;
  genre: string[];
  condition: BookCondition;
  description: string;
  availability: BookAvailability;
  price?: number;
  rentalPrice?: number;
  rentalPeriod?: string;
  securityDepositRequired?: boolean;
  securityDepositAmount?: number;
  imageUrls?: string[];
  status?: BookStatus;
  nextAvailableDate?: Date;
}

// Blog-related types and interfaces
export enum BlogStatus {
  Draft = 'draft',
  Published = 'published'
}

export interface Blog {
  id: string;
  title: string;
  content: string;
  tags: string[];
  published: boolean;
  status: BlogStatus;
  createdAt: Date;
  updatedAt: Date;
  authorId: string;
  authorName: string;
  authorEmail: string;
  slug?: string; // URL-friendly version of title
  excerpt?: string; // Short description/preview
  readTime?: number; // Estimated reading time in minutes
  views?: number; // View count
  coverImageUrl?: string; // Cover image URL from Firebase Storage
}

export interface BlogFormData {
  title: string;
  content: string;
  tags: string[];
  published: boolean;
  excerpt?: string;
  coverImageUrl?: string;
}

export interface CreateBlogData {
  title: string;
  content: string;
  tags: string[];
  published: boolean;
  authorId: string;
  authorName: string;
  authorEmail: string;
  excerpt?: string;
  coverImageUrl?: string;
}

export interface UpdateBlogData {
  title?: string;
  content?: string;
  tags?: string[];
  published?: boolean;
  excerpt?: string;
  coverImageUrl?: string;
}

// Published blog post interface for the blogPosts collection
export interface PublishedBlogPost {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  author: string;
  tags: string[];
  coverImageUrl?: string;
  published: boolean; // Always true for blogPosts collection
  createdAt: Date;
  updatedAt: Date;
  slug: string;
  readTime: number;
}

export interface CreatePublishedBlogData {
  title: string;
  content: string;
  excerpt?: string;
  author: string;
  tags: string[];
  coverImageUrl?: string;
}
