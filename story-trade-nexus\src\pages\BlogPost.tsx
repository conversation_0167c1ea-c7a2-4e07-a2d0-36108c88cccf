import React, { useEffect, useState, useCallback } from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { marked } from 'marked';
import DOMPurify from 'dompurify';
import MainLayout from '@/components/layouts/MainLayout';
import { PublishedBlogPost } from '@/types';
import { getPublishedBlogPostBySlug, getAdjacentBlogPosts, searchBlogPostByTitle } from '@/lib/blogService';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, Clock, User, ArrowLeft, ChevronLeft, ChevronRight, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';

// Loading skeleton for individual blog post
const BlogPostSkeleton: React.FC = () => (
  <div className="animate-pulse">
    {/* Hero Section Skeleton */}
    <div className="bg-beige-50 py-16">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <Skeleton className="h-12 w-3/4 mb-6" />
          <div className="flex items-center space-x-4 mb-8">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-4 w-20" />
          </div>
          <div className="flex flex-wrap gap-2">
            <Skeleton className="h-6 w-16" />
            <Skeleton className="h-6 w-20" />
            <Skeleton className="h-6 w-14" />
          </div>
        </div>
      </div>
    </div>

    {/* Content Skeleton */}
    <div className="container mx-auto px-4 py-12">
      <div className="max-w-4xl mx-auto">
        <Skeleton className="h-64 w-full mb-8" />
        <div className="space-y-4">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-5/6" />
          <Skeleton className="h-4 w-4/5" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
        </div>
      </div>
    </div>
  </div>
);

// 404 Error component for non-existent blog posts
const BlogPostNotFound: React.FC = () => (
  <MainLayout>
    <div className="min-h-screen bg-beige-50 flex items-center justify-center">
      <div className="text-center py-12">
        <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
        <h1 className="text-4xl font-playfair font-bold text-navy-800 mb-4">Blog Post Not Found</h1>
        <p className="text-gray-600 mb-8 max-w-md mx-auto">
          The blog post you're looking for doesn't exist or may have been moved.
        </p>
        <Link
          to="/blog"
          className="inline-flex items-center bg-burgundy-600 hover:bg-burgundy-700 text-white px-6 py-3 rounded-lg transition-colors duration-200"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Blog
        </Link>
      </div>
    </div>
  </MainLayout>
);

const BlogPost: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const [post, setPost] = useState<PublishedBlogPost | null>(null);
  const [adjacentPosts, setAdjacentPosts] = useState<{
    previous: PublishedBlogPost | null;
    next: PublishedBlogPost | null;
  }>({ previous: null, next: null });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Markdown rendering function with sanitization
  const renderMarkdown = useCallback((content: string) => {
    try {
      const rawHtml = marked(content);
      return DOMPurify.sanitize(rawHtml as string);
    } catch (error) {
      console.error('Error rendering markdown:', error);
      return '<p class="text-red-500">Error rendering content</p>';
    }
  }, []);

  // Format date for display
  const formatDate = useCallback((date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }, []);

  // Fetch blog post and adjacent posts
  const fetchBlogPost = useCallback(async () => {
    if (!slug) {
      setError('No blog post slug provided');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      console.log(`Fetching blog post with slug: ${slug}`);
      
      // Fetch the blog post
      let blogPost = await getPublishedBlogPostBySlug(slug);

      // If not found by slug, try searching by title as fallback
      if (!blogPost) {
        console.log(`Blog post not found by slug: ${slug}, trying title search...`);

        // Convert slug back to potential title for search
        const potentialTitle = slug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        blogPost = await searchBlogPostByTitle(potentialTitle);

        if (blogPost) {
          console.log(`Found blog post by title search: ${blogPost.title}`);
          // Redirect to correct URL with proper slug if found
          if (blogPost.slug && blogPost.slug !== slug) {
            navigate(`/blog/${blogPost.slug}`, { replace: true });
            return;
          }
        }
      }

      if (!blogPost) {
        setError('Blog post not found');
        setLoading(false);
        return;
      }

      setPost(blogPost);
      
      // Fetch adjacent posts for navigation
      const adjacent = await getAdjacentBlogPosts(slug);
      setAdjacentPosts(adjacent);
      
      console.log(`Successfully loaded blog post: ${blogPost.title}`);
    } catch (err) {
      console.error('Error fetching blog post:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to load blog post';
      setError(errorMessage);
      toast.error('Failed to load blog post. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [slug]);

  useEffect(() => {
    fetchBlogPost();
  }, [fetchBlogPost]);

  // Set SEO meta tags
  useEffect(() => {
    if (post) {
      // Set page title
      document.title = `${post.title} - PeerBooks Blog`;
      
      // Update meta description
      let metaDescription = document.querySelector('meta[name="description"]');
      if (!metaDescription) {
        metaDescription = document.createElement('meta');
        metaDescription.setAttribute('name', 'description');
        document.head.appendChild(metaDescription);
      }
      const description = post.excerpt || post.content.substring(0, 160) + '...';
      metaDescription.setAttribute('content', description);

      // Add Open Graph tags
      const updateOrCreateMetaTag = (property: string, content: string) => {
        let metaTag = document.querySelector(`meta[property="${property}"]`);
        if (!metaTag) {
          metaTag = document.createElement('meta');
          metaTag.setAttribute('property', property);
          document.head.appendChild(metaTag);
        }
        metaTag.setAttribute('content', content);
      };

      updateOrCreateMetaTag('og:title', post.title);
      updateOrCreateMetaTag('og:description', description);
      updateOrCreateMetaTag('og:type', 'article');
      updateOrCreateMetaTag('og:url', window.location.href);
      if (post.coverImageUrl) {
        updateOrCreateMetaTag('og:image', post.coverImageUrl);
      }

      // Add canonical URL
      let canonicalLink = document.querySelector('link[rel="canonical"]');
      if (!canonicalLink) {
        canonicalLink = document.createElement('link');
        canonicalLink.setAttribute('rel', 'canonical');
        document.head.appendChild(canonicalLink);
      }
      canonicalLink.setAttribute('href', window.location.href);
    }

    // Cleanup function
    return () => {
      document.title = 'PeerBooks - Share Books, Build Community';
    };
  }, [post]);

  // Handle loading state
  if (loading) {
    return (
      <MainLayout>
        <BlogPostSkeleton />
      </MainLayout>
    );
  }

  // Handle error state (404)
  if (error || !post) {
    return <BlogPostNotFound />;
  }

  return (
    <MainLayout>
      <article className="min-h-screen bg-beige-50">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-burgundy-600 to-burgundy-700 text-white py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              {/* Back to Blog Link */}
              <Link
                to="/blog"
                className="inline-flex items-center text-burgundy-100 hover:text-white mb-6 transition-colors duration-200"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Blog
              </Link>

              {/* Title */}
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-playfair font-bold mb-6 leading-tight">
                {post.title}
              </h1>

              {/* Metadata */}
              <div className="flex flex-wrap items-center gap-6 text-burgundy-100 mb-6">
                <div className="flex items-center">
                  <User className="h-5 w-5 mr-2" />
                  <span>By {post.author}</span>
                </div>
                <div className="flex items-center">
                  <Calendar className="h-5 w-5 mr-2" />
                  <span>{formatDate(post.createdAt)}</span>
                </div>
                <div className="flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  <span>{post.readTime} min read</span>
                </div>
              </div>

              {/* Tags */}
              {post.tags && post.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {post.tags.map((tag: string, index: number) => (
                    <Badge
                      key={index}
                      variant="secondary"
                      className="bg-burgundy-500 bg-opacity-20 text-white border-burgundy-300 hover:bg-opacity-30 transition-colors duration-200"
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          <div className="max-w-4xl mx-auto">
            {/* Cover Image */}
            {post.coverImageUrl && (
              <div className="mb-8">
                <img
                  src={post.coverImageUrl}
                  alt={post.title}
                  className="w-full h-64 md:h-96 object-cover rounded-lg shadow-lg"
                  loading="eager"
                />
              </div>
            )}

            {/* Excerpt */}
            {post.excerpt && (
              <div className="bg-white rounded-lg p-6 mb-8 border-l-4 border-burgundy-600">
                <p className="text-lg text-gray-700 italic leading-relaxed">
                  {post.excerpt}
                </p>
              </div>
            )}

            {/* Blog Content */}
            <div className="bg-white rounded-lg shadow-md p-8 mb-8">
              <div
                className="prose prose-lg max-w-none prose-headings:text-navy-800 prose-a:text-burgundy-600 prose-strong:text-navy-700 prose-blockquote:border-burgundy-300 prose-blockquote:text-gray-700 prose-code:text-burgundy-600 prose-pre:bg-gray-50"
                dangerouslySetInnerHTML={{
                  __html: renderMarkdown(post.content)
                }}
              />
            </div>

            {/* Navigation to Previous/Next Posts */}
            {(adjacentPosts.previous || adjacentPosts.next) && (
              <div className="bg-white rounded-lg shadow-md p-6 mb-8">
                <h3 className="text-lg font-playfair font-bold text-navy-800 mb-4">More Articles</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Previous Post */}
                  {adjacentPosts.previous && (
                    <Link
                      to={`/blog/${adjacentPosts.previous.slug}`}
                      className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-burgundy-300 hover:bg-burgundy-50 transition-all duration-200 group"
                    >
                      <ChevronLeft className="h-5 w-5 text-burgundy-600 mr-3 group-hover:translate-x-[-2px] transition-transform duration-200" />
                      <div className="flex-1 min-w-0">
                        <p className="text-xs text-gray-500 uppercase tracking-wide mb-1">Previous</p>
                        <p className="text-sm font-medium text-navy-800 truncate">
                          {adjacentPosts.previous.title}
                        </p>
                      </div>
                    </Link>
                  )}

                  {/* Next Post */}
                  {adjacentPosts.next && (
                    <Link
                      to={`/blog/${adjacentPosts.next.slug}`}
                      className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-burgundy-300 hover:bg-burgundy-50 transition-all duration-200 group md:justify-end"
                    >
                      <div className="flex-1 min-w-0 md:text-right">
                        <p className="text-xs text-gray-500 uppercase tracking-wide mb-1">Next</p>
                        <p className="text-sm font-medium text-navy-800 truncate">
                          {adjacentPosts.next.title}
                        </p>
                      </div>
                      <ChevronRight className="h-5 w-5 text-burgundy-600 ml-3 group-hover:translate-x-[2px] transition-transform duration-200" />
                    </Link>
                  )}
                </div>
              </div>
            )}

            {/* Back to Blog Button */}
            <div className="text-center">
              <Link
                to="/blog"
                className="inline-flex items-center bg-burgundy-600 hover:bg-burgundy-700 text-white px-6 py-3 rounded-lg transition-colors duration-200"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to All Posts
              </Link>
            </div>
          </div>
        </div>

        {/* Structured Data for SEO */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "BlogPosting",
              "headline": post.title,
              "description": post.excerpt || post.content.substring(0, 160),
              "image": post.coverImageUrl || "",
              "author": {
                "@type": "Person",
                "name": post.author
              },
              "publisher": {
                "@type": "Organization",
                "name": "PeerBooks",
                "logo": {
                  "@type": "ImageObject",
                  "url": `${window.location.origin}/logo.png`
                }
              },
              "datePublished": post.createdAt.toISOString(),
              "dateModified": post.updatedAt.toISOString(),
              "mainEntityOfPage": {
                "@type": "WebPage",
                "@id": window.location.href
              }
            })
          }}
        />
      </article>
    </MainLayout>
  );
};

export default BlogPost;
