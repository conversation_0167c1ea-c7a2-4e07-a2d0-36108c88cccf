import{y as ne,z as o,r as t,aq as ie,h as oe,j as e,x as n,ar as ce,N as H,A as y,W as de,a5 as S,a3 as me,i as xe,k as d,l as m,m as x,n as h,I as g,o as p,as as he,at as pe,a7 as je,J as j,au as ge,v as ue,av as fe,aw as ye}from"./index-Bm_kDzMk.js";import{D as E,a as O,b as I,c as z,d as T,e as M}from"./dialog-DGwTPBRT.js";import{A as Ne,a as be,b as we,c as ve,d as Ae,e as De,f as Ce,g as Se}from"./alert-dialog-DalAdHKY.js";import{S as B,a as W,b as Z,c as _,d as L}from"./select-BZgYO6JR.js";import{A as Pe}from"./AdminLayout-BhV0nOiW.js";import{S as Ue}from"./shield-BmG7fYuo.js";import{T as ke}from"./trash-2-lEwRhIcV.js";import{E as Fe}from"./eye-off-DwKNGezW.js";import{E as Re}from"./eye-BoIGsHz8.js";import"./index-Cr71giPO.js";import"./chevron-up-B0Gx3VCi.js";import"./users-vJ223Oqr.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ee=ne("ShieldOff",[["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M5 5a1 1 0 0 0-1 1v7c0 5 3.5 7.5 7.67 8.94a1 1 0 0 0 .67.01c2.35-.82 4.48-1.97 5.9-3.71",key:"1jlk70"}],["path",{d:"M9.309 3.652A12.252 12.252 0 0 0 11.24 2.28a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1v7a9.784 9.784 0 0 1-.08 1.264",key:"18rp1v"}]]),Oe=o.object({email:o.string().email({message:"Please enter a valid email address"}),password:o.string().min(8,{message:"Password must be at least 8 characters"}).refine(c=>{const u=/[A-Z]/.test(c),f=/[a-z]/.test(c),N=/[0-9]/.test(c),b=/[!@#$%^&*(),.?":{}|<>]/.test(c);return u&&f&&N&&b},{message:"Password must include uppercase, lowercase, number, and special character"}),displayName:o.string().min(2,{message:"Display name must be at least 2 characters"}),phone:o.string().min(10,{message:"Please enter a valid phone number"}).max(15).optional(),address:o.string().optional(),apartment:o.string().optional(),city:o.string().optional(),state:o.string().optional(),pincode:o.string().min(6,{message:"Please enter a valid pincode"}).max(6).optional(),role:o.enum(["user","admin"],{required_error:"Please select a role"})}),Ze=()=>{const[c,u]=t.useState([]),[f,N]=t.useState(!0),[b,V]=t.useState(null),[a,P]=t.useState(null),[G,w]=t.useState(!1),[K,v]=t.useState(!1),[Q,A]=t.useState(!1),[X,U]=t.useState(!1),[D,C]=t.useState(!1),[l,q]=t.useState(!1),[k,J]=t.useState(!1),[F,$]=t.useState(!1);t.useEffect(()=>{R()},[]);const R=async()=>{try{N(!0),V(null);const s=await ie();u(s)}catch(s){console.error("Error fetching users:",s),V("Failed to load users. Please try again.")}finally{N(!1)}},Y=s=>{P(s),w(!0)},ee=s=>{P(s),v(!0)},se=async()=>{if(a)try{C(!0),await je(a.uid),u(c.map(s=>s.uid===a.uid?{...s,role:S.Admin}:s)),j.success(`${a.displayName||a.email} has been promoted to admin`),w(!1)}catch(s){console.error("Error promoting user to admin:",s),j.error("Failed to promote user. Please try again.")}finally{C(!1)}},ae=async()=>{if(a)try{C(!0),await ge(a.uid),u(c.map(s=>s.uid===a.uid?{...s,role:S.User}:s)),j.success(`Admin role removed from ${a.displayName||a.email}`),v(!1)}catch(s){console.error("Error removing admin role:",s),j.error("Failed to remove admin role. Please try again.")}finally{C(!1)}},r=oe({resolver:ue(Oe),defaultValues:{email:"",password:"",displayName:"",phone:"",address:"",apartment:"",city:"",state:"",pincode:"",role:"user"}}),re=async s=>{try{q(!0);const i=await fe(s.email,s.password,{displayName:s.displayName,phone:s.phone,address:s.address,apartment:s.apartment,city:s.city,state:s.state,pincode:s.pincode,role:s.role});i.success?(j.success(i.message),A(!1),r.reset(),R()):j.error(i.message)}catch(i){console.error("Error creating user:",i),j.error("Failed to create user. Please try again.")}finally{q(!1)}},le=s=>{P(s),U(!0)},te=async()=>{if(a)try{J(!0);const s=await ye(a.uid);s.success?(u(c.filter(i=>i.uid!==a.uid)),j.success(s.message)):j.error(s.message),U(!1)}catch(s){console.error("Error deleting user:",s),j.error("Failed to delete user. Please try again.")}finally{J(!1)}};return e.jsxs(Pe,{title:"User Management",description:"Manage users and their permissions",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"User Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage users and their permissions"})]}),e.jsxs("div",{className:"flex gap-2 mt-4 md:mt-0",children:[e.jsxs(n,{variant:"default",onClick:()=>A(!0),className:"flex items-center",children:[e.jsx(ce,{className:"h-4 w-4 mr-2"}),"Create New User"]}),e.jsx(n,{variant:"outline",onClick:R,disabled:f,children:f?e.jsxs(e.Fragment,{children:[e.jsx(H,{className:"h-4 w-4 mr-2 animate-spin"}),"Loading..."]}):e.jsxs(e.Fragment,{children:[e.jsx(H,{className:"h-4 w-4 mr-2"}),"Refresh"]})})]})]}),b&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:e.jsx("p",{children:b})}),f?e.jsxs("div",{className:"flex justify-center items-center py-12",children:[e.jsx(y,{size:"lg"}),e.jsx("span",{className:"ml-2 text-gray-600",children:"Loading users..."})]}):c.length>0?e.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Email"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Role"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:c.map(s=>e.jsxs("tr",{children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 h-10 w-10",children:s.photoURL?e.jsx("img",{className:"h-10 w-10 rounded-full",src:s.photoURL,alt:""}):e.jsx("div",{className:"h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center",children:e.jsx(de,{className:"h-6 w-6 text-gray-500"})})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:s.displayName||"No Name"}),e.jsx("div",{className:"text-sm text-gray-500",children:s.phone||"No Phone"})]})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm text-gray-900",children:s.email})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s.role===S.Admin?e.jsx("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-burgundy-100 text-burgundy-800",children:"Admin"}):e.jsx("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800",children:"User"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsxs("div",{className:"flex gap-2",children:[s.role===S.Admin?e.jsxs(n,{variant:"outline",size:"sm",className:"flex items-center gap-1",onClick:()=>ee(s),children:[e.jsx(Ee,{className:"h-4 w-4"}),"Remove Admin"]}):e.jsxs(n,{variant:"outline",size:"sm",className:"flex items-center gap-1",onClick:()=>Y(s),children:[e.jsx(Ue,{className:"h-4 w-4"}),"Make Admin"]}),e.jsxs(n,{variant:"outline",size:"sm",className:"flex items-center gap-1 text-red-600 hover:text-red-800 hover:bg-red-50",onClick:()=>le(s),children:[e.jsx(ke,{className:"h-4 w-4"}),"Delete"]})]})})]},s.uid))})]})}):e.jsxs("div",{className:"bg-gray-50 rounded-lg p-8 text-center",children:[e.jsx("h2",{className:"text-xl font-medium text-gray-700 mb-2",children:"No users found"}),e.jsx("p",{className:"text-gray-500 mb-4",children:"There are no users in the system."})]}),e.jsx(E,{open:G,onOpenChange:w,children:e.jsxs(O,{children:[e.jsxs(I,{children:[e.jsx(z,{children:"Promote to Admin"}),e.jsxs(T,{children:["Are you sure you want to promote ",(a==null?void 0:a.displayName)||(a==null?void 0:a.email)," to admin? This will give them full access to the admin dashboard and all administrative functions."]})]}),e.jsxs(M,{children:[e.jsx(n,{variant:"outline",onClick:()=>w(!1),children:"Cancel"}),e.jsx(n,{variant:"default",onClick:se,disabled:D,children:D?e.jsxs(e.Fragment,{children:[e.jsx(y,{size:"sm",className:"mr-2"}),"Processing..."]}):"Confirm Promotion"})]})]})}),e.jsx(E,{open:K,onOpenChange:v,children:e.jsxs(O,{children:[e.jsxs(I,{children:[e.jsx(z,{children:"Remove Admin Role"}),e.jsxs(T,{children:["Are you sure you want to remove admin privileges from ",(a==null?void 0:a.displayName)||(a==null?void 0:a.email),"? They will no longer have access to the admin dashboard and administrative functions."]})]}),e.jsxs(M,{children:[e.jsx(n,{variant:"outline",onClick:()=>v(!1),children:"Cancel"}),e.jsx(n,{variant:"destructive",onClick:ae,disabled:D,children:D?e.jsxs(e.Fragment,{children:[e.jsx(y,{size:"sm",className:"mr-2"}),"Processing..."]}):"Confirm Removal"})]})]})}),e.jsx(Ne,{open:X,onOpenChange:U,children:e.jsxs(be,{children:[e.jsxs(we,{children:[e.jsxs(ve,{className:"text-red-600 flex items-center gap-2",children:[e.jsx(me,{className:"h-5 w-5"}),"Delete User Account"]}),e.jsxs(Ae,{children:["Are you sure you want to delete the account for ",e.jsx("strong",{children:(a==null?void 0:a.displayName)||(a==null?void 0:a.email)}),"?",e.jsx("br",{}),e.jsx("br",{}),"This action is permanent and cannot be undone. All user data will be removed from the system."]})]}),e.jsxs(De,{children:[e.jsx(Ce,{disabled:k,children:"Cancel"}),e.jsx(Se,{onClick:te,disabled:k,className:"bg-red-600 hover:bg-red-700 text-white",children:k?e.jsxs(e.Fragment,{children:[e.jsx(y,{size:"sm",className:"mr-2"}),"Deleting..."]}):"Delete User"})]})]})}),e.jsx(E,{open:Q,onOpenChange:s=>{A(s),s||(r.reset(),$(!1))},children:e.jsxs(O,{className:"sm:max-w-[600px] max-h-[90vh] overflow-y-auto",children:[e.jsxs(I,{children:[e.jsx(z,{children:"Create New User"}),e.jsx(T,{children:"Fill in the details to create a new user account. All fields marked with * are required."})]}),e.jsx(xe,{...r,children:e.jsxs("form",{onSubmit:r.handleSubmit(re),className:"space-y-4 py-2",children:[e.jsx(d,{control:r.control,name:"email",render:({field:s})=>e.jsxs(m,{children:[e.jsx(x,{children:"Email *"}),e.jsx(h,{children:e.jsx(g,{placeholder:"<EMAIL>",...s,disabled:l})}),e.jsx(p,{})]})}),e.jsx(d,{control:r.control,name:"password",render:({field:s})=>e.jsxs(m,{children:[e.jsx(x,{children:"Password *"}),e.jsxs("div",{className:"relative",children:[e.jsx(h,{children:e.jsx(g,{type:F?"text":"password",placeholder:"••••••••",...s,disabled:l})}),e.jsx(n,{type:"button",variant:"ghost",size:"icon",className:"absolute right-0 top-0 h-full px-3",onClick:()=>$(!F),children:F?e.jsx(Fe,{className:"h-4 w-4"}):e.jsx(Re,{className:"h-4 w-4"})})]}),e.jsx(he,{children:"Password must be at least 8 characters and include uppercase, lowercase, number, and special character."}),e.jsx(p,{})]})}),e.jsx(d,{control:r.control,name:"displayName",render:({field:s})=>e.jsxs(m,{children:[e.jsx(x,{children:"Display Name *"}),e.jsx(h,{children:e.jsx(g,{placeholder:"John Doe",...s,disabled:l})}),e.jsx(p,{})]})}),e.jsx(d,{control:r.control,name:"phone",render:({field:s})=>e.jsxs(m,{children:[e.jsx(x,{children:"Phone Number"}),e.jsx(h,{children:e.jsx(g,{placeholder:"9876543210",...s,disabled:l})}),e.jsx(p,{})]})}),e.jsx(d,{control:r.control,name:"role",render:({field:s})=>e.jsxs(m,{children:[e.jsx(x,{children:"User Role *"}),e.jsxs(B,{onValueChange:s.onChange,defaultValue:s.value,disabled:l,children:[e.jsx(h,{children:e.jsx(W,{children:e.jsx(Z,{placeholder:"Select a role"})})}),e.jsxs(_,{children:[e.jsx(L,{value:"user",children:"Regular User"}),e.jsx(L,{value:"admin",children:"Administrator"})]})]}),e.jsx(p,{})]})}),e.jsx(d,{control:r.control,name:"address",render:({field:s})=>e.jsxs(m,{children:[e.jsx(x,{children:"Address"}),e.jsx(h,{children:e.jsx(g,{placeholder:"123 Main St",...s,disabled:l})}),e.jsx(p,{})]})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsx(d,{control:r.control,name:"city",render:({field:s})=>e.jsxs(m,{children:[e.jsx(x,{children:"City"}),e.jsx(h,{children:e.jsx(g,{placeholder:"Mumbai",...s,disabled:l})}),e.jsx(p,{})]})}),e.jsx(d,{control:r.control,name:"state",render:({field:s})=>e.jsxs(m,{children:[e.jsx(x,{children:"State"}),e.jsxs(B,{onValueChange:s.onChange,value:s.value,disabled:l,children:[e.jsx(h,{children:e.jsx(W,{children:e.jsx(Z,{placeholder:"Select a state"})})}),e.jsx(_,{children:pe.map(i=>e.jsx(L,{value:i,children:i},i))})]}),e.jsx(p,{})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsx(d,{control:r.control,name:"pincode",render:({field:s})=>e.jsxs(m,{children:[e.jsx(x,{children:"Pincode"}),e.jsx(h,{children:e.jsx(g,{placeholder:"400001",...s,disabled:l})}),e.jsx(p,{})]})}),e.jsx(d,{control:r.control,name:"apartment",render:({field:s})=>e.jsxs(m,{children:[e.jsx(x,{children:"Apartment"}),e.jsx(h,{children:e.jsx(g,{placeholder:"Apartment name",...s,disabled:l})}),e.jsx(p,{})]})})]}),e.jsxs(M,{className:"pt-4",children:[e.jsx(n,{variant:"outline",type:"button",onClick:()=>A(!1),disabled:l,children:"Cancel"}),e.jsx(n,{type:"submit",disabled:l,children:l?e.jsxs(e.Fragment,{children:[e.jsx(y,{size:"sm",className:"mr-2"}),"Creating User..."]}):"Create User"})]})]})})]})})]})};export{Ze as default};
