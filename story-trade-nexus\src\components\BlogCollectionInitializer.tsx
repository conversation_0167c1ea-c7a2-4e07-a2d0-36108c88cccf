import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/lib/AuthContext';
import { 
  initializeBlogCollections, 
  verifyBlogCollectionAccess, 
  cleanupPlaceholders,
  BLOG_COLLECTIONS 
} from '@/lib/blogCollections';
import { toast } from 'sonner';
import { Database, CheckCircle, XCircle, AlertTriangle, Trash2, RefreshCw } from 'lucide-react';

interface CollectionStatus {
  name: string;
  initialized: boolean;
  accessible: boolean;
  error?: string;
}

const BlogCollectionInitializer: React.FC = () => {
  const { currentUser, isAdmin } = useAuth();
  const [isInitializing, setIsInitializing] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isCleaning, setIsCleaning] = useState(false);
  const [collectionStatus, setCollectionStatus] = useState<CollectionStatus[]>([]);
  const [lastCheck, setLastCheck] = useState<Date | null>(null);

  const initializeCollections = async () => {
    if (!currentUser || !isAdmin) {
      toast.error('You must be logged in as an admin to initialize collections');
      return;
    }

    setIsInitializing(true);
    try {
      console.log('🔧 Starting blog collections initialization...');

      const result = await initializeBlogCollections(currentUser.uid, currentUser.email || undefined);
      
      if (result.success) {
        toast.success('Blog collections initialized successfully!');
        console.log('✅ Collections initialization result:', result);
        
        // Update status after successful initialization
        await verifyCollections();
      } else {
        toast.error(`Failed to initialize collections: ${result.message}`);
        console.error('❌ Collections initialization failed:', result);
      }
    } catch (error) {
      console.error('❌ Error during initialization:', error);
      toast.error(`Initialization error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsInitializing(false);
    }
  };

  const verifyCollections = async () => {
    if (!currentUser || !isAdmin) {
      toast.error('You must be logged in as an admin to verify collections');
      return;
    }

    setIsVerifying(true);
    try {
      console.log('🔍 Verifying blog collections access...');
      
      const result = await verifyBlogCollectionAccess();
      
      // Update collection status
      const status: CollectionStatus[] = [
        {
          name: BLOG_COLLECTIONS.BLOGS,
          initialized: true,
          accessible: result.details?.blogsReadAccess || false,
          error: result.details?.blogsReadAccess ? undefined : 'Read access failed'
        },
        {
          name: BLOG_COLLECTIONS.BLOG_POSTS,
          initialized: true,
          accessible: result.details?.blogPostsReadAccess || false,
          error: result.details?.blogPostsReadAccess ? undefined : 'Read access failed'
        }
      ];
      
      setCollectionStatus(status);
      setLastCheck(new Date());
      
      if (result.success) {
        toast.success('All collections are accessible!');
      } else {
        toast.warning(`Some collections have access issues: ${result.message}`);
      }
      
      console.log('🔍 Verification result:', result);
    } catch (error) {
      console.error('❌ Error during verification:', error);
      toast.error(`Verification error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsVerifying(false);
    }
  };

  const cleanupPlaceholderDocs = async () => {
    if (!currentUser || !isAdmin) {
      toast.error('You must be logged in as an admin to cleanup placeholders');
      return;
    }

    setIsCleaning(true);
    try {
      console.log('🧹 Cleaning up placeholder documents...');
      
      const result = await cleanupPlaceholders();
      
      if (result.success) {
        toast.success('Placeholder documents cleaned up successfully!');
        console.log('✅ Cleanup result:', result);
        
        // Verify collections after cleanup
        await verifyCollections();
      } else {
        toast.error(`Cleanup failed: ${result.message}`);
        console.error('❌ Cleanup failed:', result);
      }
    } catch (error) {
      console.error('❌ Error during cleanup:', error);
      toast.error(`Cleanup error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsCleaning(false);
    }
  };

  const getStatusIcon = (status: CollectionStatus) => {
    if (status.accessible) {
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    } else {
      return <XCircle className="h-4 w-4 text-red-500" />;
    }
  };

  const getStatusBadge = (status: CollectionStatus) => {
    if (status.accessible) {
      return <Badge variant="default" className="bg-green-100 text-green-700">Accessible</Badge>;
    } else {
      return <Badge variant="destructive">Not Accessible</Badge>;
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5 text-blue-500" />
          Blog Collections Initializer
        </CardTitle>
        <CardDescription>
          Initialize and manage Firestore collections for the blog system. 
          This creates the necessary collections with proper structure and security rules.
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* User Status */}
        <div className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
          <div>
            <span className="text-sm font-medium">User:</span>
            <span className="ml-2 text-sm">{currentUser?.email || 'Not logged in'}</span>
          </div>
          <div>
            <span className="text-sm font-medium">Admin:</span>
            <Badge variant={isAdmin ? 'default' : 'destructive'} className="ml-2">
              {isAdmin ? 'Yes' : 'No'}
            </Badge>
          </div>
        </div>

        {/* Collection Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Collections Overview</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 border rounded-lg">
              <h4 className="font-medium text-blue-800 mb-2">📝 {BLOG_COLLECTIONS.BLOGS}</h4>
              <p className="text-sm text-gray-600 mb-2">
                Stores draft blog posts and admin content. Used for editing and autosave functionality.
              </p>
              <div className="text-xs text-gray-500">
                • Admin-only read/write access<br/>
                • Supports autosave and drafts<br/>
                • Contains author information
              </div>
            </div>
            
            <div className="p-4 border rounded-lg">
              <h4 className="font-medium text-green-800 mb-2">🌐 {BLOG_COLLECTIONS.BLOG_POSTS}</h4>
              <p className="text-sm text-gray-600 mb-2">
                Stores published, public-facing blog posts. Optimized for public consumption.
              </p>
              <div className="text-xs text-gray-500">
                • Public read access<br/>
                • Admin-only write access<br/>
                • SEO-optimized structure
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-3">
          <Button 
            onClick={initializeCollections}
            disabled={isInitializing || !isAdmin}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isInitializing ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Initializing...
              </>
            ) : (
              <>
                <Database className="h-4 w-4 mr-2" />
                Initialize Collections
              </>
            )}
          </Button>

          <Button 
            onClick={verifyCollections}
            disabled={isVerifying || !isAdmin}
            variant="outline"
          >
            {isVerifying ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Verifying...
              </>
            ) : (
              <>
                <CheckCircle className="h-4 w-4 mr-2" />
                Verify Access
              </>
            )}
          </Button>

          <Button 
            onClick={cleanupPlaceholderDocs}
            disabled={isCleaning || !isAdmin}
            variant="outline"
            className="border-orange-300 text-orange-600 hover:bg-orange-50"
          >
            {isCleaning ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Cleaning...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4 mr-2" />
                Cleanup Placeholders
              </>
            )}
          </Button>
        </div>

        {/* Collection Status */}
        {collectionStatus.length > 0 && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Collection Status</h3>
              {lastCheck && (
                <span className="text-sm text-gray-500">
                  Last checked: {lastCheck.toLocaleTimeString()}
                </span>
              )}
            </div>
            
            {collectionStatus.map((status, index) => (
              <div 
                key={index}
                className="flex items-center justify-between p-3 border rounded-lg"
              >
                <div className="flex items-center gap-3">
                  {getStatusIcon(status)}
                  <span className="font-medium">{status.name}</span>
                  {status.error && (
                    <span className="text-sm text-red-600">({status.error})</span>
                  )}
                </div>
                {getStatusBadge(status)}
              </div>
            ))}
          </div>
        )}

        {/* Instructions */}
        <div className="p-4 bg-blue-50 rounded-lg">
          <h4 className="font-medium text-blue-800 mb-2 flex items-center gap-2">
            <AlertTriangle className="h-4 w-4" />
            Setup Instructions
          </h4>
          <ol className="text-sm text-blue-600 space-y-1">
            <li>1. <strong>Initialize Collections:</strong> Creates the necessary Firestore collections if they don't exist</li>
            <li>2. <strong>Verify Access:</strong> Tests read/write permissions to ensure proper setup</li>
            <li>3. <strong>Cleanup Placeholders:</strong> Removes temporary documents used for initialization (optional)</li>
            <li>4. <strong>Security Rules:</strong> Ensure your Firestore security rules are deployed and up-to-date</li>
          </ol>
        </div>
      </CardContent>
    </Card>
  );
};

export default BlogCollectionInitializer;
