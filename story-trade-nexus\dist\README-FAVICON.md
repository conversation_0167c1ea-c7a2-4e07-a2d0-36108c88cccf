# PeerBooks Favicon Implementation

This document explains the favicon implementation for the PeerBooks application.

## Favicon Files

The following favicon files are included in the project:

- `favicon.svg` - SVG version of the favicon (vector-based, scalable)
- `favicon-32x32.png` - 32x32 pixel PNG version
- `favicon-16x16.png` - 16x16 pixel PNG version
- `favicon.ico` - ICO file (contains multiple sizes)

## Implementation Details

The favicon represents the PeerBooks brand with the following elements:

1. A burgundy circular background (#8B2635) representing the brand's primary color
2. A stack of books in beige/tan color (#F2D0A4)
3. Two silhouette figures representing peer-to-peer sharing
4. A connection line between the figures symbolizing the exchange

## HTML Implementation

The favicon is referenced in the HTML using the following tags:

```html
<!-- Favicon and Web App Manifest -->
<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
<link rel="shortcut icon" href="/favicon.ico" />
<link rel="manifest" href="/manifest.json" />
<meta name="theme-color" content="#8B2635" />
```

## Web App Manifest

The `manifest.json` file provides additional metadata for the favicon and web app:

```json
{
  "name": "PeerBooks",
  "short_name": "PeerBooks",
  "description": "Share the Joy of Reading - Rent, buy, or exchange used books directly with other readers in your community.",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#8B2635",
  "icons": [
    {
      "src": "favicon-16x16.png",
      "sizes": "16x16",
      "type": "image/png"
    },
    {
      "src": "favicon-32x32.png",
      "sizes": "32x32",
      "type": "image/png"
    },
    {
      "src": "favicon.svg",
      "sizes": "any",
      "type": "image/svg+xml",
      "purpose": "any"
    }
  ]
}
```

## Browser Compatibility

This implementation provides:

- Modern browsers: SVG favicon (best quality at any size)
- Legacy browsers: ICO and PNG fallbacks
- Mobile devices: Web app manifest for home screen icons

## Updating the Favicon

If you need to update the favicon:

1. Edit the `favicon.svg` file
2. Generate new PNG versions at 16x16 and 32x32 sizes
3. Create a new ICO file containing multiple sizes
4. Replace the existing files in the `public` directory
5. No HTML changes are needed unless you add new sizes or formats
