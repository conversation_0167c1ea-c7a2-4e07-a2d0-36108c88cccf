rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Existing book-related rules (preserving current functionality)
    match /books/{book} {
      allow read, write: if false; // Books collection remains read-only for now
    }
    
    // Published blog posts collection (public-facing content)
    match /blogPosts/{postId} {
      // Allow read access for all users (published content is public)
      allow read: if true;

      // Admin-only write access
      allow create, update, delete: if request.auth != null &&
        (request.auth.token.email == '<EMAIL>' ||
         (exists(/databases/$(database)/documents/admins/admins) &&
          request.auth.uid in get(/databases/$(database)/documents/admins/admins).data.uids));

      // Validate published blog post structure on create
      allow create: if request.auth != null &&
        (request.auth.token.email == '<EMAIL>' ||
         (exists(/databases/$(database)/documents/admins/admins) &&
          request.auth.uid in get(/databases/$(database)/documents/admins/admins).data.uids)) &&
        request.resource.data.keys().hasAll(['title', 'content', 'excerpt', 'author', 'tags', 'published', 'slug', 'readTime', 'createdAt', 'updatedAt']) &&
        request.resource.data.title is string &&
        request.resource.data.content is string &&
        request.resource.data.excerpt is string &&
        request.resource.data.author is string &&
        request.resource.data.tags is list &&
        request.resource.data.published == true &&
        request.resource.data.slug is string &&
        request.resource.data.readTime is number;

      // Allow update of specific fields for published posts
      allow update: if request.auth != null &&
        (request.auth.token.email == '<EMAIL>' ||
         (exists(/databases/$(database)/documents/admins/admins) &&
          request.auth.uid in get(/databases/$(database)/documents/admins/admins).data.uids)) &&
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['title', 'content', 'excerpt', 'author', 'tags', 'slug', 'readTime', 'coverImageUrl', 'updatedAt']);

      // Special rule for placeholder documents (used for collection initialization)
      allow create, delete: if request.auth != null &&
        (request.auth.token.email == '<EMAIL>' ||
         (exists(/databases/$(database)/documents/admins/admins) &&
          request.auth.uid in get(/databases/$(database)/documents/admins/admins).data.uids)) &&
        (postId == '_placeholder' || resource.data._placeholder == true);
    }

    // Blogs collection (draft blog posts and admin content)
    match /blogs/{blogId} {
      // Allow read access for authenticated users, with special case for admin email
      allow read: if request.auth != null &&
        (request.auth.token.email == '<EMAIL>' ||
         (exists(/databases/$(database)/documents/admins/admins) &&
          request.auth.uid in get(/databases/$(database)/documents/admins/admins).data.uids));

      // Admin-only write access
      allow create, update, delete: if request.auth != null &&
        (request.auth.token.email == '<EMAIL>' ||
         (exists(/databases/$(database)/documents/admins/admins) &&
          request.auth.uid in get(/databases/$(database)/documents/admins/admins).data.uids));

      // Validate blog draft structure on create
      allow create: if request.auth != null &&
        (request.auth.token.email == '<EMAIL>' ||
         (exists(/databases/$(database)/documents/admins/admins) &&
          request.auth.uid in get(/databases/$(database)/documents/admins/admins).data.uids)) &&
        request.resource.data.keys().hasAll(['title', 'content', 'tags', 'published', 'authorId', 'authorName', 'authorEmail', 'createdAt', 'updatedAt']) &&
        request.resource.data.title is string &&
        request.resource.data.content is string &&
        request.resource.data.tags is list &&
        request.resource.data.published is bool &&
        request.resource.data.authorId is string &&
        request.resource.data.authorName is string &&
        request.resource.data.authorEmail is string &&
        request.resource.data.authorId == request.auth.uid; // Ensure author matches authenticated user

      // Allow update of specific fields for drafts
      allow update: if request.auth != null &&
        (request.auth.token.email == '<EMAIL>' ||
         (exists(/databases/$(database)/documents/admins/admins) &&
          request.auth.uid in get(/databases/$(database)/documents/admins/admins).data.uids)) &&
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['title', 'content', 'tags', 'published', 'excerpt', 'slug', 'readTime', 'views', 'coverImageUrl', 'updatedAt']) &&
        resource.data.authorId == request.auth.uid; // Ensure user can only update their own drafts

      // Special rule for placeholder documents (used for collection initialization)
      allow create, delete: if request.auth != null &&
        (request.auth.token.email == '<EMAIL>' ||
         (exists(/databases/$(database)/documents/admins/admins) &&
          request.auth.uid in get(/databases/$(database)/documents/admins/admins).data.uids)) &&
        (blogId == '_placeholder' || resource.data._placeholder == true);
    }
    
    // Admins collection to store admin UIDs
    match /admins/{admin} {
      allow read: if request.auth != null;
      allow write: if false;
    }
  }
}
