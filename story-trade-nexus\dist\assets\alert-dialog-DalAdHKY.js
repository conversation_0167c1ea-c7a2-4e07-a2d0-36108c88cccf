import{ax as L,r as s,j as o,ay as u,az as G,aA as H,a8 as n,aB as A}from"./index-Bm_kDzMk.js";import{f as D,T as W,O as k,W as B,C as V,g as Y,h as q,i as v,R as J,P as K}from"./dialog-DGwTPBRT.js";var x="AlertDialog",[Q,De]=L(x,[D]),i=D(),N=e=>{const{__scopeAlertDialog:a,...t}=e,r=i(a);return o.jsx(J,{...r,...t,modal:!0})};N.displayName=x;var U="AlertDialogTrigger",X=s.forwardRef((e,a)=>{const{__scopeAlertDialog:t,...r}=e,l=i(t);return o.jsx(W,{...l,...r,ref:a})});X.displayName=U;var Z="AlertDialogPortal",y=e=>{const{__scopeAlertDialog:a,...t}=e,r=i(a);return o.jsx(K,{...r,...t})};y.displayName=Z;var ee="AlertDialogOverlay",R=s.forwardRef((e,a)=>{const{__scopeAlertDialog:t,...r}=e,l=i(t);return o.jsx(k,{...l,...r,ref:a})});R.displayName=ee;var c="AlertDialogContent",[ae,te]=Q(c),_=s.forwardRef((e,a)=>{const{__scopeAlertDialog:t,children:r,...l}=e,g=i(t),p=s.useRef(null),F=u(a,p),f=s.useRef(null);return o.jsx(B,{contentName:c,titleName:j,docsSlug:"alert-dialog",children:o.jsx(ae,{scope:t,cancelRef:f,children:o.jsxs(V,{role:"alertdialog",...g,...l,ref:F,onOpenAutoFocus:G(l.onOpenAutoFocus,d=>{var m;d.preventDefault(),(m=f.current)==null||m.focus({preventScroll:!0})}),onPointerDownOutside:d=>d.preventDefault(),onInteractOutside:d=>d.preventDefault(),children:[o.jsx(H,{children:r}),o.jsx(re,{contentRef:p})]})})})});_.displayName=c;var j="AlertDialogTitle",h=s.forwardRef((e,a)=>{const{__scopeAlertDialog:t,...r}=e,l=i(t);return o.jsx(Y,{...l,...r,ref:a})});h.displayName=j;var w="AlertDialogDescription",C=s.forwardRef((e,a)=>{const{__scopeAlertDialog:t,...r}=e,l=i(t);return o.jsx(q,{...l,...r,ref:a})});C.displayName=w;var oe="AlertDialogAction",E=s.forwardRef((e,a)=>{const{__scopeAlertDialog:t,...r}=e,l=i(t);return o.jsx(v,{...l,...r,ref:a})});E.displayName=oe;var b="AlertDialogCancel",P=s.forwardRef((e,a)=>{const{__scopeAlertDialog:t,...r}=e,{cancelRef:l}=te(b,t),g=i(t),p=u(a,l);return o.jsx(v,{...g,...r,ref:p})});P.displayName=b;var re=({contentRef:e})=>{const a=`\`${c}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${c}\` by passing a \`${w}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${c}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return s.useEffect(()=>{var r;document.getElementById((r=e.current)==null?void 0:r.getAttribute("aria-describedby"))||console.warn(a)},[a,e]),null},se=N,le=y,S=R,T=_,O=E,$=P,M=h,I=C;const ve=se,ie=le,z=s.forwardRef(({className:e,...a},t)=>o.jsx(S,{className:n("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...a,ref:t}));z.displayName=S.displayName;const ne=s.forwardRef(({className:e,...a},t)=>o.jsxs(ie,{children:[o.jsx(z,{}),o.jsx(T,{ref:t,className:n("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...a})]}));ne.displayName=T.displayName;const ce=({className:e,...a})=>o.jsx("div",{className:n("flex flex-col space-y-2 text-center sm:text-left",e),...a});ce.displayName="AlertDialogHeader";const de=({className:e,...a})=>o.jsx("div",{className:n("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...a});de.displayName="AlertDialogFooter";const pe=s.forwardRef(({className:e,...a},t)=>o.jsx(M,{ref:t,className:n("text-lg font-semibold",e),...a}));pe.displayName=M.displayName;const ge=s.forwardRef(({className:e,...a},t)=>o.jsx(I,{ref:t,className:n("text-sm text-muted-foreground",e),...a}));ge.displayName=I.displayName;const fe=s.forwardRef(({className:e,...a},t)=>o.jsx(O,{ref:t,className:n(A(),e),...a}));fe.displayName=O.displayName;const me=s.forwardRef(({className:e,...a},t)=>o.jsx($,{ref:t,className:n(A({variant:"outline"}),"mt-2 sm:mt-0",e),...a}));me.displayName=$.displayName;export{ve as A,ne as a,ce as b,pe as c,ge as d,de as e,me as f,fe as g};
