{"version": 3, "file": "uiNode.js", "names": ["ParentBlot", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "isMac", "test", "navigator", "platform", "TTL_FOR_VALID_SELECTION_CHANGE", "canMoveCaretBeforeUINode", "event", "key", "ctrl<PERSON>ey", "UINode", "isListening", "selectionChangeDeadline", "constructor", "quill", "options", "handleArrowKeys", "handleNavigationShortcuts", "keyboard", "addBinding", "offset", "shift<PERSON>ey", "handler", "range", "_ref", "line", "uiNode", "isRTL", "getComputedStyle", "domNode", "setSelection", "index", "length", "sources", "USER", "root", "addEventListener", "defaultPrevented", "ensureListeningToSelectionChange", "Date", "now", "listener", "handleSelectionChange", "document", "once", "selection", "getSelection", "getRangeAt", "collapsed", "startOffset", "scroll", "find", "startContainer", "newRange", "createRange", "setStartAfter", "setEndAfter", "removeAllRanges", "addRange"], "sources": ["../../src/modules/uiNode.ts"], "sourcesContent": ["import { ParentBlot } from 'parchment';\nimport Module from '../core/module.js';\nimport Quill from '../core/quill.js';\n\nconst isMac = /Mac/i.test(navigator.platform);\n\n// Export for testing\nexport const TTL_FOR_VALID_SELECTION_CHANGE = 100;\n\n// A loose check to determine if the shortcut can move the caret before a UI node:\n// <ANY_PARENT>[CARET]<div class=\"ql-ui\"></div>[CONTENT]</ANY_PARENT>\nconst canMoveCaretBeforeUINode = (event: KeyboardEvent) => {\n  if (\n    event.key === 'ArrowLeft' ||\n    event.key === 'ArrowRight' || // RTL scripts or moving from the end of the previous line\n    event.key === 'ArrowUp' ||\n    event.key === 'ArrowDown' ||\n    event.key === 'Home'\n  ) {\n    return true;\n  }\n\n  if (isMac && event.key === 'a' && event.ctrlKey === true) {\n    return true;\n  }\n\n  return false;\n};\n\nclass UINode extends Module {\n  isListening = false;\n  selectionChangeDeadline = 0;\n\n  constructor(quill: Quill, options: Record<string, never>) {\n    super(quill, options);\n\n    this.handleArrowKeys();\n    this.handleNavigationShortcuts();\n  }\n\n  private handleArrowKeys() {\n    this.quill.keyboard.addBinding({\n      key: ['ArrowLeft', 'ArrowRight'],\n      offset: 0,\n      shiftKey: null,\n      handler(range, { line, event }) {\n        if (!(line instanceof ParentBlot) || !line.uiNode) {\n          return true;\n        }\n\n        const isRTL = getComputedStyle(line.domNode)['direction'] === 'rtl';\n        if (\n          (isRTL && event.key !== 'ArrowRight') ||\n          (!isRTL && event.key !== 'ArrowLeft')\n        ) {\n          return true;\n        }\n\n        this.quill.setSelection(\n          range.index - 1,\n          range.length + (event.shiftKey ? 1 : 0),\n          Quill.sources.USER,\n        );\n        return false;\n      },\n    });\n  }\n\n  private handleNavigationShortcuts() {\n    this.quill.root.addEventListener('keydown', (event) => {\n      if (!event.defaultPrevented && canMoveCaretBeforeUINode(event)) {\n        this.ensureListeningToSelectionChange();\n      }\n    });\n  }\n\n  /**\n   * We only listen to the `selectionchange` event when\n   * there is an intention of moving the caret to the beginning using shortcuts.\n   * This is primarily implemented to prevent infinite loops, as we are changing\n   * the selection within the handler of a `selectionchange` event.\n   */\n  private ensureListeningToSelectionChange() {\n    this.selectionChangeDeadline = Date.now() + TTL_FOR_VALID_SELECTION_CHANGE;\n\n    if (this.isListening) return;\n    this.isListening = true;\n\n    const listener = () => {\n      this.isListening = false;\n\n      if (Date.now() <= this.selectionChangeDeadline) {\n        this.handleSelectionChange();\n      }\n    };\n\n    document.addEventListener('selectionchange', listener, {\n      once: true,\n    });\n  }\n\n  private handleSelectionChange() {\n    const selection = document.getSelection();\n    if (!selection) return;\n    const range = selection.getRangeAt(0);\n    if (range.collapsed !== true || range.startOffset !== 0) return;\n\n    const line = this.quill.scroll.find(range.startContainer);\n    if (!(line instanceof ParentBlot) || !line.uiNode) return;\n\n    const newRange = document.createRange();\n    newRange.setStartAfter(line.uiNode);\n    newRange.setEndAfter(line.uiNode);\n    selection.removeAllRanges();\n    selection.addRange(newRange);\n  }\n}\n\nexport default UINode;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,WAAW;AACtC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,KAAK,MAAM,kBAAkB;AAEpC,MAAMC,KAAK,GAAG,MAAM,CAACC,IAAI,CAACC,SAAS,CAACC,QAAQ,CAAC;;AAE7C;AACA,OAAO,MAAMC,8BAA8B,GAAG,GAAG;;AAEjD;AACA;AACA,MAAMC,wBAAwB,GAAIC,KAAoB,IAAK;EACzD,IACEA,KAAK,CAACC,GAAG,KAAK,WAAW,IACzBD,KAAK,CAACC,GAAG,KAAK,YAAY;EAAI;EAC9BD,KAAK,CAACC,GAAG,KAAK,SAAS,IACvBD,KAAK,CAACC,GAAG,KAAK,WAAW,IACzBD,KAAK,CAACC,GAAG,KAAK,MAAM,EACpB;IACA,OAAO,IAAI;EACb;EAEA,IAAIP,KAAK,IAAIM,KAAK,CAACC,GAAG,KAAK,GAAG,IAAID,KAAK,CAACE,OAAO,KAAK,IAAI,EAAE;IACxD,OAAO,IAAI;EACb;EAEA,OAAO,KAAK;AACd,CAAC;AAED,MAAMC,MAAM,SAASX,MAAM,CAAC;EAC1BY,WAAW,GAAG,KAAK;EACnBC,uBAAuB,GAAG,CAAC;EAE3BC,WAAWA,CAACC,KAAY,EAAEC,OAA8B,EAAE;IACxD,KAAK,CAACD,KAAK,EAAEC,OAAO,CAAC;IAErB,IAAI,CAACC,eAAe,CAAC,CAAC;IACtB,IAAI,CAACC,yBAAyB,CAAC,CAAC;EAClC;EAEQD,eAAeA,CAAA,EAAG;IACxB,IAAI,CAACF,KAAK,CAACI,QAAQ,CAACC,UAAU,CAAC;MAC7BX,GAAG,EAAE,CAAC,WAAW,EAAE,YAAY,CAAC;MAChCY,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE,IAAI;MACdC,OAAOA,CAACC,KAAK,EAAAC,IAAA,EAAmB;QAAA,IAAjB;UAAEC,IAAI;UAAElB;QAAM,CAAC,GAAAiB,IAAA;QAC5B,IAAI,EAAEC,IAAI,YAAY3B,UAAU,CAAC,IAAI,CAAC2B,IAAI,CAACC,MAAM,EAAE;UACjD,OAAO,IAAI;QACb;QAEA,MAAMC,KAAK,GAAGC,gBAAgB,CAACH,IAAI,CAACI,OAAO,CAAC,CAAC,WAAW,CAAC,KAAK,KAAK;QACnE,IACGF,KAAK,IAAIpB,KAAK,CAACC,GAAG,KAAK,YAAY,IACnC,CAACmB,KAAK,IAAIpB,KAAK,CAACC,GAAG,KAAK,WAAY,EACrC;UACA,OAAO,IAAI;QACb;QAEA,IAAI,CAACM,KAAK,CAACgB,YAAY,CACrBP,KAAK,CAACQ,KAAK,GAAG,CAAC,EACfR,KAAK,CAACS,MAAM,IAAIzB,KAAK,CAACc,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,EACvCrB,KAAK,CAACiC,OAAO,CAACC,IAChB,CAAC;QACD,OAAO,KAAK;MACd;IACF,CAAC,CAAC;EACJ;EAEQjB,yBAAyBA,CAAA,EAAG;IAClC,IAAI,CAACH,KAAK,CAACqB,IAAI,CAACC,gBAAgB,CAAC,SAAS,EAAG7B,KAAK,IAAK;MACrD,IAAI,CAACA,KAAK,CAAC8B,gBAAgB,IAAI/B,wBAAwB,CAACC,KAAK,CAAC,EAAE;QAC9D,IAAI,CAAC+B,gCAAgC,CAAC,CAAC;MACzC;IACF,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;AACA;EACUA,gCAAgCA,CAAA,EAAG;IACzC,IAAI,CAAC1B,uBAAuB,GAAG2B,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGnC,8BAA8B;IAE1E,IAAI,IAAI,CAACM,WAAW,EAAE;IACtB,IAAI,CAACA,WAAW,GAAG,IAAI;IAEvB,MAAM8B,QAAQ,GAAGA,CAAA,KAAM;MACrB,IAAI,CAAC9B,WAAW,GAAG,KAAK;MAExB,IAAI4B,IAAI,CAACC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC5B,uBAAuB,EAAE;QAC9C,IAAI,CAAC8B,qBAAqB,CAAC,CAAC;MAC9B;IACF,CAAC;IAEDC,QAAQ,CAACP,gBAAgB,CAAC,iBAAiB,EAAEK,QAAQ,EAAE;MACrDG,IAAI,EAAE;IACR,CAAC,CAAC;EACJ;EAEQF,qBAAqBA,CAAA,EAAG;IAC9B,MAAMG,SAAS,GAAGF,QAAQ,CAACG,YAAY,CAAC,CAAC;IACzC,IAAI,CAACD,SAAS,EAAE;IAChB,MAAMtB,KAAK,GAAGsB,SAAS,CAACE,UAAU,CAAC,CAAC,CAAC;IACrC,IAAIxB,KAAK,CAACyB,SAAS,KAAK,IAAI,IAAIzB,KAAK,CAAC0B,WAAW,KAAK,CAAC,EAAE;IAEzD,MAAMxB,IAAI,GAAG,IAAI,CAACX,KAAK,CAACoC,MAAM,CAACC,IAAI,CAAC5B,KAAK,CAAC6B,cAAc,CAAC;IACzD,IAAI,EAAE3B,IAAI,YAAY3B,UAAU,CAAC,IAAI,CAAC2B,IAAI,CAACC,MAAM,EAAE;IAEnD,MAAM2B,QAAQ,GAAGV,QAAQ,CAACW,WAAW,CAAC,CAAC;IACvCD,QAAQ,CAACE,aAAa,CAAC9B,IAAI,CAACC,MAAM,CAAC;IACnC2B,QAAQ,CAACG,WAAW,CAAC/B,IAAI,CAACC,MAAM,CAAC;IACjCmB,SAAS,CAACY,eAAe,CAAC,CAAC;IAC3BZ,SAAS,CAACa,QAAQ,CAACL,QAAQ,CAAC;EAC9B;AACF;AAEA,eAAe3C,MAAM", "ignoreList": []}